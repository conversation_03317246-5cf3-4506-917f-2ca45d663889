<?php
/**
 * Fix profile photo display issues
 */

echo "=== FIXING PROFILE PHOTO ISSUES ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Check storage paths
    $storagePath = storage_path('app/public/avatars');
    $publicPath = public_path('storage/avatars');
    
    echo "📁 Storage path: $storagePath\n";
    echo "📁 Public path: $publicPath\n\n";
    
    // Get all users with avatars
    $users = \App\Models\User::whereNotNull('avatar')->where('avatar', '!=', '')->get();
    
    echo "🔍 Found " . $users->count() . " users with avatars:\n\n";
    
    $synced = 0;
    $errors = 0;
    
    foreach ($users as $user) {
        echo "User: {$user->name} (ID: {$user->id})\n";
        echo "Avatar path: {$user->avatar}\n";
        
        $storageFile = storage_path('app/public/' . $user->avatar);
        $publicFile = public_path('storage/' . $user->avatar);
        
        echo "Storage file exists: " . (file_exists($storageFile) ? "✅" : "❌") . "\n";
        echo "Public file exists: " . (file_exists($publicFile) ? "✅" : "❌") . "\n";
        
        // Copy missing files from storage to public
        if (file_exists($storageFile) && !file_exists($publicFile)) {
            $publicDir = dirname($publicFile);
            if (!is_dir($publicDir)) {
                mkdir($publicDir, 0755, true);
                echo "📁 Created directory: $publicDir\n";
            }
            
            if (copy($storageFile, $publicFile)) {
                echo "✅ Copied avatar to public storage\n";
                $synced++;
            } else {
                echo "❌ Failed to copy avatar\n";
                $errors++;
            }
        } elseif (file_exists($storageFile) && file_exists($publicFile)) {
            echo "✅ Already synced\n";
            $synced++;
        } else {
            echo "❌ Source file not found: $storageFile\n";
            $errors++;
        }
        
        // Test web URL
        $webUrl = asset('storage/' . $user->avatar);
        echo "Web URL: $webUrl\n";
        echo "---\n\n";
    }
    
    echo "=== SYNC SUMMARY ===\n";
    echo "✅ Successfully synced: $synced\n";
    echo "❌ Errors: $errors\n";
    echo "📊 Total processed: " . $users->count() . "\n\n";
    
    // Create test HTML for profile photos
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Profile Photo Test</title>
    <style>
        .avatar { width: 100px; height: 100px; border: 2px solid #ccc; border-radius: 50%; object-fit: cover; margin: 10px; }
        .error { color: red; }
        .success { color: green; }
        .user-card { border: 1px solid #ddd; padding: 15px; margin: 10px; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>Profile Photo Test</h1>";
    
    foreach ($users as $user) {
        $webUrl = asset('storage/' . $user->avatar);
        $testHtml .= "
    <div class='user-card'>
        <h3>User: {$user->name}</h3>
        <p>Avatar path: {$user->avatar}</p>
        <p>Web URL: $webUrl</p>
        <img src=\"$webUrl\" alt=\"{$user->name}\" class=\"avatar\" 
             onload=\"this.nextElementSibling.innerHTML='<span class=success>✅ Image loaded successfully!</span>'\"
             onerror=\"this.nextElementSibling.innerHTML='<span class=error>❌ Image failed to load!</span>'\">
        <p>Loading...</p>
        <p><a href=\"$webUrl\" target=\"_blank\">Test direct access</a></p>
    </div>";
    }
    
    $testHtml .= "
</body>
</html>";
    
    file_put_contents(public_path('profile-photo-test.html'), $testHtml);
    echo "=== TEST FILE CREATED ===\n";
    echo "🔗 Open: http://127.0.0.1:8000/profile-photo-test.html\n\n";
    
    if ($errors === 0) {
        echo "🎉 All profile photos are now properly synced!\n";
        echo "🔗 Test profile page: http://127.0.0.1:8000/profile\n";
    } else {
        echo "⚠️ Some photos had issues. Please check the errors above.\n";
    }
    
    echo "\n=== FIX COMPLETED ===\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
