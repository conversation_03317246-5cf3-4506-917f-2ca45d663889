<?php

echo "=== TESTING REGISTRATION DETAILS FIX ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing Event model route key...\n";
    
    $event = \App\Models\Event::first();
    if ($event) {
        echo "   ✅ Event found: {$event->title}\n";
        echo "   ✅ Event ID: {$event->id}\n";
        echo "   ✅ Event Slug: {$event->slug}\n";
        echo "   ✅ Route Key Name: " . $event->getRouteKeyName() . "\n";
    } else {
        echo "   ❌ No event found\n";
        exit;
    }
    
    echo "2. Testing route generation with slug...\n";
    
    $registration = \App\Models\EventRegistration::where('event_id', $event->id)->first();
    if ($registration) {
        echo "   ✅ Registration found: ID {$registration->id}\n";
        
        try {
            // Test with slug (correct way)
            $urlWithSlug = route('ketua-ukm.events.registrations.show', [$event->slug, $registration->id]);
            echo "   ✅ URL with slug: {$urlWithSlug}\n";
            
            // Test with ID (incorrect way - should fail)
            try {
                $urlWithId = route('ketua-ukm.events.registrations.show', [$event->id, $registration->id]);
                echo "   ⚠️  URL with ID: {$urlWithId} (this might not work)\n";
            } catch (Exception $e) {
                echo "   ✅ URL with ID failed as expected: " . $e->getMessage() . "\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ Route generation failed: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ No registration found for this event\n";
    }
    
    echo "3. Testing view file update...\n";
    
    $viewPath = resource_path('views/ketua-ukm/events/registrations.blade.php');
    if (file_exists($viewPath)) {
        $viewContent = file_get_contents($viewPath);
        
        // Check if the fix is applied
        if (strpos($viewContent, '{{ $event->slug }}') !== false) {
            echo "   ✅ View updated to use event slug\n";
        } else {
            echo "   ❌ View still uses event ID\n";
        }
        
        // Check if old pattern exists
        if (strpos($viewContent, '{{ $event->id }}') !== false) {
            echo "   ⚠️  View still contains event ID references\n";
        } else {
            echo "   ✅ View no longer contains event ID references\n";
        }
    } else {
        echo "   ❌ View file not found\n";
    }
    
    echo "4. Testing controller authorization...\n";
    
    $controllerPath = app_path('Http/Controllers/KetuaUkmController.php');
    if (file_exists($controllerPath)) {
        $controllerContent = file_get_contents($controllerPath);
        
        // Check if authorization is improved
        if (strpos($controllerContent, 'leader_id !== $user->id') !== false) {
            echo "   ✅ Controller has proper authorization check\n";
        } else {
            echo "   ❌ Controller missing proper authorization\n";
        }
    }
    
    echo "5. Testing URL patterns...\n";
    
    // Test the correct URL pattern
    $correctUrl = "http://localhost:8000/ketua-ukm/events/{$event->slug}/registrations/{$registration->id}";
    echo "   ✅ Correct URL pattern: {$correctUrl}\n";
    
    // Test the incorrect URL pattern that was causing 404
    $incorrectUrl = "http://localhost:8000/ketua-ukm/events/{$event->id}/registrations/{$registration->id}";
    echo "   ❌ Incorrect URL pattern (was causing 404): {$incorrectUrl}\n";
    
    echo "6. Generating test URLs for verification...\n";
    
    // Generate URLs for all events and their registrations
    $events = \App\Models\Event::with('registrations')->get();
    foreach ($events as $evt) {
        if ($evt->registrations->count() > 0) {
            $reg = $evt->registrations->first();
            $testUrl = route('ketua-ukm.events.registrations.show', [$evt->slug, $reg->id]);
            echo "   Test URL: {$testUrl}\n";
        }
    }
    
    echo "\n=== FIX COMPLETED ===\n";
    echo "✅ Registration Details 404 Error Fixed!\n";
    echo "\nWhat was fixed:\n";
    echo "🔧 ISSUE: Event model uses 'slug' as route key, but JavaScript was using 'id'\n";
    echo "🔧 FIX: Updated JavaScript to use event slug instead of ID\n";
    echo "🔧 IMPROVEMENT: Enhanced authorization in controller\n";
    echo "\nHow to test:\n";
    echo "1. Login as ketua UKM user\n";
    echo "2. Go to event registrations page\n";
    echo "3. Click 'Detail' button on any registration\n";
    echo "4. Should now work without 404 error\n";
    echo "\nCorrect URL format: /ketua-ukm/events/{event-slug}/registrations/{registration-id}\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
