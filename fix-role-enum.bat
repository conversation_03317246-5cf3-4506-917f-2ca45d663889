@echo off
echo ========================================
echo   FIXING ROLE ENUM FOR KETUA UKM
echo ========================================
echo.

echo [1/3] Updating database enum...
mysql -u root -p ukm_web_db < update-role-enum.sql

echo.
echo [2/3] Clearing Laravel cache...
php artisan config:clear
php artisan cache:clear
php artisan view:clear

echo.
echo [3/3] Testing role assignment...
php -r "
require_once 'vendor/autoload.php';
\$app = require_once 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo 'Testing role assignment...' . PHP_EOL;
\$user = \App\Models\User::where('role', 'student')->first();
if (\$user) {
    echo 'Found test user: ' . \$user->name . PHP_EOL;
    \$user->update(['role' => 'ketua_ukm']);
    \$user->refresh();
    echo 'Updated role to: ' . \$user->role . PHP_EOL;
    
    if (\$user->role === 'ketua_ukm') {
        echo '✅ SUCCESS: Role assignment works!' . PHP_EOL;
        \$user->update(['role' => 'student']);
        echo 'Reverted back to student' . PHP_EOL;
    } else {
        echo '❌ FAILED: Role assignment failed' . PHP_EOL;
    }
} else {
    echo 'No test user found' . PHP_EOL;
}
"

echo.
echo ========================================
echo   ROLE ENUM FIX COMPLETED!
echo ========================================
echo.
echo Now you can assign 'ketua_ukm' role to users.
echo The role changes should persist after refresh.
echo.
pause
