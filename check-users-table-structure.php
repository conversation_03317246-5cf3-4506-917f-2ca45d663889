<?php

echo "=== CHECKING USERS TABLE STRUCTURE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected\n\n";
    
    // 1. Check users table structure
    echo "1. 📋 USERS TABLE STRUCTURE:\n";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "   {$column['Field']}: {$column['Type']} | Default: {$column['Default']} | Null: {$column['Null']}\n";
        
        // Check status column specifically
        if ($column['Field'] === 'status') {
            echo "   >>> STATUS COLUMN DETAILS:\n";
            echo "       Type: {$column['Type']}\n";
            echo "       Default: {$column['Default']}\n";
            echo "       Null allowed: {$column['Null']}\n";
            
            // Extract ENUM values
            if (strpos($column['Type'], 'enum') !== false) {
                preg_match("/^enum\((.+)\)$/", $column['Type'], $matches);
                if (isset($matches[1])) {
                    $enumValues = str_replace("'", "", $matches[1]);
                    echo "       Allowed values: $enumValues\n";
                }
            }
        }
    }
    
    // 2. Check current status values in use
    echo "\n2. 📊 CURRENT STATUS VALUES IN DATABASE:\n";
    $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM users GROUP BY status");
    $statusCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($statusCounts as $status) {
        echo "   {$status['status']}: {$status['count']} users\n";
    }
    
    // 3. Test inserting 'pending' status
    echo "\n3. 🧪 TESTING 'PENDING' STATUS INSERT:\n";
    
    try {
        // Try to insert a test record with pending status
        $stmt = $pdo->prepare("
            INSERT INTO users (nim, name, email, password, phone, gender, faculty, major, batch, role, status, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $testData = [
            'TEST001',
            'Test User',
            '<EMAIL>',
            password_hash('password', PASSWORD_DEFAULT),
            '081234567890',
            'male',
            'Test Faculty',
            'Test Major',
            '2024',
            'student',
            'pending'
        ];
        
        $stmt->execute($testData);
        echo "   ✅ Successfully inserted user with 'pending' status\n";
        
        // Clean up test record
        $pdo->prepare("DELETE FROM users WHERE email = '<EMAIL>'")->execute();
        echo "   ✅ Test record cleaned up\n";
        
    } catch (Exception $e) {
        echo "   ❌ Failed to insert 'pending' status: " . $e->getMessage() . "\n";
        
        // Check what status values are actually allowed
        echo "\n   🔍 CHECKING ALLOWED STATUS VALUES:\n";
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'status'");
        $statusColumn = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "   Status column type: " . $statusColumn['Type'] . "\n";
    }
    
    // 4. Check registration controller
    echo "\n4. 📝 REGISTRATION ISSUE ANALYSIS:\n";
    echo "   The error suggests 'pending' is not allowed in status ENUM\n";
    echo "   Common status values should be: active, pending, suspended\n";
    echo "   Current registration might be trying to set status to 'pending'\n";
    
    // 5. Suggest fix
    echo "\n5. 🔧 SUGGESTED FIXES:\n";
    echo "   Option 1: Update status ENUM to include 'pending'\n";
    echo "   Option 2: Change registration to use 'active' status\n";
    echo "   Option 3: Add migration to fix status column\n";
    
    echo "\n✅ ANALYSIS COMPLETE!\n";
    
} catch (PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
}
