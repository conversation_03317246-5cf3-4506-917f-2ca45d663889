<?php
/**
 * Fix UKM logo display issues in admin views
 */

echo "=== FIXING UKM LOGO DISPLAY ISSUES ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Check storage paths
    $storagePath = storage_path('app/public/ukms/logos');
    $publicPath = public_path('storage/ukms/logos');
    
    echo "📁 Storage path: $storagePath\n";
    echo "📁 Public path: $publicPath\n\n";
    
    // Get all UKMs with logos
    $ukms = \App\Models\Ukm::whereNotNull('logo')->where('logo', '!=', '')->get();
    
    echo "🔍 Found " . $ukms->count() . " UKMs with logos:\n\n";
    
    foreach ($ukms as $ukm) {
        echo "UKM: {$ukm->name}\n";
        echo "Logo path: {$ukm->logo}\n";
        
        $storageFile = storage_path('app/public/' . $ukm->logo);
        $publicFile = public_path('storage/' . $ukm->logo);
        
        echo "Storage file exists: " . (file_exists($storageFile) ? "✅" : "❌") . "\n";
        echo "Public file exists: " . (file_exists($publicFile) ? "✅" : "❌") . "\n";
        
        // Copy missing files from storage to public
        if (file_exists($storageFile) && !file_exists($publicFile)) {
            $publicDir = dirname($publicFile);
            if (!is_dir($publicDir)) {
                mkdir($publicDir, 0755, true);
                echo "📁 Created directory: $publicDir\n";
            }
            
            if (copy($storageFile, $publicFile)) {
                echo "✅ Copied file to public storage\n";
            } else {
                echo "❌ Failed to copy file\n";
            }
        }
        
        // Test web URL
        $webUrl = asset('storage/' . $ukm->logo);
        echo "Web URL: $webUrl\n";
        echo "---\n\n";
    }
    
    echo "=== VERIFICATION ===\n";
    echo "🔗 Test admin view: http://127.0.0.1:8000/admin/ukms/imma\n";
    echo "🔗 Test admin edit: http://127.0.0.1:8000/admin/ukms/imma/edit\n\n";
    
    echo "=== FIX COMPLETED ===\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
