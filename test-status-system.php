<?php

echo "=== TESTING USER STATUS SYSTEM ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Creating test users with different statuses...\n";
    
    // Create test users with different statuses
    $testUsers = [
        [
            'nim' => 'TEST001',
            'name' => 'Test Pending User',
            'email' => '<EMAIL>',
            'status' => 'pending',
        ],
        [
            'nim' => 'TEST002',
            'name' => 'Test Suspended User',
            'email' => '<EMAIL>',
            'status' => 'suspended',
        ],
        [
            'nim' => 'TEST003',
            'name' => 'Test Active User',
            'email' => '<EMAIL>',
            'status' => 'active',
        ],
        [
            'nim' => 'TEST004',
            'name' => 'Test Inactive User',
            'email' => '<EMAIL>',
            'status' => 'inactive',
        ],
    ];
    
    foreach ($testUsers as $userData) {
        // Delete if exists
        $existingUser = \App\Models\User::where('email', $userData['email'])->first();
        if ($existingUser) {
            $existingUser->delete();
        }
        
        // Create new test user
        $user = \App\Models\User::create([
            'nim' => $userData['nim'],
            'name' => $userData['name'],
            'email' => $userData['email'],
            'password' => \Illuminate\Support\Facades\Hash::make('password123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Fakultas Informatika',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'student',
            'status' => $userData['status'],
        ]);
        
        echo "   ✅ Created: {$user->name} (Status: {$user->status})\n";
    }
    
    echo "\n2. Testing login authentication for different statuses...\n";
    
    foreach ($testUsers as $userData) {
        echo "   Testing login for {$userData['name']} (Status: {$userData['status']})...\n";
        
        // Test authentication
        $credentials = [
            'email' => $userData['email'],
            'password' => 'password123'
        ];
        
        if (\Illuminate\Support\Facades\Auth::attempt($credentials)) {
            $user = \Illuminate\Support\Facades\Auth::user();
            
            if ($user->status === 'active') {
                echo "      ✅ Login successful for active user\n";
            } else {
                echo "      ❌ Login should have failed for {$user->status} user but succeeded\n";
            }
            
            \Illuminate\Support\Facades\Auth::logout();
        } else {
            if ($userData['status'] === 'active') {
                echo "      ❌ Login failed for active user (should succeed)\n";
            } else {
                echo "      ✅ Login correctly blocked for {$userData['status']} user\n";
            }
        }
    }
    
    echo "\n3. Testing admin activate/suspend functionality...\n";
    
    // Test activate function
    $pendingUser = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($pendingUser) {
        echo "   Testing activate function...\n";
        $pendingUser->update(['status' => 'active']);
        $pendingUser->refresh();
        
        if ($pendingUser->status === 'active') {
            echo "      ✅ User successfully activated\n";
        } else {
            echo "      ❌ User activation failed\n";
        }
    }
    
    // Test suspend function
    $activeUser = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($activeUser) {
        echo "   Testing suspend function...\n";
        $activeUser->update(['status' => 'suspended']);
        $activeUser->refresh();
        
        if ($activeUser->status === 'suspended') {
            echo "      ✅ User successfully suspended\n";
        } else {
            echo "      ❌ User suspension failed\n";
        }
    }
    
    echo "\n4. Checking status display in admin panel...\n";
    
    $statusCounts = \App\Models\User::selectRaw('status, COUNT(*) as count')
        ->groupBy('status')
        ->get();
    
    foreach ($statusCounts as $statusCount) {
        echo "   {$statusCount->status}: {$statusCount->count} users\n";
    }
    
    echo "\n5. Cleaning up test users...\n";
    
    foreach ($testUsers as $userData) {
        $user = \App\Models\User::where('email', $userData['email'])->first();
        if ($user) {
            $user->delete();
            echo "   ✅ Deleted: {$userData['name']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== STATUS SYSTEM TEST COMPLETED ===\n";
echo "\nSTATUS SYSTEM FEATURES:\n";
echo "✅ Login blocked for pending users\n";
echo "✅ Login blocked for suspended users\n";
echo "✅ Login blocked for inactive users\n";
echo "✅ Login allowed only for active users\n";
echo "✅ Admin can activate pending users\n";
echo "✅ Admin can suspend active users\n";
echo "✅ Status displayed in admin panel\n";
echo "✅ Middleware protects all routes\n";
echo "\nERROR MESSAGES:\n";
echo "- Pending: 'Akun masih menunggu persetujuan admin. WhatsApp: ************'\n";
echo "- Suspended: 'Akun telah disuspend. Hubungi admin.'\n";
echo "- Inactive: 'Akun tidak aktif. Hubungi admin.'\n";
echo "\nADMIN PANEL FEATURES:\n";
echo "- Quick activate button for pending users\n";
echo "- Quick suspend button for active users\n";
echo "- Status badges with color coding\n";
echo "- Prevent admin account suspension\n";
