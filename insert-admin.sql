-- Insert admin user directly
-- Password hash for 'admin123'
INSERT INTO users (
    nim, 
    name, 
    email, 
    password, 
    phone, 
    gender, 
    faculty, 
    major, 
    batch, 
    role, 
    status, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    'ADMIN001',
    'Administrator',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '081234567890',
    'male',
    'Administrasi',
    'Sistem Informasi',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- Verify the user was created
SELECT id, nim, name, email, role, status, created_at 
FROM users 
WHERE email = '<EMAIL>';
