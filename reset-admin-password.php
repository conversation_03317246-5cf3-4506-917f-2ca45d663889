<?php
require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "=== RESET ADMIN PASSWORD ===\n\n";

try {
    // Reset password untuk admin utama
    $admin = User::where('email', '<EMAIL>')->first();
    
    if ($admin) {
        $newPassword = 'admin123';
        $admin->password = Hash::make($newPassword);
        $admin->save();
        
        echo "✅ Password reset successful!\n\n";
        echo "📧 Email: <EMAIL>\n";
        echo "🔑 Password: admin123\n";
        echo "🌐 Login URL: http://localhost:8000/login\n\n";
        
        echo "You can now login with these credentials.\n";
    } else {
        echo "❌ Admin user not found!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== END ===\n";
?>
