<?php

echo "=== TESTING ROUTE DIRECTLY ===\n";

// Clear all caches first
echo "Clearing caches...\n";
exec('php artisan route:clear 2>&1', $output1);
exec('php artisan config:clear 2>&1', $output2);
exec('php artisan cache:clear 2>&1', $output3);
exec('php artisan view:clear 2>&1', $output4);

echo "Cache clearing results:\n";
echo "Route clear: " . implode("\n", $output1) . "\n";
echo "Config clear: " . implode("\n", $output2) . "\n";
echo "Cache clear: " . implode("\n", $output3) . "\n";
echo "View clear: " . implode("\n", $output4) . "\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "\n1. Checking if route exists in routes collection...\n";
    
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    $registerSuccessRoute = $routes->getByName('register.success');
    
    if ($registerSuccessRoute) {
        echo "   ✅ Route 'register.success' found!\n";
        echo "   URI: /{$registerSuccessRoute->uri()}\n";
        echo "   Methods: " . implode(', ', $registerSuccessRoute->methods()) . "\n";
        echo "   Action: {$registerSuccessRoute->getActionName()}\n";
    } else {
        echo "   ❌ Route 'register.success' NOT FOUND\n";
        
        // List all register routes
        echo "   Available register routes:\n";
        foreach ($routes->getRoutes() as $route) {
            if (str_contains($route->uri(), 'register')) {
                $name = $route->getName() ?: 'unnamed';
                echo "      - {$name}: {$route->uri()}\n";
            }
        }
    }
    
    echo "\n2. Testing route generation...\n";
    
    try {
        $url = route('register.success');
        echo "   ✅ Route URL generated: {$url}\n";
    } catch (Exception $e) {
        echo "   ❌ Route generation failed: " . $e->getMessage() . "\n";
        
        // Try to manually register the route
        echo "   Attempting manual route registration...\n";
        
        \Illuminate\Support\Facades\Route::get('/register/success', [
            \App\Http\Controllers\Auth\RegisteredUserController::class, 
            'success'
        ])->name('register.success');
        
        try {
            $url = route('register.success');
            echo "   ✅ Manual registration successful: {$url}\n";
        } catch (Exception $e2) {
            echo "   ❌ Manual registration failed: " . $e2->getMessage() . "\n";
        }
    }
    
    echo "\n3. Testing controller method...\n";
    
    try {
        $controller = new \App\Http\Controllers\Auth\RegisteredUserController();
        
        if (method_exists($controller, 'success')) {
            echo "   ✅ Controller method exists\n";
            
            // Test method execution
            $response = $controller->success();
            echo "   ✅ Method executable, returns: " . get_class($response) . "\n";
            
        } else {
            echo "   ❌ Controller method missing\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Controller test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n4. Testing view file...\n";
    
    $viewPath = resource_path('views/auth/register-success.blade.php');
    
    if (file_exists($viewPath)) {
        echo "   ✅ View file exists\n";
        
        // Test view rendering
        try {
            $view = view('auth.register-success');
            echo "   ✅ View renderable\n";
        } catch (Exception $e) {
            echo "   ❌ View rendering failed: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "   ❌ View file missing: {$viewPath}\n";
    }
    
    echo "\n5. Checking web.php syntax...\n";
    
    $webPhpPath = base_path('routes/web.php');
    $webPhpContent = file_get_contents($webPhpPath);
    
    if (str_contains($webPhpContent, "Route::get('/register/success'")) {
        echo "   ✅ Route definition found in web.php\n";
    } else {
        echo "   ❌ Route definition missing in web.php\n";
    }
    
    if (str_contains($webPhpContent, "->name('register.success')")) {
        echo "   ✅ Route name found in web.php\n";
    } else {
        echo "   ❌ Route name missing in web.php\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== DIRECT ROUTE TEST COMPLETED ===\n";
echo "\nIf route still not working, the issue might be:\n";
echo "1. Route cache not properly cleared\n";
echo "2. Syntax error in web.php\n";
echo "3. Controller import missing\n";
echo "4. Laravel not recognizing the route file\n";
echo "\nTry restarting the server completely.\n";
