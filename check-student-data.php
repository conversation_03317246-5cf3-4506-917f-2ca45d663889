<?php
echo "=== CHECKING STUDENT DATA ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check users table structure
    echo "📋 USERS Table Structure:\n";
    $result = $pdo->query("DESCRIBE users");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   • {$row['Field']} - {$row['Type']}\n";
    }
    
    echo "\n📊 Current Student Data:\n";
    $result = $pdo->query("SELECT id, name, email, nim, role, status, faculty, major, batch FROM users WHERE role = 'student' ORDER BY name");
    $studentCount = 0;

    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $studentCount++;
        echo "   {$studentCount}. {$row['name']}\n";
        echo "      📧 Email: {$row['email']}\n";
        echo "      🆔 NIM: {$row['nim']}\n";
        echo "      🏫 Faculty: " . ($row['faculty'] ?: 'NULL') . "\n";
        echo "      📚 Major: " . ($row['major'] ?: 'NULL') . "\n";
        echo "      📅 Batch: " . ($row['batch'] ?: 'NULL') . "\n";
        echo "      ✅ Status: {$row['status']}\n\n";
    }

    if ($studentCount == 0) {
        echo "   ❌ No student users found!\n";
    } else {
        echo "✅ Found {$studentCount} student users\n";

        // Check missing data
        $result = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'student' AND (faculty IS NULL OR major IS NULL OR batch IS NULL)");
        $missingData = $result->fetch(PDO::FETCH_ASSOC)['count'];

        if ($missingData > 0) {
            echo "⚠️  {$missingData} students have missing faculty/major/batch data\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== CHECK COMPLETE ===\n";
?>
