<?php
/**
 * Fix all storage issues - backgrounds, organization structures, and achievements display
 */

echo "=== FIXING ALL STORAGE ISSUES ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Syncing UKM background images...\n";
    
    $storageBackgrounds = storage_path('app/public/ukms/backgrounds');
    $publicBackgrounds = public_path('storage/ukms/backgrounds');
    
    if (!is_dir($publicBackgrounds)) {
        mkdir($publicBackgrounds, 0755, true);
        echo "   ✅ Created public backgrounds directory\n";
    }
    
    $backgroundFiles = glob($storageBackgrounds . '/*');
    $syncedBackgrounds = 0;
    
    foreach ($backgroundFiles as $file) {
        $filename = basename($file);
        $publicFile = $publicBackgrounds . '/' . $filename;
        
        if (!file_exists($publicFile)) {
            if (copy($file, $publicFile)) {
                echo "   ✅ Synced background: $filename\n";
                $syncedBackgrounds++;
            } else {
                echo "   ❌ Failed to sync: $filename\n";
            }
        }
    }
    
    echo "   📊 Synced $syncedBackgrounds background files\n\n";
    
    echo "2. Syncing organization structure images...\n";
    
    $storageStructures = storage_path('app/public/ukms/organization_structures');
    $publicStructures = public_path('storage/ukms/organization_structures');
    
    if (!is_dir($publicStructures)) {
        mkdir($publicStructures, 0755, true);
        echo "   ✅ Created public organization structures directory\n";
    }
    
    $structureFiles = glob($storageStructures . '/*');
    $syncedStructures = 0;
    
    foreach ($structureFiles as $file) {
        $filename = basename($file);
        $publicFile = $publicStructures . '/' . $filename;
        
        if (!file_exists($publicFile)) {
            if (copy($file, $publicFile)) {
                echo "   ✅ Synced structure: $filename\n";
                $syncedStructures++;
            } else {
                echo "   ❌ Failed to sync: $filename\n";
            }
        }
    }
    
    echo "   📊 Synced $syncedStructures organization structure files\n\n";
    
    echo "3. Testing UKM with background and structure...\n";
    
    $ukmsWithBackground = \App\Models\Ukm::whereNotNull('background_image')->get();
    $ukmsWithStructure = \App\Models\Ukm::whereNotNull('organization_structure')->get();
    
    echo "   📊 UKMs with background: " . $ukmsWithBackground->count() . "\n";
    echo "   📊 UKMs with organization structure: " . $ukmsWithStructure->count() . "\n\n";
    
    foreach ($ukmsWithBackground as $ukm) {
        $backgroundPath = public_path('storage/' . $ukm->background_image);
        $exists = file_exists($backgroundPath);
        echo "   Background for {$ukm->name}: " . ($exists ? "✅" : "❌") . " {$ukm->background_image}\n";
    }
    
    echo "\n";
    
    foreach ($ukmsWithStructure as $ukm) {
        $structurePath = public_path('storage/' . $ukm->organization_structure);
        $exists = file_exists($structurePath);
        echo "   Structure for {$ukm->name}: " . ($exists ? "✅" : "❌") . " {$ukm->organization_structure}\n";
    }
    
    echo "\n4. Testing achievements display for IMMA...\n";
    
    $immaUkm = \App\Models\Ukm::where('slug', 'imma')->first();
    if ($immaUkm) {
        echo "   Found IMMA UKM (ID: {$immaUkm->id})\n";
        
        // Load achievements manually
        $achievements = $immaUkm->achievements()->get();
        echo "   ✅ Achievements loaded: " . $achievements->count() . "\n";
        
        foreach ($achievements as $achievement) {
            echo "   📋 Achievement: {$achievement->title}\n";
            echo "      Level: {$achievement->level}\n";
            echo "      Date: {$achievement->achievement_date}\n";
            echo "      Position: " . ($achievement->position ?: 'N/A') . "\n";
            echo "      Participants: " . ($achievement->participants ?: 'N/A') . "\n";
            echo "      ---\n";
        }
    }
    
    echo "\n5. Creating test HTML for verification...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Storage Issues Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .image-test { width: 200px; height: 100px; border: 2px solid #ccc; margin: 10px; object-fit: cover; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Storage Issues Test</h1>
    
    <div class='test-section'>
        <h2>Background Images Test</h2>";
    
    foreach ($ukmsWithBackground as $ukm) {
        $url = asset('storage/' . $ukm->background_image);
        $testHtml .= "
        <div>
            <h3>{$ukm->name}</h3>
            <p>URL: $url</p>
            <img src='$url' alt='{$ukm->name} Background' class='image-test' 
                 onload=\"this.nextElementSibling.innerHTML='<span class=success>✅ Loaded</span>'\"
                 onerror=\"this.nextElementSibling.innerHTML='<span class=error>❌ Failed</span>'\">
            <p>Loading...</p>
        </div>";
    }
    
    $testHtml .= "
    </div>
    
    <div class='test-section'>
        <h2>Organization Structure Images Test</h2>";
    
    foreach ($ukmsWithStructure as $ukm) {
        $url = asset('storage/' . $ukm->organization_structure);
        $testHtml .= "
        <div>
            <h3>{$ukm->name}</h3>
            <p>URL: $url</p>
            <img src='$url' alt='{$ukm->name} Structure' class='image-test' 
                 onload=\"this.nextElementSibling.innerHTML='<span class=success>✅ Loaded</span>'\"
                 onerror=\"this.nextElementSibling.innerHTML='<span class=error>❌ Failed</span>'\">
            <p>Loading...</p>
        </div>";
    }
    
    $testHtml .= "
    </div>
    
    <div class='test-section'>
        <h2>Test Links</h2>
        <ul>
            <li><a href='http://127.0.0.1:8000/ukms/imma' target='_blank'>IMMA UKM Detail (should show achievements)</a></li>
            <li><a href='http://127.0.0.1:8000/ukms' target='_blank'>UKM Index (should show backgrounds)</a></li>
            <li><a href='http://127.0.0.1:8000/admin/ukms' target='_blank'>Admin UKM Management</a></li>
        </ul>
    </div>
</body>
</html>";
    
    file_put_contents(public_path('storage-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/storage-test.html\n";
    
    echo "\n=== STORAGE ISSUES FIX COMPLETED ===\n";
    echo "🔗 Test page: http://127.0.0.1:8000/storage-test.html\n";
    echo "🔗 IMMA UKM: http://127.0.0.1:8000/ukms/imma\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
