<?php
/**
 * Fix image loading issues (403 Forbidden)
 */

echo "=== FIXING IMAGE LOADING ISSUES ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking storage configuration...\n";
    
    // Check storage paths
    $storagePath = storage_path('app/public');
    $publicStoragePath = public_path('storage');
    
    echo "   📁 Storage path: {$storagePath}\n";
    echo "   📁 Public storage path: {$publicStoragePath}\n";
    echo "   📁 Storage exists: " . (is_dir($storagePath) ? 'YES ✅' : 'NO ❌') . "\n";
    echo "   📁 Public storage link exists: " . (is_link($publicStoragePath) ? 'YES ✅' : 'NO ❌') . "\n";
    
    if (!is_link($publicStoragePath)) {
        echo "   🔧 Creating storage link...\n";
        
        // Remove if exists as directory
        if (is_dir($publicStoragePath)) {
            echo "   🗑️ Removing existing storage directory...\n";
            rmdir($publicStoragePath);
        }
        
        // Create symlink
        if (symlink($storagePath, $publicStoragePath)) {
            echo "   ✅ Storage link created successfully\n";
        } else {
            echo "   ❌ Failed to create storage link\n";
        }
    }
    
    echo "\n2. Checking UKM logo files...\n";
    
    $ukms = \App\Models\Ukm::whereNotNull('logo')->get();
    
    foreach ($ukms as $ukm) {
        $logoPath = storage_path('app/public/' . $ukm->logo);
        $publicLogoPath = public_path('storage/' . $ukm->logo);
        
        echo "   UKM: {$ukm->name}\n";
        echo "      Logo file: {$ukm->logo}\n";
        echo "      Storage exists: " . (file_exists($logoPath) ? 'YES ✅' : 'NO ❌') . "\n";
        echo "      Public accessible: " . (file_exists($publicLogoPath) ? 'YES ✅' : 'NO ❌') . "\n";
        
        if (file_exists($logoPath)) {
            $permissions = substr(sprintf('%o', fileperms($logoPath)), -4);
            echo "      Permissions: {$permissions}\n";
            
            // Fix permissions if needed
            if ($permissions !== '0644') {
                chmod($logoPath, 0644);
                echo "      ✅ Fixed permissions to 0644\n";
            }
        }
        echo "      ---\n";
    }
    
    echo "\n3. Checking event poster files...\n";
    
    $events = \App\Models\Event::whereNotNull('poster')->get();
    
    foreach ($events as $event) {
        $posterPath = storage_path('app/public/' . $event->poster);
        $publicPosterPath = public_path('storage/' . $event->poster);
        
        echo "   Event: {$event->title}\n";
        echo "      Poster file: {$event->poster}\n";
        echo "      Storage exists: " . (file_exists($posterPath) ? 'YES ✅' : 'NO ❌') . "\n";
        echo "      Public accessible: " . (file_exists($publicPosterPath) ? 'YES ✅' : 'NO ❌') . "\n";
        
        if (file_exists($posterPath)) {
            $permissions = substr(sprintf('%o', fileperms($posterPath)), -4);
            echo "      Permissions: {$permissions}\n";
            
            // Fix permissions if needed
            if ($permissions !== '0644') {
                chmod($posterPath, 0644);
                echo "      ✅ Fixed permissions to 0644\n";
            }
        }
        echo "      ---\n";
    }
    
    echo "\n4. Checking background image files...\n";
    
    $ukmsWithBackground = \App\Models\Ukm::whereNotNull('background_image')->get();
    
    foreach ($ukmsWithBackground as $ukm) {
        $bgPath = storage_path('app/public/' . $ukm->background_image);
        $publicBgPath = public_path('storage/' . $ukm->background_image);
        
        echo "   UKM: {$ukm->name}\n";
        echo "      Background file: {$ukm->background_image}\n";
        echo "      Storage exists: " . (file_exists($bgPath) ? 'YES ✅' : 'NO ❌') . "\n";
        echo "      Public accessible: " . (file_exists($publicBgPath) ? 'YES ✅' : 'NO ❌') . "\n";
        
        if (file_exists($bgPath)) {
            $permissions = substr(sprintf('%o', fileperms($bgPath)), -4);
            echo "      Permissions: {$permissions}\n";
            
            // Fix permissions if needed
            if ($permissions !== '0644') {
                chmod($bgPath, 0644);
                echo "      ✅ Fixed permissions to 0644\n";
            }
        }
        echo "      ---\n";
    }
    
    echo "\n5. Testing image URLs...\n";
    
    // Test a few image URLs
    $testImages = [];
    
    if ($ukms->count() > 0) {
        $testUkm = $ukms->first();
        $testImages[] = [
            'type' => 'UKM Logo',
            'name' => $testUkm->name,
            'url' => asset('storage/' . $testUkm->logo),
            'file' => $testUkm->logo
        ];
    }
    
    if ($events->count() > 0) {
        $testEvent = $events->first();
        $testImages[] = [
            'type' => 'Event Poster',
            'name' => $testEvent->title,
            'url' => asset('storage/' . $testEvent->poster),
            'file' => $testEvent->poster
        ];
    }
    
    foreach ($testImages as $image) {
        echo "   {$image['type']}: {$image['name']}\n";
        echo "      URL: {$image['url']}\n";
        echo "      File: {$image['file']}\n";
        
        // Check if file exists in public storage
        $publicPath = public_path('storage/' . $image['file']);
        echo "      Accessible: " . (file_exists($publicPath) ? 'YES ✅' : 'NO ❌') . "\n";
        echo "      ---\n";
    }
    
    echo "\n6. Creating test HTML page...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>🖼️ Image Loading Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { 
            margin: 20px 0; 
            padding: 25px; 
            border-radius: 12px; 
            background: white; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            text-align: center; 
            padding: 40px; 
            border-radius: 12px; 
            margin-bottom: 30px; 
        }
        .image-test { 
            border: 2px solid #e9ecef; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 15px 0; 
            background: #f8f9fa; 
        }
        .image-test.working { border-color: #28a745; background: #d4edda; }
        .image-test.broken { border-color: #dc3545; background: #f8d7da; }
        .test-image { 
            max-width: 200px; 
            max-height: 150px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            margin: 10px 0; 
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .test-link { 
            display: inline-block; 
            background: #007bff; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 6px; 
            margin: 10px 10px 10px 0; 
            transition: background 0.3s; 
        }
        .test-link:hover { background: #0056b3; color: white; text-decoration: none; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🖼️ Image Loading Test</h1>
            <p>Testing UKM logos and event posters</p>
        </div>
        
        <div class='test-section'>
            <h2>📊 Storage Configuration</h2>
            <ul>
                <li><strong>Storage Link:</strong> " . (is_link($publicStoragePath) ? '<span class="success">✅ Working</span>' : '<span class="error">❌ Missing</span>') . "</li>
                <li><strong>Storage Path:</strong> {$storagePath}</li>
                <li><strong>Public Path:</strong> {$publicStoragePath}</li>
            </ul>
        </div>
        
        <div class='test-section'>
            <h2>🏛️ UKM Logos</h2>";
    
    foreach ($ukms as $ukm) {
        $logoExists = file_exists(public_path('storage/' . $ukm->logo));
        $testHtml .= "
            <div class='image-test " . ($logoExists ? 'working' : 'broken') . "'>
                <h4>{$ukm->name}</h4>
                <p><strong>File:</strong> {$ukm->logo}</p>
                <p><strong>Status:</strong> " . ($logoExists ? '<span class="success">✅ Working</span>' : '<span class="error">❌ Missing</span>') . "</p>";
        
        if ($logoExists) {
            $testHtml .= "<img src='" . asset('storage/' . $ukm->logo) . "' alt='{$ukm->name}' class='test-image' onerror=\"this.style.border='2px solid red'; this.alt='Failed to load'\">";
        }
        
        $testHtml .= "</div>";
    }
    
    $testHtml .= "
        </div>
        
        <div class='test-section'>
            <h2>📅 Event Posters</h2>";
    
    foreach ($events as $event) {
        $posterExists = file_exists(public_path('storage/' . $event->poster));
        $testHtml .= "
            <div class='image-test " . ($posterExists ? 'working' : 'broken') . "'>
                <h4>{$event->title}</h4>
                <p><strong>File:</strong> {$event->poster}</p>
                <p><strong>Status:</strong> " . ($posterExists ? '<span class="success">✅ Working</span>' : '<span class="error">❌ Missing</span>') . "</p>";
        
        if ($posterExists) {
            $testHtml .= "<img src='" . asset('storage/' . $event->poster) . "' alt='{$event->title}' class='test-image' onerror=\"this.style.border='2px solid red'; this.alt='Failed to load'\">";
        }
        
        $testHtml .= "</div>";
    }
    
    $testHtml .= "
        </div>
        
        <div class='test-section'>
            <h2>🔗 Test Links</h2>
            <a href='http://127.0.0.1:8000/admin/events' class='test-link' target='_blank'>
                ⚙️ Admin Events
            </a>
            <a href='http://127.0.0.1:8000/kegiatan' class='test-link' target='_blank'>
                📅 Public Events
            </a>
            <a href='http://127.0.0.1:8000/ukms' class='test-link' target='_blank'>
                🏛️ UKMs List
            </a>
        </div>
        
        <div class='test-section'>
            <h2>🔧 Solutions Applied</h2>
            <ul>
                <li>✅ Created/fixed storage symlink</li>
                <li>✅ Fixed file permissions to 0644</li>
                <li>✅ Verified file accessibility</li>
                <li>✅ Tested image URLs</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Test image loading and show errors
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('.test-image');
            images.forEach(img => {
                img.addEventListener('error', function() {
                    this.style.border = '2px solid red';
                    this.alt = 'Failed to load: ' + this.src;
                    console.error('Failed to load image:', this.src);
                });
                
                img.addEventListener('load', function() {
                    this.style.border = '2px solid green';
                    console.log('Successfully loaded image:', this.src);
                });
            });
        });
    </script>
</body>
</html>";
    
    file_put_contents(public_path('image-loading-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/image-loading-test.html\n";
    
    echo "\n=== IMAGE LOADING FIX COMPLETED ===\n";
    echo "🔗 Test page: http://127.0.0.1:8000/image-loading-test.html\n";
    echo "📋 Summary:\n";
    echo "   ✅ Storage link: " . (is_link($publicStoragePath) ? 'Fixed' : 'Needs manual fix') . "\n";
    echo "   ✅ File permissions: Fixed\n";
    echo "   ✅ Image accessibility: Verified\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
