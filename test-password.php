<?php

echo "=== PASSWORD HASH TESTER ===\n\n";

// Test hash yang digunakan di database
$hash1 = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
$hash2 = '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm';

// Test passwords
$passwords = ['password', 'admin123', 'secret', '123456', 'admin'];

echo "Testing Hash 1: " . substr($hash1, 0, 30) . "...\n";
foreach ($passwords as $pass) {
    $result = password_verify($pass, $hash1) ? '✅ MATCH' : '❌ NO MATCH';
    echo "  {$pass}: {$result}\n";
}

echo "\nTesting Hash 2: " . substr($hash2, 0, 30) . "...\n";
foreach ($passwords as $pass) {
    $result = password_verify($pass, $hash2) ? '✅ MATCH' : '❌ NO MATCH';
    echo "  {$pass}: {$result}\n";
}

echo "\n=== GENERATING NEW HASHES ===\n";
foreach ($passwords as $pass) {
    $newHash = password_hash($pass, PASSWORD_DEFAULT);
    echo "{$pass}: {$newHash}\n";
}

echo "\n=== FINAL RECOMMENDATION ===\n";
echo "Use these credentials:\n";
echo "Email: <EMAIL>\n";
echo "Password: password\n";
echo "OR\n";
echo "Email: <EMAIL>\n";
echo "Password: secret\n";
