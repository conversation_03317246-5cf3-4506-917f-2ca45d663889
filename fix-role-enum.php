<?php

echo "=== FIXING ROLE ENUM TO SUPPORT KETUA_UKM ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking current role enum values...\n";
    
    // Check current users and their roles
    $users = \App\Models\User::all();
    echo "   Current users and roles:\n";
    foreach ($users as $user) {
        echo "      - {$user->name}: {$user->role}\n";
    }
    
    echo "\n2. Updating role enum to support ketua_ukm...\n";
    
    // Use raw SQL to modify the enum
    \Illuminate\Support\Facades\DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('student', 'ketua_ukm', 'admin') DEFAULT 'student'");
    
    echo "   ✅ Role enum updated successfully!\n";
    
    echo "\n3. Testing role assignment...\n";
    
    // Find a test user to change role
    $testUser = \App\Models\User::where('role', 'student')->first();
    
    if ($testUser) {
        echo "   Testing with user: {$testUser->name}\n";
        echo "   Current role: {$testUser->role}\n";
        
        // Try to update to ketua_ukm
        $testUser->update(['role' => 'ketua_ukm']);
        $testUser->refresh();
        
        echo "   After update to ketua_ukm: {$testUser->role}\n";
        
        if ($testUser->role === 'ketua_ukm') {
            echo "   ✅ Role assignment test PASSED!\n";
            
            // Change back to student
            $testUser->update(['role' => 'student']);
            echo "   Reverted back to student role\n";
        } else {
            echo "   ❌ Role assignment test FAILED!\n";
        }
    } else {
        echo "   No student users found for testing\n";
    }
    
    echo "\n4. Verifying enum values...\n";
    
    // Check the actual enum values in database
    $result = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users LIKE 'role'");
    if (!empty($result)) {
        $enumValues = $result[0]->Type;
        echo "   Current enum definition: {$enumValues}\n";
        
        if (strpos($enumValues, 'ketua_ukm') !== false) {
            echo "   ✅ ketua_ukm is now supported in the enum!\n";
        } else {
            echo "   ❌ ketua_ukm is still not in the enum\n";
        }
    }
    
    echo "\n=== ROLE ENUM FIX COMPLETED ===\n";
    echo "You can now assign 'ketua_ukm' role to users!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
