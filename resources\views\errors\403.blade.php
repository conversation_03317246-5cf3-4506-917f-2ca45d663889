@extends('layouts.app')

@section('title', '403 - <PERSON><PERSON><PERSON>')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <div>
            <div class="mx-auto h-32 w-32 text-yellow-600">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-6xl font-extrabold text-gray-900">403</h2>
            <h3 class="mt-2 text-3xl font-bold text-gray-900">Aks<PERSON></h3>
            <p class="mt-2 text-sm text-gray-600">
                Anda tidak memiliki izin untuk mengakses halaman ini.
            </p>
        </div>

        <div class="mt-8 space-y-4">
            <div class="bg-white shadow rounded-lg p-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Kemungkinan Penyebab:</h4>
                <ul class="text-left text-sm text-gray-600 space-y-2">
                    <li class="flex items-start">
                        <span class="text-yellow-500 mr-2">•</span>
                        Anda tidak memiliki role yang sesuai
                    </li>
                    <li class="flex items-start">
                        <span class="text-yellow-500 mr-2">•</span>
                        Akun Anda belum diaktivasi oleh admin
                    </li>
                    <li class="flex items-start">
                        <span class="text-yellow-500 mr-2">•</span>
                        Halaman khusus untuk admin atau ketua UKM
                    </li>
                    <li class="flex items-start">
                        <span class="text-yellow-500 mr-2">•</span>
                        Session Anda telah berakhir
                    </li>
                </ul>
            </div>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('home') }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Kembali ke Beranda
                </a>

                @auth
                <a href="{{ route('dashboard') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                    </svg>
                    Dashboard
                </a>
                @else
                <a href="{{ route('login') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                    </svg>
                    Login
                </a>
                @endauth
            </div>

            <div class="text-center">
                <button onclick="history.back()" 
                        class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                    ← Kembali ke halaman sebelumnya
                </button>
            </div>
        </div>

        <div class="mt-8 text-xs text-gray-500">
            <p>Jika Anda yakin memiliki akses, silakan hubungi administrator.</p>
            <p class="mt-1">Error Code: 403 | {{ now()->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</div>
@endsection
