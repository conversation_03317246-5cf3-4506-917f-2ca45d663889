<?php

echo "=== TESTING EDIT EVENT IMAGE PREVIEW FUNCTIONALITY ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing edit event image preview features...\n";
    
    // Find an event with images
    $event = \App\Models\Event::whereNotNull('poster')
                             ->whereNotNull('certificate_template')
                             ->first();
    
    if (!$event) {
        echo "   ⚠️  No event with both poster and certificate template found\n";
        $event = \App\Models\Event::first();
        if (!$event) {
            echo "   ❌ No event found\n";
            exit;
        }
    }
    
    echo "   ✅ Using event: {$event->title}\n";
    echo "   Event ID: {$event->id}\n";
    
    echo "2. Checking current images...\n";
    
    $images = [
        'poster' => $event->poster,
        'certificate_template' => $event->certificate_template,
    ];
    
    foreach ($images as $type => $path) {
        echo "   {$type}: ";
        if ($path) {
            $fullPath = storage_path('app/public/' . $path);
            $exists = file_exists($fullPath);
            echo "{$path} " . ($exists ? '✅ EXISTS' : '❌ MISSING') . "\n";
            
            if ($exists) {
                $size = filesize($fullPath);
                $mimeType = mime_content_type($fullPath);
                echo "     Size: " . round($size / 1024, 2) . " KB\n";
                echo "     Type: {$mimeType}\n";
                
                // Check if it's an image
                $isImage = strpos($mimeType, 'image/') === 0;
                echo "     Is Image: " . ($isImage ? 'Yes' : 'No') . "\n";
            }
        } else {
            echo "Not uploaded\n";
        }
    }
    
    echo "3. Testing view file image preview features...\n";
    
    $editViewPath = resource_path('views/ketua-ukm/events/edit.blade.php');
    if (file_exists($editViewPath)) {
        echo "   ✅ Edit view exists\n";
        
        $viewContent = file_get_contents($editViewPath);
        
        // Check for image preview features
        $previewFeatures = [
            'Current Poster Preview' => 'Preview poster saat ini',
            'Current Certificate Template Preview' => 'Preview template saat ini',
            'New Poster Preview' => 'Preview poster baru',
            'New Certificate Template Preview' => 'Preview template baru',
            'previewImage function' => 'JavaScript preview function',
            'onchange="previewImage' => 'Preview trigger on file change',
            'w-24 h-32 object-cover' => 'Poster preview styling',
            'w-32 h-24 object-cover' => 'Certificate preview styling',
            'asset(\'storage/\'' => 'Current image display',
            'Lihat Ukuran Penuh' => 'View full size link',
            'Lihat Template' => 'View template link',
        ];
        
        foreach ($previewFeatures as $pattern => $description) {
            if (strpos($viewContent, $pattern) !== false) {
                echo "   ✅ {$description}\n";
            } else {
                echo "   ❌ Missing: {$description}\n";
            }
        }
        
    } else {
        echo "   ❌ Edit view file not found\n";
    }
    
    echo "4. Testing JavaScript preview functionality...\n";
    
    // Check for JavaScript function
    if (strpos($viewContent, 'function previewImage(input, previewId)') !== false) {
        echo "   ✅ previewImage function defined\n";
        
        $jsFeatures = [
            'input.files && input.files[0]' => 'File selection check',
            'file.type.startsWith(\'image/\')' => 'Image type validation',
            'FileReader()' => 'File reader for preview',
            'reader.readAsDataURL(file)' => 'Convert to data URL',
            'previewImg.src = e.target.result' => 'Set preview image source',
            'preview.classList.remove(\'hidden\')' => 'Show preview',
            'preview.classList.add(\'hidden\')' => 'Hide preview',
        ];
        
        foreach ($jsFeatures as $pattern => $description) {
            if (strpos($viewContent, $pattern) !== false) {
                echo "   ✅ {$description}\n";
            } else {
                echo "   ❌ Missing: {$description}\n";
            }
        }
        
    } else {
        echo "   ❌ previewImage function not found\n";
    }
    
    echo "5. Testing image preview styling...\n";
    
    $stylingFeatures = [
        'w-24 h-32' => 'Poster preview dimensions (portrait)',
        'w-32 h-24' => 'Certificate preview dimensions (landscape)',
        'object-cover' => 'Image scaling',
        'rounded-lg' => 'Rounded corners',
        'border border-gray-300' => 'Border styling',
        'shadow-sm' => 'Shadow effect',
        'bg-gray-50' => 'Background color',
        'p-3' => 'Padding',
        'space-x-3' => 'Horizontal spacing',
        'flex items-start' => 'Flex layout',
    ];
    
    foreach ($stylingFeatures as $pattern => $description) {
        if (strpos($viewContent, $pattern) !== false) {
            echo "   ✅ {$description}\n";
        } else {
            echo "   ❌ Missing: {$description}\n";
        }
    }
    
    echo "6. Testing file type handling...\n";
    
    // Check for different file type handling
    $fileTypeFeatures = [
        'pathinfo($event->certificate_template, PATHINFO_EXTENSION)' => 'File extension detection',
        'in_array(strtolower(' => 'Extension validation',
        '[\'jpg\', \'jpeg\', \'png\', \'gif\']' => 'Supported image formats',
        'fas fa-file-pdf' => 'PDF file icon',
        'bg-gray-200' => 'Non-image placeholder',
        'flex items-center justify-center' => 'Icon centering',
    ];
    
    foreach ($fileTypeFeatures as $pattern => $description) {
        if (strpos($viewContent, $pattern) !== false) {
            echo "   ✅ {$description}\n";
        } else {
            echo "   ❌ Missing: {$description}\n";
        }
    }
    
    echo "7. Testing user experience features...\n";
    
    $uxFeatures = [
        'Poster Saat Ini:' => 'Current poster label',
        'Template Saat Ini:' => 'Current template label',
        'Preview Poster Baru:' => 'New poster preview label',
        'Preview Template Baru:' => 'New template preview label',
        'Lihat Ukuran Penuh' => 'View full size link',
        'Lihat Template' => 'View template link',
        'fas fa-external-link-alt' => 'External link icon',
        'target="_blank"' => 'Open in new tab',
        'Template ini akan digunakan sebagai background sertifikat' => 'Template usage explanation',
    ];
    
    foreach ($uxFeatures as $pattern => $description) {
        if (strpos($viewContent, $pattern) !== false) {
            echo "   ✅ {$description}\n";
        } else {
            echo "   ❌ Missing: {$description}\n";
        }
    }
    
    echo "8. Testing responsive design...\n";
    
    $responsiveFeatures = [
        'flex-shrink-0' => 'Image container no shrink',
        'flex-1 min-w-0' => 'Text container flexible',
        'text-sm' => 'Small text sizing',
        'text-xs' => 'Extra small text sizing',
        'mb-1' => 'Margin bottom spacing',
        'mt-1' => 'Margin top spacing',
        'inline-flex items-center' => 'Inline flex for links',
    ];
    
    foreach ($responsiveFeatures as $pattern => $description) {
        if (strpos($viewContent, $pattern) !== false) {
            echo "   ✅ {$description}\n";
        } else {
            echo "   ❌ Missing: {$description}\n";
        }
    }
    
    echo "9. Testing accessibility features...\n";
    
    $accessibilityFeatures = [
        'alt="Current Poster"' => 'Poster alt text',
        'alt="Current Certificate Template"' => 'Certificate alt text',
        'alt="New Poster Preview"' => 'New poster alt text',
        'alt="New Certificate Template Preview"' => 'New certificate alt text',
        'aria-' => 'ARIA attributes',
        'title=' => 'Title attributes',
    ];
    
    foreach ($accessibilityFeatures as $pattern => $description) {
        if (strpos($viewContent, $pattern) !== false) {
            echo "   ✅ {$description}\n";
        } else {
            echo "   ⚠️  Consider adding: {$description}\n";
        }
    }
    
    echo "10. Expected user workflow...\n";
    
    echo "   Image Preview Workflow:\n";
    echo "   1. Ketua UKM opens edit event page ✅\n";
    echo "   2. Sees current poster/template with preview ✅\n";
    echo "   3. Can click 'Lihat Ukuran Penuh' to view full image ✅\n";
    echo "   4. Selects new image file ✅\n";
    echo "   5. JavaScript shows instant preview ✅\n";
    echo "   6. Can compare old vs new image ✅\n";
    echo "   7. Submits form to update ✅\n";
    
    echo "   Visual Layout:\n";
    echo "   ┌─────────────────────────────────────┐\n";
    echo "   │ Poster Event                        │\n";
    echo "   │ ┌─────────────────────────────────┐ │\n";
    echo "   │ │ Poster Saat Ini:               │ │\n";
    echo "   │ │ [IMG] filename.jpg             │ │\n";
    echo "   │ │       [Lihat Ukuran Penuh]     │ │\n";
    echo "   │ └─────────────────────────────────┘ │\n";
    echo "   │ [Choose File] No file chosen        │\n";
    echo "   │ ┌─────────────────────────────────┐ │\n";
    echo "   │ │ Preview Poster Baru: (hidden)   │ │\n";
    echo "   │ │ [IMG] (shows when file selected)│ │\n";
    echo "   │ └─────────────────────────────────┘ │\n";
    echo "   └─────────────────────────────────────┘\n";
    
    echo "\n=== EDIT EVENT IMAGE PREVIEW TEST COMPLETED ===\n";
    echo "✅ Image preview functionality verified!\n";
    echo "\nKey Features Added:\n";
    echo "🖼️  CURRENT IMAGE PREVIEW: Shows existing poster/template\n";
    echo "👁️  FULL SIZE VIEW: Links to view images in full size\n";
    echo "⚡ INSTANT PREVIEW: JavaScript preview of new images\n";
    echo "📱 RESPONSIVE DESIGN: Proper sizing and layout\n";
    echo "🎨 VISUAL COMPARISON: Can see old vs new images\n";
    echo "📄 FILE TYPE SUPPORT: Handles images and PDFs\n";
    echo "\nExpected Behavior:\n";
    echo "✅ Current images displayed with thumbnails\n";
    echo "✅ Click links to view full size images\n";
    echo "✅ Select new file shows instant preview\n";
    echo "✅ Proper sizing for poster (portrait) and certificate (landscape)\n";
    echo "✅ PDF files show icon instead of preview\n";
    echo "✅ Clean, professional layout\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
