<?php

// Test script untuk memverifikasi login terpadu
echo "=== TESTING UNIFIED LOGIN SYSTEM ===\n\n";

// Test 1: Cek apakah admin user sudah ada
echo "1. Checking admin users...\n";
try {
    require_once 'vendor/autoload.php';

    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

    $adminUsers = \App\Models\User::where('role', 'admin')->get();

    if ($adminUsers->count() > 0) {
        echo "   ✅ Found {$adminUsers->count()} admin user(s):\n";
        foreach ($adminUsers as $admin) {
            echo "      - {$admin->name} ({$admin->email})\n";
        }
    } else {
        echo "   ❌ No admin users found. Creating default admin...\n";

        \App\Models\User::create([
            'nim' => 'ADMIN001',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Administrasi',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        echo "   ✅ Default admin created!\n";
        echo "      Email: <EMAIL>\n";
        echo "      Password: admin123\n";
    }

} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
}

// Test 2: Cek student users
echo "\n2. Checking student users...\n";
try {
    $studentUsers = \App\Models\User::where('role', 'student')->count();
    echo "   ✅ Found {$studentUsers} student user(s)\n";
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
}

// Test 3: Cek routes
echo "\n3. Checking routes...\n";
try {
    $routes = \Illuminate\Support\Facades\Route::getRoutes();

    $loginRoute = $routes->getByName('login');
    $adminDashboardRoute = $routes->getByName('admin.dashboard');
    $studentDashboardRoute = $routes->getByName('dashboard');

    if ($loginRoute) {
        echo "   ✅ Login route exists: " . $loginRoute->uri() . "\n";
    } else {
        echo "   ❌ Login route not found\n";
    }

    if ($adminDashboardRoute) {
        echo "   ✅ Admin dashboard route exists: " . $adminDashboardRoute->uri() . "\n";
    } else {
        echo "   ❌ Admin dashboard route not found\n";
    }

    if ($studentDashboardRoute) {
        echo "   ✅ Student dashboard route exists: " . $studentDashboardRoute->uri() . "\n";
    } else {
        echo "   ❌ Student dashboard route not found\n";
    }

} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
echo "\n=== LOGIN INFORMATION ===\n";
echo "URL: http://127.0.0.1:8000/login\n\n";

echo "👨‍🎓 MAHASISWA:\n";
echo "- Gunakan NIM atau email @student.telkomuniversity.ac.id\n";
echo "- Otomatis masuk ke Student Dashboard\n\n";

echo "👨‍💼 ADMINISTRATOR:\n";
echo "- Email: <EMAIL>\n";
echo "- Password: admin123\n";
echo "- Otomatis masuk ke Admin Dashboard\n\n";

echo "📋 FITUR LOGIN PAGE:\n";
echo "✅ Panduan mahasiswa (box biru)\n";
echo "✅ Informasi akun admin (box merah)\n";
echo "✅ Field NIM atau Email\n";
echo "✅ Auto-redirect berdasarkan role\n";
