<?php

echo "=== TESTING ATTENDANCE VERIFICATION ISSUES ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking attendance data and verification buttons...\n";
    
    // Find an event with attendances
    $event = \App\Models\Event::whereHas('attendances')->first();
    if (!$event) {
        echo "   ❌ No event with attendances found\n";
        exit;
    }
    
    echo "   ✅ Testing with event: {$event->title}\n";
    
    $attendances = $event->attendances()->with('user')->get();
    echo "   Total attendances: {$attendances->count()}\n";
    
    echo "2. Analyzing attendance statuses...\n";
    
    $statusCounts = [
        'present_pending' => 0,
        'present_verified' => 0,
        'present_rejected' => 0,
        'absent' => 0,
        'other' => 0
    ];
    
    foreach ($attendances as $attendance) {
        echo "   Attendance ID {$attendance->id}:\n";
        echo "     User: {$attendance->user->name}\n";
        echo "     Status: {$attendance->status}\n";
        echo "     Verification Status: {$attendance->verification_status}\n";
        echo "     Proof File: " . ($attendance->proof_file ? 'Yes' : 'No') . "\n";
        
        // Check button visibility condition
        $shouldShowVerifyButtons = ($attendance->status === 'present' && $attendance->verification_status === 'pending');
        echo "     Should show verify buttons: " . ($shouldShowVerifyButtons ? 'Yes' : 'No') . "\n";
        
        if ($attendance->status === 'present') {
            if ($attendance->verification_status === 'pending') {
                $statusCounts['present_pending']++;
            } elseif ($attendance->verification_status === 'verified') {
                $statusCounts['present_verified']++;
            } elseif ($attendance->verification_status === 'rejected') {
                $statusCounts['present_rejected']++;
            }
        } elseif ($attendance->status === 'absent') {
            $statusCounts['absent']++;
        } else {
            $statusCounts['other']++;
        }
        echo "\n";
    }
    
    echo "3. Status summary:\n";
    foreach ($statusCounts as $status => $count) {
        echo "   {$status}: {$count}\n";
    }
    
    echo "4. Testing verification functionality...\n";
    
    // Find a pending attendance to test
    $pendingAttendance = $attendances->where('status', 'present')
                                   ->where('verification_status', 'pending')
                                   ->first();
    
    if (!$pendingAttendance) {
        echo "   ⚠️  No pending attendance found, creating test data...\n";
        
        // Create test attendance
        $student = \App\Models\User::where('role', 'student')->first();
        if (!$student) {
            echo "   ❌ No student found for testing\n";
            exit;
        }
        
        $pendingAttendance = \App\Models\EventAttendance::create([
            'event_id' => $event->id,
            'user_id' => $student->id,
            'status' => 'present',
            'verification_status' => 'pending',
            'submitted_at' => now(),
            'notes' => 'Test attendance for verification',
        ]);
        
        echo "   ✅ Created test attendance ID: {$pendingAttendance->id}\n";
    } else {
        echo "   ✅ Using existing pending attendance ID: {$pendingAttendance->id}\n";
    }
    
    echo "5. Testing verification methods...\n";
    
    // Test verify method
    try {
        $originalStatus = $pendingAttendance->verification_status;
        
        // Test verify
        $pendingAttendance->verify(1, 'Test verification');
        $pendingAttendance->refresh();
        
        echo "   Verify test:\n";
        echo "     Before: {$originalStatus}\n";
        echo "     After: {$pendingAttendance->verification_status}\n";
        echo "     Verified by: {$pendingAttendance->verified_by}\n";
        echo "     Verified at: {$pendingAttendance->verified_at}\n";
        
        if ($pendingAttendance->verification_status === 'verified') {
            echo "   ✅ Verify method working correctly\n";
        } else {
            echo "   ❌ Verify method failed\n";
        }
        
        // Reset for reject test
        $pendingAttendance->update([
            'verification_status' => 'pending',
            'verified_by' => null,
            'verified_at' => null,
            'verification_notes' => null,
        ]);
        
        // Test reject
        $pendingAttendance->reject(1, 'Test rejection');
        $pendingAttendance->refresh();
        
        echo "   Reject test:\n";
        echo "     Status: {$pendingAttendance->verification_status}\n";
        echo "     Notes: {$pendingAttendance->verification_notes}\n";
        
        if ($pendingAttendance->verification_status === 'rejected') {
            echo "   ✅ Reject method working correctly\n";
        } else {
            echo "   ❌ Reject method failed\n";
        }
        
    } catch (\Exception $e) {
        echo "   ❌ Error testing verification: " . $e->getMessage() . "\n";
    }
    
    echo "6. Testing certificate download functionality...\n";
    
    // Find a verified attendance
    $verifiedAttendance = $attendances->where('verification_status', 'verified')->first();
    if (!$verifiedAttendance) {
        // Create one for testing
        $pendingAttendance->update([
            'verification_status' => 'verified',
            'verified_by' => 1,
            'verified_at' => now(),
        ]);
        $verifiedAttendance = $pendingAttendance;
        echo "   ✅ Created verified attendance for testing\n";
    }
    
    echo "   Testing canDownloadCertificate()...\n";
    $canDownload = $verifiedAttendance->canDownloadCertificate();
    echo "   Can download certificate: " . ($canDownload ? 'Yes' : 'No') . "\n";
    
    if ($canDownload) {
        echo "   ✅ Certificate download should be available\n";
    } else {
        echo "   ❌ Certificate download not available\n";
        echo "   Status: {$verifiedAttendance->status}\n";
        echo "   Verification: {$verifiedAttendance->verification_status}\n";
    }
    
    echo "7. Testing bulk verification route...\n";
    
    // Check if route exists
    try {
        $route = route('ketua-ukm.events.bulk-verify-attendances', $event);
        echo "   Bulk verification route: {$route}\n";
        echo "   ✅ Route exists\n";
    } catch (\Exception $e) {
        echo "   ❌ Route error: " . $e->getMessage() . "\n";
    }
    
    echo "8. Testing certificate service...\n";
    
    try {
        $certificateService = app(\App\Services\CertificateService::class);
        echo "   ✅ CertificateService can be instantiated\n";
        
        // Test certificate generation (without actually generating)
        if ($verifiedAttendance->canDownloadCertificate()) {
            echo "   Testing certificate generation...\n";
            
            // Check if event has certificate template
            if ($event->certificate_template) {
                echo "   Event has certificate template: Yes\n";
            } else {
                echo "   Event has certificate template: No (will use default)\n";
            }
            
            echo "   ✅ Certificate generation should work\n";
        }
        
    } catch (\Exception $e) {
        echo "   ❌ CertificateService error: " . $e->getMessage() . "\n";
    }
    
    echo "9. Checking view conditions...\n";
    
    // Simulate view conditions
    foreach ($attendances->take(3) as $attendance) {
        echo "   Attendance {$attendance->id}:\n";
        
        $showVerifyButtons = ($attendance->status === 'present' && $attendance->verification_status === 'pending');
        $showDetailButton = ($attendance->verification_status !== 'pending');
        $showDash = !$showVerifyButtons && !$showDetailButton;
        
        echo "     Show verify buttons: " . ($showVerifyButtons ? 'Yes' : 'No') . "\n";
        echo "     Show detail button: " . ($showDetailButton ? 'Yes' : 'No') . "\n";
        echo "     Show dash: " . ($showDash ? 'Yes' : 'No') . "\n";
        
        if ($showVerifyButtons) {
            echo "     ✅ Should show verify/reject buttons\n";
        } elseif ($showDetailButton) {
            echo "     ✅ Should show detail button\n";
        } else {
            echo "     ✅ Should show dash (-)\n";
        }
        echo "\n";
    }
    
    echo "10. Recommendations...\n";
    
    if ($statusCounts['present_pending'] === 0) {
        echo "   ⚠️  No pending attendances found\n";
        echo "   💡 Create test attendance with status='present' and verification_status='pending'\n";
    }
    
    if ($statusCounts['present_verified'] === 0) {
        echo "   ⚠️  No verified attendances found\n";
        echo "   💡 Verify some attendances to test certificate download\n";
    }
    
    echo "   💡 To see verify buttons, ensure attendance has:\n";
    echo "      - status = 'present'\n";
    echo "      - verification_status = 'pending'\n";
    echo "      - proof_file uploaded (optional but recommended)\n";
    
    echo "\n=== ATTENDANCE VERIFICATION TEST COMPLETED ===\n";
    echo "✅ Attendance verification system analysis complete!\n";
    echo "\nKey Findings:\n";
    echo "🔧 BUTTONS: Verify buttons show when status='present' AND verification_status='pending'\n";
    echo "🔧 BULK: Bulk verification route and method implemented\n";
    echo "🔧 CERTIFICATE: Certificate service available with fallback design\n";
    echo "🔧 DOWNLOAD: Certificate download works for verified attendances\n";
    echo "\nExpected Behavior:\n";
    echo "✅ Pending attendances → Show verify/reject buttons\n";
    echo "✅ Verified/rejected attendances → Show detail button\n";
    echo "✅ Bulk verification → Select multiple and verify all\n";
    echo "✅ Certificate download → Available after verification\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
