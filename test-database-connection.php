<?php

echo "=== TESTING DATABASE CONNECTION AND DATA ===\n\n";

try {
    // Test database connection
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected successfully\n\n";
    
    // 1. Check users table
    echo "👥 USERS WITH KETUA_UKM ROLE:\n";
    $stmt = $pdo->query("SELECT id, name, email, role FROM users WHERE role = 'ketua_ukm'");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "❌ No users with ketua_ukm role found!\n";
    } else {
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Name: {$user['name']}, Email: {$user['email']}\n";
        }
    }
    
    // 2. Check UKMs table
    echo "\n🏢 UKM DATA:\n";
    $stmt = $pdo->query("SELECT id, name, leader_id FROM ukms");
    $ukms = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($ukms as $ukm) {
        echo "- UKM ID: {$ukm['id']}, Name: {$ukm['name']}, Leader ID: " . ($ukm['leader_id'] ?: 'NULL') . "\n";
    }
    
    // 3. Check ukm_members table
    echo "\n📋 UKM MEMBERS DATA:\n";
    $stmt = $pdo->query("
        SELECT 
            um.id,
            um.ukm_id,
            um.user_id,
            um.status,
            u.name as user_name,
            ukm.name as ukm_name
        FROM ukm_members um
        LEFT JOIN users u ON um.user_id = u.id
        LEFT JOIN ukms ukm ON um.ukm_id = ukm.id
        ORDER BY um.created_at DESC
        LIMIT 10
    ");
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($members)) {
        echo "❌ No ukm_members data found!\n";
    } else {
        foreach ($members as $member) {
            echo "- Member ID: {$member['id']}, UKM: {$member['ukm_name']}, User: {$member['user_name']}, Status: {$member['status']}\n";
        }
    }
    
    // 4. Specific check for UKM Cendol
    echo "\n🎯 UKM CENDOL SPECIFIC CHECK:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id as ukm_id,
            ukm.name as ukm_name,
            ukm.leader_id,
            u.name as leader_name,
            u.email as leader_email
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
        WHERE ukm.name = 'UKM Cendol'
    ");
    $cendol = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($cendol) {
        echo "UKM Cendol found:\n";
        echo "- UKM ID: {$cendol['ukm_id']}\n";
        echo "- Leader ID: " . ($cendol['leader_id'] ?: 'NULL') . "\n";
        echo "- Leader Name: " . ($cendol['leader_name'] ?: 'NOT FOUND') . "\n";
        echo "- Leader Email: " . ($cendol['leader_email'] ?: 'N/A') . "\n";
        
        // Check members for UKM Cendol
        $stmt = $pdo->prepare("
            SELECT 
                um.status,
                u.name as user_name,
                u.email as user_email
            FROM ukm_members um
            JOIN users u ON um.user_id = u.id
            WHERE um.ukm_id = ?
        ");
        $stmt->execute([$cendol['ukm_id']]);
        $cendolMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\nUKM Cendol Members:\n";
        if (empty($cendolMembers)) {
            echo "- No members found\n";
        } else {
            foreach ($cendolMembers as $member) {
                echo "- {$member['user_name']} ({$member['user_email']}) - Status: {$member['status']}\n";
            }
        }
    } else {
        echo "❌ UKM Cendol not found!\n";
    }
    
    // 5. Fix leader assignments if needed
    echo "\n🔧 FIXING LEADER ASSIGNMENTS:\n";
    
    // Get correct user IDs
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $arasId = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $rehanId = $stmt->fetchColumn();
    
    if ($arasId) {
        $stmt = $pdo->prepare("UPDATE ukms SET leader_id = ? WHERE name = 'UKM Cendol'");
        $stmt->execute([$arasId]);
        echo "✅ Set UKM Cendol leader to Aras (ID: {$arasId})\n";
    } else {
        echo "❌ Aras user not found\n";
    }
    
    if ($rehanId) {
        $stmt = $pdo->prepare("UPDATE ukms SET leader_id = ? WHERE name = 'Sistem informasi'");
        $stmt->execute([$rehanId]);
        echo "✅ Set Sistem Informasi leader to Rehan (ID: {$rehanId})\n";
    } else {
        echo "❌ Rehan user not found\n";
    }
    
    // 6. Final verification
    echo "\n✅ FINAL VERIFICATION:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.name as ukm_name,
            ukm.leader_id,
            u.name as leader_name,
            u.email as leader_email
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
    ");
    $finalCheck = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($finalCheck as $ukm) {
        echo "- {$ukm['ukm_name']} → Leader: " . ($ukm['leader_name'] ?: 'NONE') . " (ID: {$ukm['leader_id']})\n";
    }
    
    echo "\n🎯 NOW TRY:\n";
    echo "1. Login as Aras: <EMAIL>\n";
    echo "2. Go to: http://localhost:8000/ketua-ukm/members\n";
    echo "3. You should see debug info and pending members\n";
    
} catch (Exception $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
    echo "\nCheck your database connection settings:\n";
    echo "- Host: 127.0.0.1\n";
    echo "- Database: ukmwebv\n";
    echo "- Username: root\n";
    echo "- Password: (empty)\n";
}
