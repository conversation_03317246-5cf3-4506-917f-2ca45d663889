<?php

echo "=== TESTING COLUMN FIX ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing Event model and columns...\n";
    
    // Test Event model
    $event = \App\Models\Event::first();
    if ($event) {
        echo "   Found event: {$event->title}\n";
        echo "   start_datetime: {$event->start_datetime}\n";
        echo "   end_datetime: {$event->end_datetime}\n";
        echo "   ✅ Event model columns are correct\n";
    } else {
        echo "   ⚠️  No events found\n";
    }
    
    echo "2. Testing UKM events relationship...\n";
    
    $ukm = \App\Models\Ukm::first();
    if ($ukm) {
        echo "   Found UKM: {$ukm->name}\n";
        
        // Test events relationship with correct column
        try {
            $totalEvents = $ukm->events()->count();
            echo "   Total events: {$totalEvents}\n";
            echo "   ✅ UKM events relationship works\n";
        } catch (Exception $e) {
            echo "   ❌ UKM events relationship error: " . $e->getMessage() . "\n";
        }
        
        // Test upcoming events with correct column
        try {
            $upcomingEvents = $ukm->events()->where('start_datetime', '>', now())->count();
            echo "   Upcoming events: {$upcomingEvents}\n";
            echo "   ✅ Upcoming events query works\n";
        } catch (Exception $e) {
            echo "   ❌ Upcoming events query error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "3. Testing dashboard stats calculation...\n";
    
    $ketuaUkm = \App\Models\User::where('role', 'ketua_ukm')->first();
    if ($ketuaUkm) {
        echo "   Found ketua UKM: {$ketuaUkm->name}\n";
        
        // Assign UKM for testing
        $ukm = \App\Models\Ukm::first();
        if ($ukm) {
            $originalLeader = $ukm->leader_id;
            $ukm->update(['leader_id' => $ketuaUkm->id]);
            $ukm->refresh();
            
            echo "   Assigned {$ketuaUkm->name} to {$ukm->name}\n";
            
            // Test dashboard stats calculation
            try {
                $leadingUkms = $ketuaUkm->getLeadingUkms();
                $stats = [
                    'total_ukms' => $leadingUkms->count(),
                    'total_members' => 0,
                    'total_events' => 0,
                    'upcoming_events' => 0,
                ];

                foreach ($leadingUkms as $ukm) {
                    $stats['total_members'] += $ukm->activeMembers()->count();
                    $stats['total_events'] += $ukm->events()->count();
                    $stats['upcoming_events'] += $ukm->events()->where('start_datetime', '>', now())->count();
                }
                
                echo "   Dashboard stats:\n";
                echo "     total_ukms: {$stats['total_ukms']}\n";
                echo "     total_members: {$stats['total_members']}\n";
                echo "     total_events: {$stats['total_events']}\n";
                echo "     upcoming_events: {$stats['upcoming_events']}\n";
                echo "   ✅ Dashboard stats calculation works without column errors\n";
                
            } catch (Exception $e) {
                echo "   ❌ Dashboard stats error: " . $e->getMessage() . "\n";
            }
            
            // Revert assignment
            $ukm->update(['leader_id' => $originalLeader]);
            echo "   Reverted assignment\n";
        }
    }
    
    echo "4. Testing Event creation fields...\n";
    
    $eventData = [
        'title' => 'Test Event',
        'slug' => 'test-event',
        'description' => 'Test event description',
        'start_datetime' => now()->addDays(1),
        'end_datetime' => now()->addDays(1)->addHours(2),
        'location' => 'Test Location',
        'type' => 'workshop',
        'ukm_id' => 1,
        'status' => 'draft',
    ];
    
    try {
        // Test if we can create event with correct fields
        $testEvent = \App\Models\Event::create($eventData);
        echo "   Test event created with ID: {$testEvent->id}\n";
        echo "   start_datetime: {$testEvent->start_datetime}\n";
        echo "   end_datetime: {$testEvent->end_datetime}\n";
        echo "   ✅ Event creation with correct columns works\n";
        
        // Clean up
        $testEvent->delete();
        echo "   Test event deleted\n";
        
    } catch (Exception $e) {
        echo "   ❌ Event creation error: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== TESTING COMPLETED ===\n";
    echo "✅ Column error 'Unknown column start_date' should be fixed!\n";
    echo "All queries now use correct column names: start_datetime, end_datetime\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
