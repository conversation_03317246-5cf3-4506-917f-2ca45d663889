<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Event;
use App\Models\EventRegistration;
use App\Models\User;

echo "=== TESTING REAL REGISTRATION WITH APPROVAL ===\n";

echo "1. Finding event and student...\n";

$event = Event::where('slug', 'bukber')->first();
if (!$event) {
    echo "   ❌ Event 'bukber' not found\n";
    exit;
}

$student = User::where('role', 'student')->first();
if (!$student) {
    echo "   ❌ Student not found\n";
    exit;
}

echo "   ✅ Event: {$event->title}\n";
echo "   ✅ Student: {$student->name}\n";
echo "   Event requires approval: " . ($event->requires_approval ? 'Yes' : 'No') . "\n";

echo "2. Checking existing registration...\n";

$existingRegistration = $event->registrations()->where('user_id', $student->id)->first();
if ($existingRegistration) {
    echo "   Found existing registration with status: {$existingRegistration->status}\n";
    echo "   Deleting existing registration for clean test...\n";
    $existingRegistration->delete();
}

echo "3. Testing registration process...\n";

// Simulate the registration logic from EventController
$registrationStatus = $event->requires_approval ? 'pending' : 'approved';
$approvedAt = $event->requires_approval ? null : now();
$approvedBy = $event->requires_approval ? null : null;

$registration = EventRegistration::create([
    'user_id' => $student->id,
    'event_id' => $event->id,
    'status' => $registrationStatus,
    'motivation' => 'Test registration with approval logic',
    'availability_form' => ['full_attendance' => 'yes'],
    'registration_notes' => 'Testing approval system',
    'additional_data' => ['phone' => '081234567890'],
    'payment_status' => 'verified',
    'approved_at' => $approvedAt,
    'approved_by' => $approvedBy,
]);

echo "   ✅ Registration created\n";
echo "   Status: {$registration->status}\n";
echo "   Approved at: " . ($registration->approved_at ? $registration->approved_at->format('Y-m-d H:i:s') : 'null') . "\n";

echo "4. Testing participant count logic...\n";

$participantsBefore = $event->current_participants;
echo "   Participants before: {$participantsBefore}\n";

// Apply the logic from EventController
if (!$event->requires_approval) {
    $event->updateParticipantCount();
}

$event->refresh();
$participantsAfter = $event->current_participants;
echo "   Participants after: {$participantsAfter}\n";

if ($event->requires_approval && $participantsBefore == $participantsAfter) {
    echo "   ✅ Participant count NOT updated (correct for approval required)\n";
} elseif (!$event->requires_approval && $participantsAfter > $participantsBefore) {
    echo "   ✅ Participant count updated (correct for auto-approval)\n";
} else {
    echo "   ⚠️  Unexpected participant count behavior\n";
}

echo "5. Testing view logic...\n";

// Test the logic from EventController show method
$userRegistration = $event->registrations()->where('user_id', $student->id)->first();
$isRegistered = $userRegistration && in_array($userRegistration->status, ['approved', 'pending']);
$canRegister = $event->isRegistrationOpen() && !$isRegistered;

echo "   User has registration: " . ($userRegistration ? 'Yes' : 'No') . "\n";
echo "   Registration status: " . ($userRegistration ? $userRegistration->status : 'N/A') . "\n";
echo "   Is considered registered: " . ($isRegistered ? 'Yes' : 'No') . "\n";
echo "   Can register again: " . ($canRegister ? 'Yes' : 'No') . "\n";

echo "6. Testing message logic...\n";

if ($event->requires_approval) {
    $message = 'Pendaftaran berhasil dikirim! Menunggu persetujuan dari ketua UKM.';
} else {
    $message = 'Pendaftaran berhasil! Anda telah terdaftar untuk kegiatan ini.';
}

echo "   Expected message: {$message}\n";

echo "7. URLs to test in browser...\n";
echo "   Event detail: http://localhost:8000/kegiatan/{$event->slug}\n";
echo "   Ketua UKM registrations: http://localhost:8000/ketua-ukm/events/{$event->slug}/registrations\n";

echo "\n=== REAL REGISTRATION TEST COMPLETED ===\n";
echo "✅ Registration logic working correctly!\n";

if ($event->requires_approval) {
    echo "\n📋 NEXT STEPS:\n";
    echo "1. Visit event detail page to see 'Menunggu Persetujuan' status\n";
    echo "2. Visit ketua UKM registrations page to approve the registration\n";
    echo "3. Check that participant count updates after approval\n";
}

?>
