<?php
/**
 * Test UKM registration button logic
 */

echo "=== TESTING UKM REGISTRATION LOGIC ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing UKM and user data...\n";
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    if (!$ukm) {
        echo "   ❌ IMMA UKM not found\n";
        exit;
    }
    
    echo "   ✅ Found IMMA UKM (ID: {$ukm->id})\n";
    echo "   Status: {$ukm->status}\n";
    echo "   Registration Status: " . ($ukm->registration_status ?? 'open') . "\n";
    echo "   Current/Max Members: {$ukm->current_members}/{$ukm->max_members}\n";
    
    // Find a student user
    $student = \App\Models\User::where('role', 'student')->first();
    if (!$student) {
        echo "   ❌ No student user found\n";
        exit;
    }
    
    echo "   ✅ Found student: {$student->name} (ID: {$student->id})\n";
    
    echo "\n2. Testing different membership statuses...\n";
    
    // Test scenarios
    $scenarios = [
        'no_membership' => 'No membership record',
        'pending' => 'Pending application',
        'active' => 'Active member',
        'inactive' => 'Inactive member',
        'alumni' => 'Alumni member'
    ];
    
    foreach ($scenarios as $status => $description) {
        echo "\n   Testing scenario: $description\n";
        
        // Clear existing membership
        $ukm->members()->detach($student->id);
        
        // Create membership based on scenario
        if ($status !== 'no_membership') {
            $ukm->members()->attach($student->id, [
                'role' => 'member',
                'status' => $status,
                'joined_date' => $status === 'active' ? now() : null,
                'applied_at' => now(),
            ]);
        }
        
        // Test controller logic
        $membership = $ukm->members()->where('ukm_members.user_id', $student->id)->first();
        $membershipStatus = $membership ? $membership->pivot->status : null;
        
        echo "      Membership status: " . ($membershipStatus ?: 'null') . "\n";
        
        // Determine expected button behavior
        if ($membershipStatus === 'active') {
            echo "      Expected: Show 'Sudah Bergabung' status\n";
        } elseif ($membershipStatus === 'pending') {
            echo "      Expected: Show 'Menunggu Approval' status (button disabled)\n";
        } elseif ($membershipStatus === 'inactive' || $membershipStatus === 'alumni') {
            echo "      Expected: Show 'Daftar Ulang' button\n";
        } else {
            echo "      Expected: Show 'Daftar Keanggotaan' button\n";
        }
    }
    
    echo "\n3. Creating test HTML for registration logic...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Registration Logic Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            background: #f9f9f9; 
        }
        .scenario { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 10px 0; 
            background: white; 
        }
        .status-active { background: #d4edda; color: #155724; padding: 8px 12px; border-radius: 4px; }
        .status-pending { background: #fff3cd; color: #856404; padding: 8px 12px; border-radius: 4px; }
        .status-rejected { background: #f8d7da; color: #721c24; padding: 8px 12px; border-radius: 4px; }
        .btn-register { background: #007bff; color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; }
        .btn-reregister { background: #28a745; color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; }
        .btn-disabled { background: #6c757d; color: white; padding: 8px 16px; border-radius: 4px; cursor: not-allowed; }
    </style>
</head>
<body>
    <h1>🔧 UKM Registration Logic Test</h1>
    
    <div class='test-section'>
        <h2>📋 Registration Button Logic</h2>
        <p>This page demonstrates how the registration button should behave based on membership status:</p>";
    
    foreach ($scenarios as $status => $description) {
        $testHtml .= "
        <div class='scenario'>
            <h4>Scenario: $description</h4>
            <p><strong>Status:</strong> " . ($status === 'no_membership' ? 'No membership' : $status) . "</p>";
        
        if ($status === 'active') {
            $testHtml .= "<p><strong>Button:</strong> <span class='status-active'>✅ Sudah Bergabung</span></p>";
        } elseif ($status === 'pending') {
            $testHtml .= "<p><strong>Button:</strong> <span class='status-pending'>⏳ Menunggu Approval</span> (disabled)</p>";
        } elseif ($status === 'rejected' || $status === 'inactive') {
            $testHtml .= "<p><strong>Button:</strong> <a href='#' class='btn-reregister'>🔄 Daftar Ulang</a></p>";
        } else {
            $testHtml .= "<p><strong>Button:</strong> <a href='#' class='btn-register'>➕ Daftar Keanggotaan</a></p>";
        }
        
        $testHtml .= "</div>";
    }
    
    $testHtml .= "
    </div>
    
    <div class='test-section'>
        <h2>🔗 Test Links</h2>
        <ul>
            <li><a href='http://127.0.0.1:8000/ukms/imma' target='_blank'><strong>IMMA UKM Detail</strong> - Test registration button logic</a></li>
            <li><a href='http://127.0.0.1:8000/ukms' target='_blank'><strong>All UKMs</strong> - Test registration buttons in cards</a></li>
            <li><a href='http://127.0.0.1:8000/login' target='_blank'><strong>Login</strong> - Login as student to test registration</a></li>
        </ul>
    </div>
    
    <div class='test-section'>
        <h2>✅ Expected Behavior</h2>
        <ul>
            <li><strong>No membership:</strong> Show 'Daftar Keanggotaan' button</li>
            <li><strong>Pending status:</strong> Show 'Menunggu Approval' status (button disabled)</li>
            <li><strong>Active status:</strong> Show 'Sudah Bergabung' status</li>
            <li><strong>Rejected/Inactive:</strong> Show 'Daftar Ulang' button</li>
        </ul>
    </div>
    
    <div class='test-section'>
        <h2>🧪 Test Instructions</h2>
        <ol>
            <li>Login as a student user</li>
            <li>Visit IMMA UKM detail page</li>
            <li>Check registration button behavior</li>
            <li>Submit registration and verify 'Menunggu Approval' status appears</li>
            <li>Admin can approve/reject from admin panel</li>
        </ol>
    </div>
</body>
</html>";
    
    file_put_contents(public_path('registration-logic-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/registration-logic-test.html\n";
    
    echo "\n4. Setting up test membership for demonstration...\n";
    
    // Create a pending membership for demonstration
    $ukm->members()->detach($student->id);
    $ukm->members()->attach($student->id, [
        'role' => 'member',
        'status' => 'pending',
        'applied_at' => now(),
        'previous_experience' => 'Test experience',
        'skills_interests' => 'Test skills',
        'reason_joining' => 'Test reason',
        'preferred_division' => 'Test division',
    ]);
    
    echo "   ✅ Created pending membership for {$student->name} in {$ukm->name}\n";
    echo "   📋 Student can now see 'Menunggu Approval' status\n";
    
    echo "\n=== REGISTRATION LOGIC TEST COMPLETED ===\n";
    echo "🔗 Test page: http://127.0.0.1:8000/registration-logic-test.html\n";
    echo "🔗 IMMA UKM: http://127.0.0.1:8000/ukms/imma\n";
    echo "📋 Login as student to test: {$student->email}\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
