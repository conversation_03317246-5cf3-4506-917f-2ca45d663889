<?php

echo "=== FIXING REGISTER SUCCESS ROUTE ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking register.success route...\n";
    
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    $registerSuccessRoute = $routes->getByName('register.success');
    
    if ($registerSuccessRoute) {
        echo "   ✅ Route 'register.success' exists\n";
        echo "   URI: {$registerSuccessRoute->uri()}\n";
        echo "   Method: " . implode(', ', $registerSuccessRoute->methods()) . "\n";
        echo "   Action: {$registerSuccessRoute->getActionName()}\n";
    } else {
        echo "   ❌ Route 'register.success' not found\n";
    }
    
    echo "\n2. Checking RegisteredUserController...\n";
    
    $controller = new \App\Http\Controllers\Auth\RegisteredUserController();
    
    if (method_exists($controller, 'success')) {
        echo "   ✅ Method 'success' exists in RegisteredUserController\n";
    } else {
        echo "   ❌ Method 'success' missing in RegisteredUserController\n";
    }
    
    echo "\n3. Checking register-success view...\n";
    
    $viewPath = resource_path('views/auth/register-success.blade.php');
    
    if (file_exists($viewPath)) {
        echo "   ✅ View 'auth.register-success' exists\n";
        echo "   Path: {$viewPath}\n";
    } else {
        echo "   ❌ View 'auth.register-success' missing\n";
    }
    
    echo "\n4. Testing route generation...\n";
    
    try {
        $url = route('register.success');
        echo "   ✅ Route URL generated: {$url}\n";
    } catch (Exception $e) {
        echo "   ❌ Route generation failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n5. Checking all auth routes...\n";
    
    $authRoutes = [
        'login' => 'GET /login',
        'register' => 'GET /register',
        'register.success' => 'GET /register/success',
        'logout' => 'POST /logout',
    ];
    
    foreach ($authRoutes as $routeName => $expectedRoute) {
        $route = $routes->getByName($routeName);
        if ($route) {
            echo "   ✅ {$routeName}: {$route->uri()}\n";
        } else {
            echo "   ❌ {$routeName}: NOT FOUND\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== ROUTE CHECK COMPLETED ===\n";
echo "\nIf route still not found, try:\n";
echo "1. php artisan route:clear\n";
echo "2. php artisan config:clear\n";
echo "3. php artisan cache:clear\n";
echo "4. Check web.php for syntax errors\n";
echo "5. Restart server\n";
