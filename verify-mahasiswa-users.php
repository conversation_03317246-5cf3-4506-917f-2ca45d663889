<?php
echo "=== VERIFYING MAHASISWA USERS ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check total users
    echo "📊 User Statistics:\n";
    $result = $pdo->query("SELECT COUNT(*) as total FROM users");
    $totalUsers = $result->fetch(PDO::FETCH_ASSOC)['total'];
    echo "   Total Users: {$totalUsers}\n";
    
    // Check by role
    $result = $pdo->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   {$row['role']}: {$row['count']} users\n";
    }
    
    echo "\n📋 Student Users:\n";
    $result = $pdo->query("SELECT name, email, nim, status FROM users WHERE role = 'student' ORDER BY name");
    $studentCount = 0;

    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $status = $row['status'] == 'active' ? '✅' : '❌';
        echo "   {$status} {$row['name']}\n";
        echo "      📧 {$row['email']}\n";
        echo "      🆔 {$row['nim']}\n\n";
        $studentCount++;
    }

    if ($studentCount == 0) {
        echo "   ❌ No student users found!\n\n";
        echo "📋 To create student users:\n";
        echo "1. Open phpMyAdmin: http://localhost/phpmyadmin\n";
        echo "2. Select database 'ukmwebv'\n";
        echo "3. Go to SQL tab\n";
        echo "4. Copy content from 'mahasiswa-users-phpmyadmin.sql'\n";
        echo "5. Paste and execute\n\n";
    } else {
        echo "✅ Found {$studentCount} student users\n\n";

        echo "🔑 Login Information:\n";
        echo "   Password for all students: pass123123\n";
        echo "   Login URL: http://localhost:8000/login\n\n";

        echo "📱 Sample Login:\n";
        $result = $pdo->query("SELECT email FROM users WHERE role = 'student' LIMIT 1");
        $sampleUser = $result->fetch(PDO::FETCH_ASSOC);
        if ($sampleUser) {
            echo "   Email: {$sampleUser['email']}\n";
            echo "   Password: pass123123\n\n";
        }
        
        echo "🌐 Test URLs:\n";
        echo "   Login: http://localhost:8000/login\n";
        echo "   Dashboard: http://localhost:8000/dashboard\n";
        echo "   UKM List: http://localhost:8000/ukm\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== VERIFICATION COMPLETE ===\n";
?>
