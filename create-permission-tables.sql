-- CREATE SPATIE PERMISSION TABLES MANUALLY
-- Untuk mengatasi error table 'roles' tidak ditemukan

-- 1. Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    guard_name varchar(255) NOT NULL,
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY permissions_name_guard_name_unique (name, guard_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Create roles table
CREATE TABLE IF NOT EXISTS roles (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    guard_name varchar(255) NOT NULL,
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY roles_name_guard_name_unique (name, guard_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Create model_has_permissions table
CREATE TABLE IF NOT EXISTS model_has_permissions (
    permission_id bigint unsigned NOT NULL,
    model_type varchar(255) NOT NULL,
    model_id bigint unsigned NOT NULL,
    PRIMARY KEY (permission_id, model_id, model_type),
    KEY model_has_permissions_model_id_model_type_index (model_id, model_type),
    CONSTRAINT model_has_permissions_permission_id_foreign 
        FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Create model_has_roles table
CREATE TABLE IF NOT EXISTS model_has_roles (
    role_id bigint unsigned NOT NULL,
    model_type varchar(255) NOT NULL,
    model_id bigint unsigned NOT NULL,
    PRIMARY KEY (role_id, model_id, model_type),
    KEY model_has_roles_model_id_model_type_index (model_id, model_type),
    CONSTRAINT model_has_roles_role_id_foreign 
        FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Create role_has_permissions table
CREATE TABLE IF NOT EXISTS role_has_permissions (
    permission_id bigint unsigned NOT NULL,
    role_id bigint unsigned NOT NULL,
    PRIMARY KEY (permission_id, role_id),
    CONSTRAINT role_has_permissions_permission_id_foreign 
        FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE,
    CONSTRAINT role_has_permissions_role_id_foreign 
        FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Insert basic roles
INSERT IGNORE INTO roles (name, guard_name, created_at, updated_at) VALUES
('admin', 'web', NOW(), NOW()),
('student', 'web', NOW(), NOW()),
('ketua_ukm', 'web', NOW(), NOW());

-- 7. Insert basic permissions
INSERT IGNORE INTO permissions (name, guard_name, created_at, updated_at) VALUES
('manage users', 'web', NOW(), NOW()),
('manage ukms', 'web', NOW(), NOW()),
('manage events', 'web', NOW(), NOW()),
('view dashboard', 'web', NOW(), NOW()),
('edit profile', 'web', NOW(), NOW());

-- 8. Assign permissions to admin role
INSERT IGNORE INTO role_has_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'admin' AND r.guard_name = 'web';

-- 9. Assign basic permissions to student role
INSERT IGNORE INTO role_has_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'student' AND r.guard_name = 'web' 
AND p.name IN ('view dashboard', 'edit profile');

-- 10. Assign UKM management permissions to ketua_ukm role
INSERT IGNORE INTO role_has_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'ketua_ukm' AND r.guard_name = 'web' 
AND p.name IN ('manage ukms', 'manage events', 'view dashboard', 'edit profile');

-- 11. Assign roles to existing users
INSERT IGNORE INTO model_has_roles (role_id, model_type, model_id)
SELECT r.id, 'App\\Models\\User', u.id
FROM roles r, users u
WHERE r.name = u.role AND r.guard_name = 'web';

-- Verify tables created
SELECT 'permissions' as table_name, COUNT(*) as count FROM permissions
UNION ALL
SELECT 'roles' as table_name, COUNT(*) as count FROM roles
UNION ALL
SELECT 'model_has_roles' as table_name, COUNT(*) as count FROM model_has_roles;

-- Show role assignments
SELECT u.name, u.email, r.name as role_name
FROM users u
LEFT JOIN model_has_roles mhr ON mhr.model_id = u.id AND mhr.model_type = 'App\\Models\\User'
LEFT JOIN roles r ON r.id = mhr.role_id
ORDER BY u.id;
