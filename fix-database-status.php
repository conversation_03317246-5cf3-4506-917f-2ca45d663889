<?php

echo "=== FIXING DATABASE STATUS COLUMN ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check current status column
    echo "📋 CURRENT STATUS COLUMN:\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM ukm_members LIKE 'status'");
    $statusColumn = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($statusColumn) {
        echo "Type: {$statusColumn['Type']}\n";
        echo "Null: {$statusColumn['Null']}\n";
        echo "Default: {$statusColumn['Default']}\n\n";
        
        if (strpos($statusColumn['Type'], 'enum') !== false) {
            echo "⚠️  ENUM detected! This is causing the truncation error.\n";
            echo "Current enum values: {$statusColumn['Type']}\n\n";
            
            echo "🔧 FIXING: Converting ENUM to VARCHAR...\n";
            $pdo->exec("ALTER TABLE ukm_members MODIFY COLUMN status VARCHAR(20) DEFAULT 'pending'");
            echo "✅ Status column converted to VARCHAR(20)\n\n";
            
        } else {
            echo "✅ Already VARCHAR/TEXT type\n\n";
        }
    }
    
    // Verify the fix
    echo "📋 AFTER FIX:\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM ukm_members LIKE 'status'");
    $statusColumn = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($statusColumn) {
        echo "Type: {$statusColumn['Type']}\n";
        echo "Null: {$statusColumn['Null']}\n";
        echo "Default: {$statusColumn['Default']}\n\n";
    }
    
    // Test inserting 'rejected' status
    echo "🧪 TESTING 'rejected' STATUS:\n";
    
    // Find a pending member
    $stmt = $pdo->query("SELECT id, user_id, status FROM ukm_members WHERE status = 'pending' LIMIT 1");
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testMember) {
        echo "Found test member ID: {$testMember['id']}\n";
        echo "Current status: {$testMember['status']}\n";
        
        // Test update (but revert immediately)
        $pdo->beginTransaction();
        
        try {
            $stmt = $pdo->prepare("UPDATE ukm_members SET status = 'rejected', rejected_at = NOW(), rejected_by = 1 WHERE id = ?");
            $stmt->execute([$testMember['id']]);
            
            echo "✅ Successfully updated status to 'rejected'\n";
            
            // Check the result
            $stmt = $pdo->prepare("SELECT status FROM ukm_members WHERE id = ?");
            $stmt->execute([$testMember['id']]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "New status: {$result['status']}\n";
            
            // Revert the change
            $pdo->rollback();
            echo "✅ Test completed and reverted\n\n";
            
        } catch (Exception $e) {
            $pdo->rollback();
            echo "❌ Test failed: " . $e->getMessage() . "\n\n";
        }
        
    } else {
        echo "No pending members found for testing\n\n";
    }
    
    echo "🎯 DATABASE FIX COMPLETED!\n";
    echo "Now you can proceed with reject functionality.\n";
    
} catch (Exception $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
}
