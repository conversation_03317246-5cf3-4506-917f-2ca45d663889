<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Ukm;
use Illuminate\Support\Facades\Route;

echo "=== DIAGNOSING EVENT FORM SUBMISSION ISSUE ===\n";

echo "1. Checking route configuration...\n";

// Check if the route exists
$routeName = 'ketua-ukm.events.store';
if (Route::has($routeName)) {
    echo "   ✅ Route exists: {$routeName}\n";
    
    try {
        $url = route($routeName);
        echo "   ✅ Route URL: {$url}\n";
    } catch (\Exception $e) {
        echo "   ❌ Route URL generation failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ❌ Route missing: {$routeName}\n";
}

echo "2. Checking controller method...\n";

$controller = new \App\Http\Controllers\KetuaUkmController();
if (method_exists($controller, 'storeEvent')) {
    echo "   ✅ Controller method exists: storeEvent\n";
} else {
    echo "   ❌ Controller method missing: storeEvent\n";
}

echo "3. Checking form data requirements...\n";

// Check what data the form needs
$requiredFields = [
    'ukm_id' => 'UKM ID (hidden field)',
    'title' => 'Event title',
    'description' => 'Event description',
    'start_datetime' => 'Start date and time',
    'end_datetime' => 'End date and time',
    'location' => 'Event location',
    'type' => 'Event type'
];

echo "   Required fields for form submission:\n";
foreach ($requiredFields as $field => $description) {
    echo "   - {$field}: {$description}\n";
}

echo "4. Testing with sample data...\n";

// Find a ketua UKM user
$ketuaUkm = User::where('role', 'ketua_ukm')->first();
if (!$ketuaUkm) {
    echo "   ❌ No ketua UKM user found\n";
    exit;
}

echo "   ✅ Using ketua UKM: {$ketuaUkm->name}\n";

// Find UKM led by this user
$ukm = Ukm::where('leader_id', $ketuaUkm->id)->first();
if (!$ukm) {
    echo "   ❌ No UKM found for this ketua UKM\n";
    exit;
}

echo "   ✅ Using UKM: {$ukm->name}\n";

echo "5. Simulating form validation...\n";

// Create sample form data
$formData = [
    'ukm_id' => $ukm->id,
    'title' => 'Test Event - ' . date('Y-m-d H:i:s'),
    'description' => 'This is a test event to diagnose form submission issues.',
    'start_datetime' => now()->addDays(7)->format('Y-m-d\TH:i'),
    'end_datetime' => now()->addDays(7)->addHours(2)->format('Y-m-d\TH:i'),
    'location' => 'Test Location',
    'type' => 'workshop',
    'registration_open' => '1',
    'requires_approval' => '1'
];

echo "   Sample form data:\n";
foreach ($formData as $key => $value) {
    echo "   - {$key}: {$value}\n";
}

echo "6. Testing validation rules...\n";

// Test validation rules from controller
$validationRules = [
    'ukm_id' => 'required|exists:ukms,id',
    'title' => 'required|string|max:255',
    'description' => 'required|string',
    'start_datetime' => 'required|date',
    'end_datetime' => 'required|date|after:start_datetime',
    'location' => 'required|string|max:255',
    'type' => 'required|in:workshop,seminar,competition,meeting,social,other',
];

$validator = \Illuminate\Support\Facades\Validator::make($formData, $validationRules);

if ($validator->fails()) {
    echo "   ❌ Validation failed:\n";
    foreach ($validator->errors()->all() as $error) {
        echo "   - {$error}\n";
    }
} else {
    echo "   ✅ Validation passed\n";
}

echo "7. Checking potential issues...\n";

$potentialIssues = [
    'CSRF Token' => 'Form might be missing @csrf directive',
    'Hidden ukm_id' => 'Hidden ukm_id field might be empty',
    'Date Format' => 'datetime-local format might not match validation',
    'File Upload' => 'enctype="multipart/form-data" required for file uploads',
    'JavaScript Validation' => 'Client-side validation might prevent submission',
    'Server Error' => 'Controller might throw exception without proper error handling'
];

echo "   Potential issues to check:\n";
foreach ($potentialIssues as $issue => $description) {
    echo "   - {$issue}: {$description}\n";
}

echo "8. Checking form view file...\n";

$formViewPath = resource_path('views/ketua-ukm/events/create.blade.php');
if (file_exists($formViewPath)) {
    echo "   ✅ Form view exists\n";
    
    $formContent = file_get_contents($formViewPath);
    
    // Check for common issues
    $checks = [
        '@csrf' => 'CSRF token directive',
        'method="POST"' => 'POST method',
        'enctype="multipart/form-data"' => 'File upload support',
        'name="ukm_id"' => 'UKM ID field',
        'route(\'ketua-ukm.events.store\')' => 'Correct form action'
    ];
    
    echo "   Form checks:\n";
    foreach ($checks as $check => $description) {
        $found = strpos($formContent, $check) !== false;
        echo "   " . ($found ? '✅' : '❌') . " {$description}: " . ($found ? 'Found' : 'Missing') . "\n";
    }
} else {
    echo "   ❌ Form view file not found\n";
}

echo "9. Debugging suggestions...\n";

echo "   🔍 TO DEBUG THE ISSUE:\n";
echo "   1. Check browser Network tab when submitting form\n";
echo "   2. Look for HTTP status code (200, 302, 422, 500)\n";
echo "   3. Check if form data is being sent correctly\n";
echo "   4. Look for JavaScript errors in Console\n";
echo "   5. Check Laravel logs: storage/logs/laravel.log\n";
echo "   6. Enable debug mode in .env: APP_DEBUG=true\n";

echo "10. Quick fixes to try...\n";

echo "   🔧 QUICK FIXES:\n";
echo "   1. Ensure ukm_id hidden field has value: value=\"{{ \$ukm ? \$ukm->id : '' }}\"\n";
echo "   2. Check if user is authenticated and has ketua_ukm role\n";
echo "   3. Verify UKM exists and user is the leader\n";
echo "   4. Check if all required fields are filled\n";
echo "   5. Look for validation errors in session\n";

echo "\n=== DIAGNOSIS COMPLETED ===\n";
echo "✅ Route and controller exist\n";
echo "✅ Validation rules are reasonable\n";
echo "✅ Sample data passes validation\n";

echo "\nNEXT STEPS:\n";
echo "1. Open browser developer tools\n";
echo "2. Go to Network tab\n";
echo "3. Submit the form\n";
echo "4. Check what HTTP response is received\n";
echo "5. Look for any JavaScript errors\n";
echo "6. Check Laravel logs for any server errors\n";

?>
