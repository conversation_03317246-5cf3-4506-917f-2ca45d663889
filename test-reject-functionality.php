<?php

echo "=== TESTING REJECT FUNCTIONALITY ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Test if we can update status to 'rejected'
    echo "🧪 TESTING STATUS UPDATE TO 'rejected':\n";
    
    // Find a pending member to test with
    $stmt = $pdo->query("SELECT id, user_id, ukm_id, status FROM ukm_members WHERE status = 'pending' LIMIT 1");
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testMember) {
        echo "Found test member: ID {$testMember['id']}, Status: {$testMember['status']}\n";
        
        // Try to update status to rejected (but don't actually do it)
        $sql = "UPDATE ukm_members SET status = 'rejected', rejected_at = NOW(), rejected_by = 1 WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        
        echo "SQL to execute: {$sql}\n";
        echo "Parameters: [{$testMember['id']}]\n";
        
        // Actually execute the test (comment out if you don't want to change data)
        // $stmt->execute([$testMember['id']]);
        // echo "✅ Status update successful!\n";
        
        echo "⚠️  Test prepared but not executed. Uncomment line above to actually test.\n";
        
    } else {
        echo "❌ No pending members found to test with\n";
    }
    
    // Show current schema
    echo "\n📋 CURRENT STATUS COLUMN SCHEMA:\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM ukm_members LIKE 'status'");
    $statusColumn = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($statusColumn) {
        echo "Type: {$statusColumn['Type']}\n";
        echo "Null: {$statusColumn['Null']}\n";
        echo "Default: {$statusColumn['Default']}\n";
        
        if (strpos($statusColumn['Type'], 'enum') !== false) {
            echo "⚠️  ENUM detected! Make sure 'rejected' is included.\n";
            echo "Current enum values: {$statusColumn['Type']}\n";
        } else {
            echo "✅ VARCHAR/TEXT type - should accept 'rejected'\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎯 NEXT STEPS:\n";
echo "1. Run the SQL fix in phpMyAdmin\n";
echo "2. Test the reject functionality in the web interface\n";
echo "3. Check if the error is resolved\n";
