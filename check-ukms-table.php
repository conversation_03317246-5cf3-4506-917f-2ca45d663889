<?php

echo "=== CHECKING UKMS TABLE STRUCTURE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check if ukms table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'ukms'");
    if ($stmt->rowCount() == 0) {
        echo "❌ Table 'ukms' does not exist\n";
        exit;
    }
    
    echo "✅ Table 'ukms' exists\n\n";
    
    // Show table structure
    echo "📋 CURRENT TABLE STRUCTURE:\n";
    $stmt = $pdo->query("DESCRIBE ukms");
    $columns = $stmt->fetchAll();
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "- {$column['Field']} ({$column['Type']}) - {$column['Null']} - {$column['Key']} - {$column['Default']}\n";
    }
    
    // Check for missing columns that are needed
    $requiredColumns = [
        'registration_status' => "ENUM('open', 'closed') DEFAULT 'open'",
        'is_recruiting' => "BOOLEAN DEFAULT true",
        'max_members' => "INT DEFAULT NULL",
        'current_members' => "INT DEFAULT 0",
        'contact_info' => "JSON DEFAULT NULL",
        'meeting_schedule' => "VARCHAR(255) DEFAULT NULL",
        'meeting_location' => "VARCHAR(255) DEFAULT NULL",
        'requirements' => "TEXT DEFAULT NULL",
        'vision' => "TEXT DEFAULT NULL",
        'mission' => "TEXT DEFAULT NULL",
        'category' => "VARCHAR(255) DEFAULT NULL",
        'banner' => "VARCHAR(255) DEFAULT NULL"
    ];
    
    echo "\n🔍 CHECKING REQUIRED COLUMNS:\n";
    $missingColumns = [];
    
    foreach ($requiredColumns as $column => $definition) {
        if (in_array($column, $existingColumns)) {
            echo "✅ Column '{$column}' exists\n";
        } else {
            echo "❌ Column '{$column}' is MISSING\n";
            $missingColumns[$column] = $definition;
        }
    }
    
    // Add missing columns
    if (!empty($missingColumns)) {
        echo "\n🔧 ADDING MISSING COLUMNS:\n";
        
        foreach ($missingColumns as $column => $definition) {
            try {
                $sql = "ALTER TABLE ukms ADD COLUMN {$column} {$definition}";
                $pdo->exec($sql);
                echo "✅ Added column: {$column}\n";
            } catch (Exception $e) {
                echo "❌ Error adding {$column}: " . $e->getMessage() . "\n";
            }
        }
    } else {
        echo "\n✅ All required columns exist!\n";
    }
    
    // Show updated structure
    echo "\n📋 UPDATED TABLE STRUCTURE:\n";
    $stmt = $pdo->query("DESCRIBE ukms");
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) - Default: {$column['Default']}\n";
    }
    
    // Check current data
    echo "\n📊 CURRENT DATA:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ukms");
    $count = $stmt->fetchColumn();
    echo "Total UKMs: {$count}\n";
    
    if ($count > 0) {
        echo "\nExisting UKMs:\n";
        $stmt = $pdo->query("SELECT id, name, slug, status FROM ukms LIMIT 5");
        $ukms = $stmt->fetchAll();
        
        foreach ($ukms as $ukm) {
            echo "- ID: {$ukm['id']}, Name: {$ukm['name']}, Status: {$ukm['status']}\n";
        }
    }
    
    echo "\n✅ UKMs table structure check complete!\n";
    echo "🎯 Admin edit UKM should now work without column errors.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
