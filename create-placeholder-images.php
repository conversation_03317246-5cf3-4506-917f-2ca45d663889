<?php
// Script untuk membuat placeholder images
$placeholderImages = [
    'storage/app/public/ukm-logos/himsi.png',
    'storage/app/public/ukm-logos/basket.png', 
    'storage/app/public/ukm-logos/musik.png',
    'storage/app/public/ukm-banners/himsi-banner.jpg',
    'storage/app/public/ukm-banners/basket-banner.jpg',
    'storage/app/public/ukm-banners/musik-banner.jpg'
];

foreach ($placeholderImages as $imagePath) {
    $fullPath = __DIR__ . '/' . $imagePath;
    $dir = dirname($fullPath);
    
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    // Create a simple colored rectangle as placeholder
    $width = strpos($imagePath, 'banner') !== false ? 800 : 200;
    $height = strpos($imagePath, 'banner') !== false ? 400 : 200;
    
    $image = imagecreatetruecolor($width, $height);
    $bgColor = imagecolorallocate($image, 59, 130, 246); // Blue color
    $textColor = imagecolorallocate($image, 255, 255, 255); // White color
    
    imagefill($image, 0, 0, $bgColor);
    
    $text = strpos($imagePath, 'himsi') !== false ? 'HIMSI' : 
           (strpos($imagePath, 'basket') !== false ? 'BASKET' : 'MUSIK');
    
    $fontSize = strpos($imagePath, 'banner') !== false ? 5 : 3;
    $textWidth = imagefontwidth($fontSize) * strlen($text);
    $textHeight = imagefontheight($fontSize);
    
    $x = ($width - $textWidth) / 2;
    $y = ($height - $textHeight) / 2;
    
    imagestring($image, $fontSize, $x, $y, $text, $textColor);
    
    if (strpos($imagePath, '.png') !== false) {
        imagepng($image, $fullPath);
    } else {
        imagejpeg($image, $fullPath);
    }
    
    imagedestroy($image);
    echo "Created: $imagePath\n";
}

echo "All placeholder images created successfully!\n";
?>
