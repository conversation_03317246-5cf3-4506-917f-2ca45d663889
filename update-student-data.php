<?php
echo "=== UPDATING STUDENT DATA ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Update students with faculty, major, and batch data
    echo "Updating student data...\n\n";
    
    $updates = [
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Informatika',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Sistem Informasi',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Ekon<PERSON> dan <PERSON>',
            'major' => 'Akuntansi',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Komunikasi dan Bisnis',
            'major' => 'Ilmu Komunikasi',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Informatika',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Komputer',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Sistem Informasi',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Ekonomi dan Bisnis',
            'major' => 'Manajemen',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Informatika',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Komunikasi dan Bisnis',
            'major' => 'Digital Marketing',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Informatika',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Ekonomi dan Bisnis',
            'major' => 'Akuntansi',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Sistem Informasi',
            'batch' => '2023'
        ],
        [
            'email' => '<EMAIL>',
            'faculty' => 'Fakultas Komunikasi dan Bisnis',
            'major' => 'Ilmu Komunikasi',
            'batch' => '2023'
        ]
    ];
    
    $sql = "UPDATE users SET faculty = ?, major = ?, batch = ? WHERE email = ? AND role = 'student'";
    $stmt = $pdo->prepare($sql);
    
    $updated = 0;
    $failed = 0;
    
    foreach ($updates as $update) {
        try {
            if ($stmt->execute([$update['faculty'], $update['major'], $update['batch'], $update['email']])) {
                echo "   ✅ Updated: {$update['email']}\n";
                echo "      🏫 Faculty: {$update['faculty']}\n";
                echo "      📚 Major: {$update['major']}\n";
                echo "      📅 Batch: {$update['batch']}\n\n";
                $updated++;
            } else {
                echo "   ❌ Failed to update: {$update['email']}\n\n";
                $failed++;
            }
        } catch (Exception $e) {
            echo "   ❌ Error updating {$update['email']}: " . $e->getMessage() . "\n\n";
            $failed++;
        }
    }
    
    echo "=== RESULT ===\n";
    echo "✅ Successfully updated: {$updated} students\n";
    echo "❌ Failed: {$failed} students\n";
    
    // Verify the updates
    echo "\n📊 Updated Student Data:\n";
    $result = $pdo->query("SELECT name, email, faculty, major, batch FROM users WHERE role = 'student' AND faculty IS NOT NULL ORDER BY name");
    $count = 0;
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $count++;
        echo "   {$count}. {$row['name']}\n";
        echo "      📧 {$row['email']}\n";
        echo "      🏫 {$row['faculty']}\n";
        echo "      📚 {$row['major']}\n";
        echo "      📅 {$row['batch']}\n\n";
    }
    
    echo "✅ Total students with complete data: {$count}\n";
    
    echo "\n🌐 Test URLs:\n";
    echo "   Admin Dashboard: http://localhost:8000/admin/dashboard\n";
    echo "   Student List: http://localhost:8000/admin/students\n";
    echo "   Login: http://localhost:8000/login\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== UPDATE COMPLETE ===\n";
?>
