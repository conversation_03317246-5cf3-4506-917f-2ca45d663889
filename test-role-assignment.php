<?php

echo "=== TESTING ROLE ASSIGNMENT ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Finding test user...\n";
    
    $testUser = \App\Models\User::where('role', 'student')->first();
    if (!$testUser) {
        echo "   No student user found for testing\n";
        exit;
    }
    
    echo "   Test user: {$testUser->name} ({$testUser->email})\n";
    echo "   Current role: {$testUser->role}\n";
    echo "   Current Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . "\n";
    
    echo "\n2. Simulating admin role change to ketua_ukm...\n";
    
    // Simulate what happens in UserManagementController
    $updateData = [
        'role' => 'ketua_ukm'
    ];
    
    // Update user
    $testUser->update($updateData);
    
    // Sync role with Spatie Permission
    $testUser->syncRoleWithSpatie();
    
    // Clear Spatie permission cache
    app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    
    // Refresh user data
    $testUser->refresh();
    
    echo "   ✅ Role update completed\n";
    
    echo "\n3. Verifying role assignment...\n";
    
    echo "   Role column: {$testUser->role}\n";
    echo "   Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . "\n";
    echo "   hasRole('ketua_ukm'): " . ($testUser->hasRole('ketua_ukm') ? 'true' : 'false') . "\n";
    echo "   isKetuaUkm(): " . ($testUser->isKetuaUkm() ? 'true' : 'false') . "\n";
    
    if ($testUser->role === 'ketua_ukm' && $testUser->hasRole('ketua_ukm') && $testUser->isKetuaUkm()) {
        echo "   ✅ SUCCESS: All checks passed!\n";
    } else {
        echo "   ❌ FAILED: Some checks failed\n";
    }
    
    echo "\n4. Testing menu access...\n";
    
    if ($testUser->isKetuaUkm()) {
        echo "   ✅ User can access Ketua UKM menu\n";
    } else {
        echo "   ❌ User cannot access Ketua UKM menu\n";
    }
    
    echo "\n5. Testing permissions...\n";
    
    $ketuaUkmPermissions = [
        'manage_ukm',
        'edit_ukm', 
        'create_event',
        'view_ukm_dashboard'
    ];
    
    foreach ($ketuaUkmPermissions as $permission) {
        if ($testUser->can($permission)) {
            echo "   ✅ Has permission: {$permission}\n";
        } else {
            echo "   ❌ Missing permission: {$permission}\n";
        }
    }
    
    echo "\n6. Reverting back to student...\n";
    
    $testUser->update(['role' => 'student']);
    $testUser->syncRoleWithSpatie();
    $testUser->refresh();
    
    echo "   Reverted to: {$testUser->role}\n";
    echo "   Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . "\n";
    
    echo "\n=== TEST COMPLETED ===\n";
    echo "✅ Role assignment system is working correctly!\n";
    echo "You can now safely assign 'ketua_ukm' role through admin panel.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
