<?php

echo "=== MARKING PERMISSION MIGRATION AS COMPLETE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Mark permission migration as completed
    $stmt = $pdo->prepare("INSERT IGNORE INTO migrations (migration, batch) VALUES (?, ?)");
    $stmt->execute(['2025_05_26_134429_create_permission_tables', 2]);
    
    echo "✅ Permission migration marked as completed\n";
    
    // Also mark role migration as completed if needed
    $stmt->execute(['2025_05_26_133735_add_role_to_users_table', 2]);
    echo "✅ Role migration marked as completed\n";
    
    // Check migration status
    echo "\n📋 MIGRATION STATUS:\n";
    $stmt = $pdo->query("SELECT migration, batch FROM migrations WHERE migration LIKE '%permission%' OR migration LIKE '%role%'");
    $migrations = $stmt->fetchAll();
    
    foreach ($migrations as $migration) {
        echo "✅ {$migration['migration']} - Batch {$migration['batch']}\n";
    }
    
    echo "\n🎯 All permission-related migrations are now marked as complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
