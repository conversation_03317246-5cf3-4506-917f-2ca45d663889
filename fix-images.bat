@echo off
echo ========================================
echo    Laravel Image Fix Script
echo ========================================
echo.

echo [1/5] Checking current directory...
cd /d "c:\Users\<USER>\Desktop\TAWEBB\ukmwebLbasedfunc\ukmwebLbasedfunc"
echo Current directory: %CD%
echo.

echo [2/5] Checking if storage link exists...
if exist "public\storage" (
    echo ✅ Storage link already exists
) else (
    echo ❌ Storage link not found
)
echo.

echo [3/5] Recreating storage link...
if exist "public\storage" (
    echo Removing old storage link...
    rmdir "public\storage" /s /q 2>nul
    del "public\storage" 2>nul
)

echo Creating new storage link...
php artisan storage:link
if %errorlevel% equ 0 (
    echo ✅ Storage link created successfully
) else (
    echo ❌ Failed to create storage link
)
echo.

echo [4/5] Clearing Laravel cache...
php artisan config:clear
php artisan cache:clear
php artisan view:clear
echo ✅ Cache cleared
echo.

echo [5/5] Testing image access...
if exist "public\storage" (
    echo ✅ Storage link verification: SUCCESS
    echo.
    echo 📁 Available image folders:
    dir "public\storage" /b 2>nul
    echo.
    echo 🌐 Images should now be accessible at:
    echo    http://localhost:8000/storage/[folder]/[filename]
    echo.
    echo Examples:
    echo    http://localhost:8000/storage/avatars/user.jpg
    echo    http://localhost:8000/storage/ukm-logos/logo.png
    echo    http://localhost:8000/storage/ukm-banners/banner.jpg
) else (
    echo ❌ Storage link verification: FAILED
)

echo.
echo ========================================
echo           Fix Complete!
echo ========================================
echo.
echo If images still don't show:
echo 1. Check if files exist in storage/app/public/
echo 2. Verify image paths in your Blade templates
echo 3. Make sure server is running: php artisan serve
echo.
pause
