-- Create Complete Student Data with Faculty and Major
-- Password for all users: pass123123

-- First, delete any existing student users to avoid duplicates
DELETE FROM users WHERE email LIKE '%@student.telkomuniversity.ac.id';

-- Insert complete student data
INSERT INTO users (name, email, nim, password, role, status, faculty, major, batch, email_verified_at, created_at, updated_at) VALUES

('<PERSON><PERSON>', '<EMAIL>', '12345678901', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Teknik Elektro', 'Teknik Informatika', '2023', NOW(), NOW(), NOW()),

('Ryemius Marghareta Siregar', '<EMAIL>', '12345678902', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Teknik Elektro', 'Sistem Informasi', '2023', NOW(), NOW(), NOW()),

('Amanda Riski Agustian', '<EMAIL>', '12345678903', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Ekonomi dan Bisnis', 'Akuntansi', '2023', NOW(), NOW(), NOW()),

('Najla Ramadina Sulistyowati', '<EMAIL>', '12345678904', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Komunikasi dan Bisnis', 'Ilmu Komunikasi', '2023', NOW(), NOW(), NOW()),

('Nabilla Alyvia', '<EMAIL>', '12345678905', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Teknik Elektro', 'Teknik Informatika', '2023', NOW(), NOW(), NOW()),

('Aras Agita Fasya', '<EMAIL>', '12345678906', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Teknik Elektro', 'Teknik Komputer', '2023', NOW(), NOW(), NOW()),

('Aufa Hafiy Andhika', '<EMAIL>', '12345678907', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Teknik Elektro', 'Sistem Informasi', '2023', NOW(), NOW(), NOW()),

('Rahadian Nungki Saputra', '<EMAIL>', '12345678908', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Ekonomi dan Bisnis', 'Manajemen', '2023', NOW(), NOW(), NOW()),

('Adit Kurniawan', '<EMAIL>', '12345678909', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Teknik Elektro', 'Teknik Informatika', '2023', NOW(), NOW(), NOW()),

('Mikel Austin', '<EMAIL>', '12345678910', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Komunikasi dan Bisnis', 'Digital Marketing', '2023', NOW(), NOW(), NOW()),

('Antonius Valentino', '<EMAIL>', '12345678911', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Teknik Elektro', 'Teknik Informatika', '2023', NOW(), NOW(), NOW()),

('Abraham Arif Mulia', '<EMAIL>', '12345678912', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Ekonomi dan Bisnis', 'Akuntansi', '2023', NOW(), NOW(), NOW()),

('Fathan Mubina', '<EMAIL>', '12345678913', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Teknik Elektro', 'Sistem Informasi', '2023', NOW(), NOW(), NOW()),

('Mutiara Hani Demayanti', '<EMAIL>', '12345678914', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', 'Fakultas Komunikasi dan Bisnis', 'Ilmu Komunikasi', '2023', NOW(), NOW(), NOW());

-- Verify the results
SELECT 'Total Users' as info, COUNT(*) as count FROM users
UNION ALL
SELECT 'Student Users' as info, COUNT(*) as count FROM users WHERE role = 'student'
UNION ALL
SELECT 'Students with Complete Data' as info, COUNT(*) as count FROM users WHERE role = 'student' AND faculty IS NOT NULL AND major IS NOT NULL;

-- Show all created students with complete data
SELECT 
    name as 'Nama',
    email as 'Email', 
    nim as 'NIM',
    faculty as 'Fakultas',
    major as 'Program Studi',
    batch as 'Angkatan',
    status as 'Status'
FROM users 
WHERE role = 'student' 
ORDER BY faculty, major, name;

-- Show faculty distribution
SELECT 
    faculty as 'Fakultas',
    COUNT(*) as 'Jumlah Mahasiswa'
FROM users 
WHERE role = 'student' AND faculty IS NOT NULL
GROUP BY faculty 
ORDER BY COUNT(*) DESC;

-- Show major distribution
SELECT 
    major as 'Program Studi',
    COUNT(*) as 'Jumlah Mahasiswa'
FROM users 
WHERE role = 'student' AND major IS NOT NULL
GROUP BY major 
ORDER BY COUNT(*) DESC;
