<?php
echo "=== TESTING UKM LEADER DISPLAY ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // First, assign some leaders to UKMs for testing
    echo "📋 Setting up test data...\n";
    
    // Get first few students
    $students = $pdo->query("SELECT id, name, nim FROM users WHERE role = 'student' ORDER BY name LIMIT 6")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($students) < 3) {
        echo "❌ Not enough students found. Please create students first.\n";
        exit;
    }
    
    // Assign leaders to some UKMs
    $assignments = [
        'badminton' => 0,
        'dpm' => 1,
        'esport' => 2,
        'futsal' => 3,
        'imma' => 4,
        'mapala' => 5,
    ];
    
    $stmt = $pdo->prepare("UPDATE ukms SET leader_id = ?, updated_at = NOW() WHERE slug = ?");
    
    foreach ($assignments as $slug => $studentIndex) {
        if (isset($students[$studentIndex])) {
            $stmt->execute([$students[$studentIndex]['id'], $slug]);
            echo "   ✅ Assigned {$students[$studentIndex]['name']} to {$slug}\n";
        }
    }
    
    echo "\n📊 Testing UKM Leader Display:\n";
    
    // Test query that mimics what the view will do
    $result = $pdo->query("
        SELECT 
            u.id,
            u.name as ukm_name,
            u.slug,
            u.category,
            u.current_members,
            u.max_members,
            us.name as leader_name,
            us.nim as leader_nim,
            us.email as leader_email
        FROM ukms u
        LEFT JOIN users us ON u.leader_id = us.id
        ORDER BY u.name
    ");
    
    $count = 0;
    $withLeader = 0;
    $withoutLeader = 0;
    
    echo "\n🎯 UKM Leadership Status:\n";
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $count++;
        $hasLeader = !empty($row['leader_name']);
        $status = $hasLeader ? '✅' : '❌';
        
        echo "   {$count}. {$status} {$row['ukm_name']}\n";
        echo "      📁 Slug: {$row['slug']}\n";
        echo "      📊 Members: {$row['current_members']}/{$row['max_members']}\n";
        
        if ($hasLeader) {
            echo "      👤 Ketua: {$row['leader_name']} (NIM: {$row['leader_nim']})\n";
            echo "      📧 Email: {$row['leader_email']}\n";
            $withLeader++;
        } else {
            echo "      👤 Ketua: Belum ada, mungkin pendaftaran anggota akan tertunda\n";
            $withoutLeader++;
        }
        echo "\n";
    }
    
    echo "📈 Summary:\n";
    echo "   Total UKMs: {$count}\n";
    echo "   With Leader: {$withLeader}\n";
    echo "   Without Leader: {$withoutLeader}\n";
    
    echo "\n🌐 Test URLs:\n";
    echo "   Public UKM Detail: http://localhost:8000/ukm/badminton\n";
    echo "   Admin UKM List: http://localhost:8000/admin/ukms\n";
    echo "   Admin UKM Detail: http://localhost:8000/admin/ukms/badminton\n";
    
    echo "\n📋 Expected Results:\n";
    echo "   ✅ UKMs with leaders will show:\n";
    echo "      • Leader name and NIM in contact info\n";
    echo "      • Leader info in admin cards\n";
    echo "   ❌ UKMs without leaders will show:\n";
    echo "      • 'Ketua belum ada, mungkin pendaftaran anggota akan tertunda'\n";
    
    echo "\n🔧 Files Modified:\n";
    echo "   • resources/views/ukms/show.blade.php (public UKM detail)\n";
    echo "   • resources/views/admin/ukms/show.blade.php (admin UKM detail)\n";
    echo "   • resources/views/admin/ukms/index.blade.php (admin UKM list)\n";
    echo "   • app/Http/Controllers/Admin/UkmManagementController.php (eager loading)\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
