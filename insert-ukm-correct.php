<?php
echo "=== INSERTING UKM DATA (CORRECT STRUCTURE) ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n";
    
    // Clear existing data
    echo "Clearing existing UKM data...\n";
    $pdo->exec("DELETE FROM ukms");
    echo "✅ Existing data cleared\n\n";
    
    // Insert new data
    echo "Inserting new UKM data...\n";
    
    $sql = "INSERT INTO ukms (name, slug, description, category, contact_info, meeting_schedule, meeting_location, max_members, current_members, status, registration_status, is_recruiting, established_date, vision, mission, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
    
    $stmt = $pdo->prepare($sql);
    
    $ukms = [
        [
            'UKM Badminton', 
            'badminton', 
            'Unit Kegiatan Mahasiswa Badminton Telkom Jakarta adalah wadah bagi mahasiswa yang memiliki passion dalam olahraga badminton. Kami mengembangkan kemampuan teknik, taktik, dan mental bertanding melalui latihan rutin dan kompetisi.', 
            'sports', 
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567801',
                'instagram' => '@ukmbadminton_telkomjkt'
            ]), 
            'Selasa & Kamis 16:00-18:00, Sabtu 08:00-10:00', 
            'GOR Telkom University Jakarta', 
            50, 0, 'active', 'open', 1, '2018-01-01',
            'Menjadi UKM badminton terdepan yang menghasilkan atlet berprestasi dan berkarakter.',
            'Mengembangkan potensi mahasiswa dalam bidang badminton melalui latihan berkualitas, kompetisi, dan pembinaan karakter.'
        ],
        [
            'UKM DPM (Dewan Perwakilan Mahasiswa)', 
            'dpm', 
            'Dewan Perwakilan Mahasiswa (DPM) adalah lembaga legislatif mahasiswa yang berperan sebagai pengawas dan mitra BEM dalam menjalankan program kerja kemahasiswaan.', 
            'social', 
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567802',
                'instagram' => '@dpm_telkomjkt'
            ]), 
            'Senin 19:00-21:00', 
            'Ruang Sidang Kemahasiswaan', 
            25, 0, 'active', 'open', 1, '2017-01-01',
            'Menjadi lembaga legislatif mahasiswa yang aspiratif, demokratis, dan berintegritas.',
            'Menyuarakan aspirasi mahasiswa, mengawasi kinerja eksekutif mahasiswa, dan memperjuangkan kepentingan mahasiswa.'
        ],
        [
            'UKM Esport',
            'esport',
            'UKM Esport Telkom Jakarta adalah komunitas gamers yang fokus pada pengembangan kemampuan bermain game kompetitif. Kami menaungi berbagai divisi game seperti Mobile Legends, PUBG Mobile, Valorant, dan Dota 2.',
            'technology',
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567803',
                'instagram' => '@esport_telkomjkt',
                'website' => 'https://esport.telkomuniversity.ac.id'
            ]),
            'Senin, Rabu, Jumat 19:00-22:00',
            'Lab Gaming & Multimedia',
            80, 0, 'active', 'open', 1, '2019-01-01',
            'Menjadi UKM esport terdepan yang menghasilkan atlet esport berprestasi tingkat nasional.',
            'Mengembangkan ekosistem esport di kampus, membina atlet esport berkualitas, dan membangun komunitas gaming yang positif.'
        ],
        [
            'UKM Futsal',
            'futsal',
            'UKM Futsal Telkom Jakarta adalah wadah bagi mahasiswa yang memiliki passion dalam olahraga futsal. Kami mengembangkan teknik individu, kerjasama tim, dan strategi permainan melalui latihan intensif.',
            'sports',
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567804',
                'instagram' => '@futsal_telkomjkt'
            ]),
            'Selasa & Kamis 16:00-18:00, Minggu 08:00-10:00',
            'Lapangan Futsal Kampus',
            40, 0, 'active', 'open', 1, '2018-01-01',
            'Menjadi tim futsal universitas yang kompetitif dan berprestasi di tingkat regional.',
            'Mengembangkan kemampuan futsal mahasiswa, membangun sportivitas, dan mengharumkan nama Telkom University Jakarta.'
        ],
        [
            'UKM IMMA (Ikatan Mahasiswa Muslim Akuntansi)',
            'imma',
            'IMMA adalah organisasi yang menghimpun mahasiswa muslim khususnya dari program studi akuntansi dan ekonomi. Kami fokus pada pengembangan spiritual, akademik, dan sosial.',
            'religion',
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567805',
                'instagram' => '@imma_telkomjkt'
            ]),
            'Jumat 13:00-15:00',
            'Musholla Kampus',
            60, 0, 'active', 'open', 1, '2017-01-01',
            'Menjadi wadah pengembangan mahasiswa muslim yang berakhlak mulia dan kompeten di bidang ekonomi.',
            'Menyelenggarakan kajian Islam, mengembangkan pemahaman ekonomi syariah, dan membangun ukhuwah islamiyah.'
        ],
        [
            'UKM Mapala (Mahasiswa Pecinta Alam)',
            'mapala',
            'UKM Mapala Telkom Jakarta adalah komunitas mahasiswa yang memiliki kecintaan terhadap alam dan lingkungan. Kami melakukan kegiatan pendakian gunung, camping, rock climbing, dan konservasi alam.',
            'other',
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567806',
                'instagram' => '@mapala_telkomjkt'
            ]),
            'Sabtu 14:00-17:00',
            'Basecamp Mapala',
            45, 0, 'active', 'open', 1, '2018-01-01',
            'Menjadi komunitas pecinta alam yang berperan aktif dalam pelestarian lingkungan dan pembentukan karakter.',
            'Mengembangkan kecintaan terhadap alam, melatih mental dan fisik melalui kegiatan alam, serta berkontribusi dalam konservasi lingkungan.'
        ],
        [
            'UKM PMK (Persekutuan Mahasiswa Kristen)',
            'pmk',
            'PMK adalah wadah persekutuan bagi mahasiswa Kristen di Telkom University Jakarta. Kami menyelenggarakan ibadah, fellowship, retreat, dan pelayanan sosial.',
            'religion',
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567807',
                'instagram' => '@pmk_telkomjkt'
            ]),
            'Kamis 18:00-20:00',
            'Ruang Persekutuan',
            40, 0, 'active', 'open', 1, '2017-01-01',
            'Menjadi persekutuan yang membawa terang Kristus dalam kehidupan kampus dan masyarakat.',
            'Menguatkan iman mahasiswa Kristen, membangun persekutuan yang solid, dan melayani sesama dengan kasih.'
        ],
        [
            'UKM Seni Budaya',
            'seni-budaya',
            'UKM Seni Budaya Telkom Jakarta adalah wadah kreativitas mahasiswa dalam bidang seni dan budaya Indonesia. Kami mengembangkan berbagai kesenian tradisional seperti tari, musik tradisional, teater, dan seni rupa.',
            'arts',
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567808',
                'instagram' => '@senibudaya_telkomjkt'
            ]),
            'Rabu & Sabtu 15:00-18:00',
            'Studio Seni & Budaya',
            55, 0, 'active', 'open', 1, '2017-01-01',
            'Menjadi pusat pengembangan seni dan budaya yang melestarikan kearifan lokal Indonesia.',
            'Mengembangkan bakat seni mahasiswa, melestarikan budaya Indonesia, dan memperkenalkan kesenian tradisional kepada generasi muda.'
        ],
        [
            'UKM Sistem Informasi',
            'sistem-informasi',
            'UKM Sistem Informasi adalah komunitas mahasiswa yang fokus pada pengembangan teknologi informasi dan sistem. Kami menyelenggarakan workshop programming, seminar teknologi, hackathon, dan project development.',
            'technology',
            json_encode([
                'email' => '<EMAIL>',
                'phone' => '081234567809',
                'instagram' => '@si_telkomjkt',
                'website' => 'https://si.telkomuniversity.ac.id'
            ]),
            'Selasa & Kamis 19:00-21:00',
            'Lab Komputer & Programming',
            70, 0, 'active', 'open', 1, '2018-01-01',
            'Menjadi komunitas IT terdepan yang menghasilkan lulusan kompeten dan inovatif.',
            'Mengembangkan kemampuan programming, membangun solusi teknologi, dan mempersiapkan mahasiswa untuk industri IT.'
        ]
    ];
    
    $created = 0;
    foreach ($ukms as $ukm) {
        if ($stmt->execute($ukm)) {
            echo "✅ {$ukm[0]}\n";
            $created++;
        } else {
            echo "❌ Failed: {$ukm[0]}\n";
        }
    }
    
    echo "\n=== RESULT ===\n";
    echo "✅ Successfully created: {$created} UKMs\n";
    
    // Check total
    $result = $pdo->query("SELECT COUNT(*) as total FROM ukms");
    $total = $result->fetch(PDO::FETCH_ASSOC)['total'];
    echo "📊 Total UKMs in database: {$total}\n";
    
    // Show categories
    echo "\n📋 Categories:\n";
    $result = $pdo->query("SELECT category, COUNT(*) as count FROM ukms GROUP BY category");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   • {$row['category']}: {$row['count']} UKMs\n";
    }
    
    echo "\n🌐 Test URLs:\n";
    echo "   UKM Index: http://localhost:8000/ukm\n";
    echo "   Sample UKM: http://localhost:8000/ukm/badminton\n";
    echo "   Admin UKMs: http://localhost:8000/admin/ukms\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== COMPLETE ===\n";
?>
