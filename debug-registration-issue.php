<?php

echo "=== DEBUGGING REGISTRATION ISSUE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // 1. Check UKM Cendol details
    echo "📋 CHECKING UKM CENDOL:\n";
    $stmt = $pdo->query("SELECT * FROM ukms WHERE name LIKE '%cendol%'");
    $ukms = $stmt->fetchAll();
    
    foreach ($ukms as $ukm) {
        echo "- UKM ID: {$ukm['id']}, Name: {$ukm['name']}\n";
        echo "  Leader ID: {$ukm['leader_id']}\n";
        echo "  Status: {$ukm['status']}\n";
        echo "  Registration Status: {$ukm['registration_status']}\n";
        echo "  Current Members: {$ukm['current_members']}\n";
        
        // Check leader
        if ($ukm['leader_id']) {
            $stmt = $pdo->prepare("SELECT id, name, email, role FROM users WHERE id = ?");
            $stmt->execute([$ukm['leader_id']]);
            $leader = $stmt->fetch();
            
            if ($leader) {
                echo "  Leader: {$leader['name']} ({$leader['email']}) - Role: {$leader['role']}\n";
            } else {
                echo "  ❌ LEADER NOT FOUND!\n";
            }
        } else {
            echo "  ⚠️  No leader assigned\n";
        }
    }
    
    // 2. Check all registrations for UKM Cendol
    echo "\n📋 CHECKING UKM CENDOL REGISTRATIONS:\n";
    $stmt = $pdo->query("
        SELECT 
            um.*,
            u.name as user_name,
            u.email as user_email,
            u.nim,
            ukm.name as ukm_name
        FROM ukm_members um
        LEFT JOIN users u ON um.user_id = u.id
        LEFT JOIN ukms ukm ON um.ukm_id = ukm.id
        WHERE ukm.name LIKE '%cendol%'
        ORDER BY um.created_at DESC
    ");
    $registrations = $stmt->fetchAll();
    
    if (count($registrations) > 0) {
        echo "Found " . count($registrations) . " registrations:\n";
        foreach ($registrations as $reg) {
            echo "- User: {$reg['user_name']} ({$reg['user_email']})\n";
            echo "  Status: {$reg['status']}\n";
            echo "  Applied: {$reg['applied_at']}\n";
            echo "  Created: {$reg['created_at']}\n";
            echo "  UKM: {$reg['ukm_name']}\n";
            echo "  ---\n";
        }
    } else {
        echo "❌ No registrations found for UKM Cendol\n";
    }
    
    // 3. Check recent registrations (all UKMs)
    echo "\n📋 CHECKING ALL RECENT REGISTRATIONS:\n";
    $stmt = $pdo->query("
        SELECT 
            um.*,
            u.name as user_name,
            u.email as user_email,
            ukm.name as ukm_name
        FROM ukm_members um
        LEFT JOIN users u ON um.user_id = u.id
        LEFT JOIN ukms ukm ON um.ukm_id = ukm.id
        WHERE um.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        ORDER BY um.created_at DESC
        LIMIT 10
    ");
    $recentRegs = $stmt->fetchAll();
    
    if (count($recentRegs) > 0) {
        echo "Found " . count($recentRegs) . " recent registrations:\n";
        foreach ($recentRegs as $reg) {
            echo "- User: {$reg['user_name']} → UKM: {$reg['ukm_name']}\n";
            echo "  Status: {$reg['status']}, Created: {$reg['created_at']}\n";
        }
    } else {
        echo "❌ No recent registrations found\n";
    }
    
    // 4. Check ketua UKM users
    echo "\n👥 CHECKING KETUA UKM USERS:\n";
    $stmt = $pdo->query("
        SELECT 
            u.id,
            u.name,
            u.email,
            u.role,
            ukm.name as leading_ukm
        FROM users u
        LEFT JOIN ukms ukm ON u.id = ukm.leader_id
        WHERE u.role = 'ketua_ukm' OR ukm.leader_id IS NOT NULL
    ");
    $ketuaUkms = $stmt->fetchAll();
    
    foreach ($ketuaUkms as $ketua) {
        echo "- {$ketua['name']} ({$ketua['email']}) - Role: {$ketua['role']}\n";
        echo "  Leading UKM: " . ($ketua['leading_ukm'] ?: 'None') . "\n";
    }
    
    // 5. Check if there's a mismatch in UKM slug vs ID
    echo "\n🔍 CHECKING UKM SLUG USAGE:\n";
    $stmt = $pdo->query("SELECT id, name, slug FROM ukms");
    $allUkms = $stmt->fetchAll();
    
    foreach ($allUkms as $ukm) {
        echo "- ID: {$ukm['id']}, Name: {$ukm['name']}, Slug: {$ukm['slug']}\n";
    }
    
    // 6. Check if registration was submitted to correct UKM
    echo "\n🎯 CHECKING REGISTRATION SUBMISSION:\n";
    echo "If student registered for 'UKM Cendol', check these URLs:\n";
    echo "- Registration form: /ukm/cendol/daftar\n";
    echo "- UKM show page: /ukm/cendol\n";
    echo "- Check if slug 'cendol' matches database\n";
    
    // 7. Test query that ketua UKM controller uses
    echo "\n🔍 TESTING KETUA UKM QUERY:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id,
            ukm.name,
            ukm.leader_id,
            COUNT(pending.id) as pending_count,
            COUNT(active.id) as active_count
        FROM ukms ukm
        LEFT JOIN ukm_members pending ON ukm.id = pending.ukm_id AND pending.status = 'pending'
        LEFT JOIN ukm_members active ON ukm.id = active.ukm_id AND active.status = 'active'
        GROUP BY ukm.id, ukm.name, ukm.leader_id
    ");
    $ukmStats = $stmt->fetchAll();
    
    foreach ($ukmStats as $stat) {
        echo "- UKM: {$stat['name']} (Leader ID: {$stat['leader_id']})\n";
        echo "  Pending: {$stat['pending_count']}, Active: {$stat['active_count']}\n";
    }
    
    echo "\n✅ Debug complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
