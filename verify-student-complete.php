<?php
echo "=== VERIFYING COMPLETE STUDENT DATA ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check total students
    echo "📊 Student Statistics:\n";
    $result = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'student'");
    $totalStudents = $result->fetch(PDO::FETCH_ASSOC)['total'];
    echo "   Total Students: {$totalStudents}\n";
    
    // Check students with complete data
    $result = $pdo->query("SELECT COUNT(*) as complete FROM users WHERE role = 'student' AND faculty IS NOT NULL AND major IS NOT NULL AND batch IS NOT NULL");
    $completeStudents = $result->fetch(PDO::FETCH_ASSOC)['complete'];
    echo "   Students with Complete Data: {$completeStudents}\n";
    
    // Check students with missing data
    $missingStudents = $totalStudents - $completeStudents;
    echo "   Students with Missing Data: {$missingStudents}\n\n";
    
    if ($completeStudents > 0) {
        echo "✅ Students with Complete Data:\n";
        $result = $pdo->query("SELECT name, email, faculty, major, batch FROM users WHERE role = 'student' AND faculty IS NOT NULL ORDER BY name");
        $count = 0;
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            $count++;
            echo "   {$count}. {$row['name']}\n";
            echo "      📧 {$row['email']}\n";
            echo "      🏫 {$row['faculty']}\n";
            echo "      📚 {$row['major']}\n";
            echo "      📅 Batch {$row['batch']}\n\n";
        }
    }
    
    if ($missingStudents > 0) {
        echo "⚠️  Students with Missing Data:\n";
        $result = $pdo->query("SELECT name, email, faculty, major, batch FROM users WHERE role = 'student' AND (faculty IS NULL OR major IS NULL OR batch IS NULL) ORDER BY name");
        $count = 0;
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            $count++;
            echo "   {$count}. {$row['name']}\n";
            echo "      📧 {$row['email']}\n";
            echo "      🏫 Faculty: " . ($row['faculty'] ?: 'MISSING') . "\n";
            echo "      📚 Major: " . ($row['major'] ?: 'MISSING') . "\n";
            echo "      📅 Batch: " . ($row['batch'] ?: 'MISSING') . "\n\n";
        }
        
        echo "📋 To fix missing data:\n";
        echo "1. Open phpMyAdmin: http://localhost/phpmyadmin\n";
        echo "2. Select database 'ukmwebv'\n";
        echo "3. Go to SQL tab\n";
        echo "4. Copy content from 'update-student-data.sql'\n";
        echo "5. Paste and execute\n\n";
    }
    
    // Check faculty distribution
    echo "🏫 Faculty Distribution:\n";
    $result = $pdo->query("SELECT faculty, COUNT(*) as count FROM users WHERE role = 'student' AND faculty IS NOT NULL GROUP BY faculty ORDER BY count DESC");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   • {$row['faculty']}: {$row['count']} students\n";
    }
    
    echo "\n📚 Major Distribution:\n";
    $result = $pdo->query("SELECT major, COUNT(*) as count FROM users WHERE role = 'student' AND major IS NOT NULL GROUP BY major ORDER BY count DESC");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   • {$row['major']}: {$row['count']} students\n";
    }
    
    echo "\n🌐 Admin Dashboard URLs:\n";
    echo "   Dashboard: http://localhost:8000/admin/dashboard\n";
    echo "   Students: http://localhost:8000/admin/students\n";
    echo "   UKMs: http://localhost:8000/admin/ukms\n";
    
    if ($completeStudents == $totalStudents && $totalStudents > 0) {
        echo "\n🎉 All student data is complete! Students should now appear in admin dashboard.\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== VERIFICATION COMPLETE ===\n";
?>
