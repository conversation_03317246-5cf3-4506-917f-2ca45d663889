<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Support\Facades\Hash;

// Setup database connection
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '127.0.0.1',
    'database' => 'ukmwebv',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Test database connection
    $pdo = $capsule->getConnection()->getPdo();
    echo "✅ Database connected successfully!\n";
    
    // Check if users table exists
    $tables = $capsule->select("SHOW TABLES LIKE 'users'");
    if (empty($tables)) {
        echo "❌ Users table does not exist!\n";
        exit(1);
    }
    echo "✅ Users table exists\n";
    
    // Check current users
    $userCount = $capsule->table('users')->count();
    echo "📊 Current users in database: $userCount\n";
    
    if ($userCount > 0) {
        $users = $capsule->table('users')->select('email', 'role', 'status')->get();
        echo "👥 Existing users:\n";
        foreach ($users as $user) {
            echo "   - {$user->email} ({$user->role}) - {$user->status}\n";
        }
    }
    
    // Insert admin users if they don't exist
    $adminExists = $capsule->table('users')->where('email', '<EMAIL>')->exists();
    
    if (!$adminExists) {
        echo "🔧 Creating admin users...\n";
        
        // Insert admin user
        $capsule->table('users')->insert([
            'nim' => 'ADMIN001',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Administrasi',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
        
        // Insert student user
        $capsule->table('users')->insert([
            'nim' => '1103210001',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => password_hash('student123', PASSWORD_DEFAULT),
            'phone' => '081234567892',
            'gender' => 'male',
            'faculty' => 'Informatika',
            'major' => 'Teknik Informatika',
            'batch' => '2021',
            'role' => 'student',
            'status' => 'active',
            'email_verified_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
        
        // Insert ketua UKM user
        $capsule->table('users')->insert([
            'nim' => '1103210002',
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'password' => password_hash('ketua123', PASSWORD_DEFAULT),
            'phone' => '081234567893',
            'gender' => 'female',
            'faculty' => 'Informatika',
            'major' => 'Sistem Informasi',
            'batch' => '2021',
            'role' => 'ketua_ukm',
            'status' => 'active',
            'email_verified_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
        
        echo "✅ Admin users created successfully!\n";
    } else {
        echo "ℹ️  Admin user already exists\n";
    }
    
    // Final user count
    $finalCount = $capsule->table('users')->count();
    echo "📊 Final user count: $finalCount\n";
    
    echo "\n🔑 Login Credentials:\n";
    echo "Admin: <EMAIL> / admin123\n";
    echo "Student: <EMAIL> / student123\n";
    echo "Ketua UKM: <EMAIL> / ketua123\n";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
