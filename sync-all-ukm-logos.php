<?php
/**
 * Sync all UKM logos from storage to public
 */

echo "=== SYNCING ALL UKM LOGOS ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Get all UKMs with logos
    $ukms = \App\Models\Ukm::whereNotNull('logo')->where('logo', '!=', '')->get();
    
    echo "🔍 Found " . $ukms->count() . " UKMs with logos\n\n";
    
    $synced = 0;
    $errors = 0;
    
    foreach ($ukms as $ukm) {
        echo "Processing: {$ukm->name}\n";
        
        $storagePath = storage_path('app/public/' . $ukm->logo);
        $publicPath = public_path('storage/' . $ukm->logo);
        
        if (file_exists($storagePath)) {
            // Ensure directory exists
            $publicDir = dirname($publicPath);
            if (!is_dir($publicDir)) {
                mkdir($publicDir, 0755, true);
                echo "  📁 Created directory: $publicDir\n";
            }
            
            // Copy if not exists or different
            if (!file_exists($publicPath) || filesize($storagePath) !== filesize($publicPath)) {
                if (copy($storagePath, $publicPath)) {
                    echo "  ✅ Synced logo\n";
                    $synced++;
                } else {
                    echo "  ❌ Failed to sync logo\n";
                    $errors++;
                }
            } else {
                echo "  ✅ Already synced\n";
                $synced++;
            }
        } else {
            echo "  ❌ Source file not found: $storagePath\n";
            $errors++;
        }
        
        echo "---\n";
    }
    
    echo "\n=== SYNC SUMMARY ===\n";
    echo "✅ Successfully synced: $synced\n";
    echo "❌ Errors: $errors\n";
    echo "📊 Total processed: " . $ukms->count() . "\n\n";
    
    if ($errors === 0) {
        echo "🎉 All UKM logos are now properly synced!\n";
        echo "🔗 Test admin views:\n";
        foreach ($ukms as $ukm) {
            echo "   - http://127.0.0.1:8000/admin/ukms/{$ukm->slug}\n";
        }
    } else {
        echo "⚠️ Some logos had issues. Please check the errors above.\n";
    }
    
    echo "\n=== SYNC COMPLETED ===\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
