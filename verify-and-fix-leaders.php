<?php

echo "=== VERIFYING AND FIXING LEADER IDs ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // 1. Show all users with ketua_ukm role
    echo "👥 KETUA UKM USERS:\n";
    $stmt = $pdo->query("SELECT id, name, email, role FROM users WHERE role = 'ketua_ukm' ORDER BY id");
    $ketuaUsers = $stmt->fetchAll();
    
    foreach ($ketuaUsers as $user) {
        echo "- ID: {$user['id']}, Name: {$user['name']}, Email: {$user['email']}\n";
    }
    
    // 2. Show current UKM leader assignments
    echo "\n🏢 CURRENT UKM LEADER ASSIGNMENTS:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id as ukm_id,
            ukm.name as ukm_name,
            ukm.leader_id,
            u.name as leader_name,
            u.email as leader_email,
            u.role as leader_role
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
        ORDER BY ukm.id
    ");
    $assignments = $stmt->fetchAll();
    
    foreach ($assignments as $assignment) {
        echo "- UKM: {$assignment['ukm_name']} (ID: {$assignment['ukm_id']})\n";
        echo "  Leader ID: " . ($assignment['leader_id'] ?: 'NULL') . "\n";
        echo "  Leader: " . ($assignment['leader_name'] ?: 'NOT FOUND') . "\n";
        echo "  Email: " . ($assignment['leader_email'] ?: 'N/A') . "\n";
        echo "  Role: " . ($assignment['leader_role'] ?: 'N/A') . "\n";
        echo "  ---\n";
    }
    
    // 3. Fix assignments based on email
    echo "\n🔧 FIXING LEADER ASSIGNMENTS:\n";
    
    // Get Aras ID
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $arasId = $stmt->fetchColumn();
    
    // Get Rehan ID  
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $rehanId = $stmt->fetchColumn();
    
    if ($arasId) {
        echo "Found Aras ID: {$arasId}\n";
        $stmt = $pdo->prepare("UPDATE ukms SET leader_id = ? WHERE name = 'UKM Cendol'");
        $stmt->execute([$arasId]);
        echo "✅ Updated UKM Cendol leader to Aras (ID: {$arasId})\n";
    } else {
        echo "❌ Aras not found!\n";
    }
    
    if ($rehanId) {
        echo "Found Rehan ID: {$rehanId}\n";
        $stmt = $pdo->prepare("UPDATE ukms SET leader_id = ? WHERE name = 'Sistem informasi'");
        $stmt->execute([$rehanId]);
        echo "✅ Updated Sistem Informasi leader to Rehan (ID: {$rehanId})\n";
    } else {
        echo "❌ Rehan not found!\n";
    }
    
    // 4. Verify after fix
    echo "\n✅ VERIFICATION AFTER FIX:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id as ukm_id,
            ukm.name as ukm_name,
            ukm.leader_id,
            u.name as leader_name,
            u.email as leader_email
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
        ORDER BY ukm.id
    ");
    $verifyAssignments = $stmt->fetchAll();
    
    foreach ($verifyAssignments as $assignment) {
        echo "- UKM: {$assignment['ukm_name']} → Leader: " . ($assignment['leader_name'] ?: 'NONE') . "\n";
        echo "  Leader ID: {$assignment['leader_id']}, Email: " . ($assignment['leader_email'] ?: 'N/A') . "\n";
    }
    
    // 5. Check registrations for each UKM
    echo "\n📋 CHECKING REGISTRATIONS:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.name as ukm_name,
            ukm.leader_id,
            COUNT(um.id) as total_registrations,
            SUM(CASE WHEN um.status = 'pending' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN um.status = 'active' THEN 1 ELSE 0 END) as active_count,
            SUM(CASE WHEN um.status = 'rejected' THEN 1 ELSE 0 END) as rejected_count
        FROM ukms ukm
        LEFT JOIN ukm_members um ON ukm.id = um.ukm_id
        GROUP BY ukm.id, ukm.name, ukm.leader_id
    ");
    $registrationStats = $stmt->fetchAll();
    
    foreach ($registrationStats as $stat) {
        echo "- UKM: {$stat['ukm_name']} (Leader ID: {$stat['leader_id']})\n";
        echo "  Total: {$stat['total_registrations']}, Pending: {$stat['pending_count']}, Active: {$stat['active_count']}, Rejected: {$stat['rejected_count']}\n";
    }
    
    // 6. Show specific pending registrations
    echo "\n📝 PENDING REGISTRATIONS DETAIL:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.name as ukm_name,
            ukm.leader_id,
            u.name as student_name,
            u.email as student_email,
            um.status,
            um.applied_at,
            um.created_at
        FROM ukm_members um
        JOIN users u ON um.user_id = u.id
        JOIN ukms ukm ON um.ukm_id = ukm.id
        WHERE um.status = 'pending'
        ORDER BY ukm.name, um.created_at DESC
    ");
    $pendingRegs = $stmt->fetchAll();
    
    foreach ($pendingRegs as $reg) {
        echo "- UKM: {$reg['ukm_name']} (Leader ID: {$reg['leader_id']})\n";
        echo "  Student: {$reg['student_name']} ({$reg['student_email']})\n";
        echo "  Applied: {$reg['applied_at']}, Created: {$reg['created_at']}\n";
        echo "  ---\n";
    }
    
    echo "\n🎯 NEXT STEPS:\n";
    echo "1. Login as Aras: <EMAIL>\n";
    echo "2. Go to: http://localhost:8000/ketua-ukm/members\n";
    echo "3. Check Laravel logs at: storage/logs/laravel.log\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
