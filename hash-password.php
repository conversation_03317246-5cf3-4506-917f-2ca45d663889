<?php

// Simple script to generate password hash for admin
echo "=== PASSWORD HASH GENERATOR ===\n";

$passwords = [
    'admin123' => 'Admin password',
    'student123' => 'Student password', 
    'ketua123' => 'Ketua UKM password'
];

foreach ($passwords as $password => $description) {
    $hash = password_hash($password, PASSWORD_DEFAULT);
    echo "{$description}: {$password}\n";
    echo "Hash: {$hash}\n\n";
}

echo "=== MANUAL ADMIN CREATION ===\n";
echo "You can manually insert this into your database:\n\n";

$adminHash = password_hash('admin123', PASSWORD_DEFAULT);

echo "INSERT INTO users (\n";
echo "    nim, name, email, password, phone, gender, faculty, major, batch, role, status, email_verified_at, created_at, updated_at\n";
echo ") VALUES (\n";
echo "    'ADMIN001',\n";
echo "    'Administrator',\n";
echo "    '<EMAIL>',\n";
echo "    '{$adminHash}',\n";
echo "    '081234567890',\n";
echo "    'male',\n";
echo "    'Administrasi',\n";
echo "    'Sistem Informasi',\n";
echo "    '2024',\n";
echo "    'admin',\n";
echo "    'active',\n";
echo "    NOW(),\n";
echo "    NOW(),\n";
echo "    NOW()\n";
echo ");\n\n";

echo "=== LOGIN CREDENTIALS ===\n";
echo "Email: <EMAIL>\n";
echo "Password: admin123\n";
echo "Role: admin\n";
echo "Status: active\n";
echo "\nLogin URL: http://localhost:8000/login\n";
