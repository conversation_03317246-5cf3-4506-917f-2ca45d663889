@echo off
echo ========================================
echo   VERIFYING ADMIN ACTIVATE/SUSPEND BUTTONS
echo ========================================
echo.

echo [1/5] Clearing caches...
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

echo.
echo [2/5] Testing admin button functionality...
php test-admin-buttons.php

echo.
echo [3/5] Checking routes exist...
php artisan route:list | findstr activate
php artisan route:list | findstr suspend

echo.
echo [4/5] Creating admin user...
php create-admin-user.php

echo.
echo [5/5] Starting server for testing...
echo.
echo ========================================
echo   ADMIN BUTTONS VERIFICATION COMPLETED
echo ========================================
echo.
echo BUTTON LOCATIONS:
echo ✓ Moved from Detail Mahasiswa to Kelola Mahasiswa
echo ✓ All activate/suspend buttons in user list table
echo ✓ Detail page only shows Reset Password
echo.
echo BUTTON BEHAVIOR:
echo 🟢 PENDING users → GREEN "Aktifkan" button
echo 🔵 SUSPENDED users → BLUE "Aktifkan" button  
echo 🟡 INACTIVE users → YELLOW "Aktifkan" button
echo 🔴 ACTIVE users → RED "Suspend" button
echo 🛡️ ADMIN users → No suspend button (protected)
echo.
echo FUNCTIONALITY:
echo ✓ Activate button changes status to "active"
echo ✓ Suspend button changes status to "suspended"
echo ✓ Confirmation dialogs before action
echo ✓ Success/error messages displayed
echo ✓ Admin accounts protected from suspension
echo.
echo ROUTES:
echo - Activate: PATCH /admin/users/{id}/activate
echo - Suspend: PATCH /admin/users/{id}/suspend
echo.
echo USER EXPERIENCE:
echo 1. Admin goes to Kelola Mahasiswa
echo 2. Sees user list with status badges
echo 3. Clicks appropriate button based on status
echo 4. Confirms action in dialog
echo 5. Status changes immediately
echo 6. Success message displayed
echo.
echo REGISTRATION WORKFLOW:
echo 1. User registers → Status: PENDING
echo 2. Admin sees "Menunggu Persetujuan" badge
echo 3. Admin clicks GREEN "Aktifkan" button
echo 4. Status changes to ACTIVE
echo 5. User can now login
echo.
echo SUSPENSION WORKFLOW:
echo 1. Admin clicks RED "Suspend" on active user
echo 2. Confirms suspension
echo 3. Status changes to SUSPENDED
echo 4. User is logged out immediately
echo 5. User cannot login until reactivated
echo.
echo ADMIN PANEL ACCESS:
echo - Login: <EMAIL> / admin123
echo - URL: http://127.0.0.1:8000/admin/users
echo.
echo TESTING CHECKLIST:
echo □ Login as admin
echo □ Go to Kelola Mahasiswa
echo □ Check status badges display correctly
echo □ Test activate button on pending user
echo □ Test suspend button on active user
echo □ Verify user can/cannot login after status change
echo □ Check detail page only has Reset Password
echo □ Verify admin accounts cannot be suspended
echo.
echo Starting Laravel server...
echo.

php artisan serve --host=127.0.0.1 --port=8000
