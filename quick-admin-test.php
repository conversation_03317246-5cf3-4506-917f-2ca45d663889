<?php

echo "=== QUICK ADMIN LOGIN TEST ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // 1. Create/Update admin user
    echo "1. Creating admin user...\n";
    $admin = \App\Models\User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'nim' => 'ADMIN001',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Administrasi',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]
    );
    echo "   ✅ Admin user ready: {$admin->email}\n";
    
    // 2. Test login credentials
    echo "\n2. Testing login credentials...\n";
    $credentials = ['email' => '<EMAIL>', 'password' => 'admin123'];
    
    if (\Illuminate\Support\Facades\Auth::attempt($credentials)) {
        echo "   ✅ Login test successful!\n";
        $user = \Illuminate\Support\Facades\Auth::user();
        echo "   User: {$user->name} (Role: {$user->role})\n";
        \Illuminate\Support\Facades\Auth::logout();
    } else {
        echo "   ❌ Login test failed!\n";
    }
    
    // 3. Test with 'login' field (for form compatibility)
    echo "\n3. Testing with 'login' field...\n";
    $loginField = '<EMAIL>';
    $field = filter_var($loginField, FILTER_VALIDATE_EMAIL) ? 'email' : 'nim';
    $credentials2 = [$field => $loginField, 'password' => 'admin123'];
    
    if (\Illuminate\Support\Facades\Auth::attempt($credentials2)) {
        echo "   ✅ Login field test successful!\n";
        $user = \Illuminate\Support\Facades\Auth::user();
        echo "   Field used: {$field}\n";
        echo "   User: {$user->name} (Role: {$user->role})\n";
        \Illuminate\Support\Facades\Auth::logout();
    } else {
        echo "   ❌ Login field test failed!\n";
    }
    
    // 4. Check routes
    echo "\n4. Checking routes...\n";
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    
    $loginRoute = $routes->getByName('login');
    $adminRoute = $routes->getByName('admin.dashboard');
    
    if ($loginRoute) {
        echo "   ✅ Login route: /{$loginRoute->uri()}\n";
    }
    
    if ($adminRoute) {
        echo "   ✅ Admin route: /{$adminRoute->uri()}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
echo "\nNOW TRY LOGGING IN:\n";
echo "1. Open: http://127.0.0.1:8000/login\n";
echo "2. Email: <EMAIL>\n";
echo "3. Password: admin123\n";
echo "4. Should redirect to: http://127.0.0.1:8000/admin\n";
