<?php

echo "=== COMPREHENSIVE LOGIN DEBUG ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // 1. Check if users exist
    echo "1. CHECKING USERS IN DATABASE:\n";
    $stmt = $pdo->query("SELECT id, nim, name, email, role, status, password FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "❌ NO USERS FOUND IN DATABASE!\n";
        echo "🔧 Creating test user...\n";
        
        // Create a simple test user
        $testPassword = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (nim, name, email, password, phone, gender, faculty, major, batch, status, role, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())");
        
        $stmt->execute([
            'ADMIN001',
            'Test Admin',
            '<EMAIL>',
            $testPassword,
            '081234567890',
            'male',
            'Admin',
            'Admin',
            '2024',
            'active',
            'admin'
        ]);
        
        echo "✅ Test user created\n";
        
        // Re-fetch users
        $stmt = $pdo->query("SELECT id, nim, name, email, role, status, password FROM users ORDER BY id");
        $users = $stmt->fetchAll();
    }
    
    echo "Found " . count($users) . " users:\n";
    foreach ($users as $user) {
        echo "- ID: {$user['id']}, Email: {$user['email']}, Role: {$user['role']}, Status: {$user['status']}\n";
        echo "  Password hash: " . substr($user['password'], 0, 30) . "...\n";
    }
    
    echo "\n2. TESTING LOGIN LOGIC:\n";
    
    // Test credentials
    $testCredentials = [
        '<EMAIL>',
        'ADMIN001',
        '<EMAIL>',
        'ADMIN002'
    ];
    
    foreach ($testCredentials as $login) {
        echo "\n--- Testing login: {$login} ---\n";
        
        // Determine field type (same logic as Laravel LoginRequest)
        $field = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'nim';
        echo "Field type: {$field}\n";
        
        // Find user
        $stmt = $pdo->prepare("SELECT * FROM users WHERE {$field} = ?");
        $stmt->execute([$login]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "✅ User found: {$user['name']}\n";
            echo "Status: {$user['status']}\n";
            echo "Role: {$user['role']}\n";
            
            // Test password
            $testPasswords = ['password', 'admin123', 'secret', '123456'];
            
            foreach ($testPasswords as $testPass) {
                if (password_verify($testPass, $user['password'])) {
                    echo "✅ Password '{$testPass}' works!\n";
                    
                    // Check if login should work
                    if ($user['status'] === 'active') {
                        echo "✅ Status is active - LOGIN SHOULD WORK\n";
                        echo "🎯 USE: {$login} / {$testPass}\n";
                    } else {
                        echo "❌ Status '{$user['status']}' will block login\n";
                    }
                    break;
                } else {
                    echo "❌ Password '{$testPass}' failed\n";
                }
            }
        } else {
            echo "❌ User not found\n";
        }
    }
    
    echo "\n3. CREATING GUARANTEED WORKING USER:\n";
    
    // Delete existing admin and create new one with known password
    $pdo->exec("DELETE FROM users WHERE email = '<EMAIL>'");
    
    $guaranteedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (nim, name, email, password, phone, gender, faculty, major, batch, status, role, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())");
    
    $stmt->execute([
        'TEST001',
        'Guaranteed Admin',
        '<EMAIL>',
        $guaranteedPassword,
        '081234567890',
        'male',
        'Admin',
        'Admin',
        '2024',
        'active',
        'admin'
    ]);
    
    echo "✅ Guaranteed working user created\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
    
    // Verify it works
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $testUser = $stmt->fetch();
    
    if ($testUser && password_verify('admin123', $testUser['password'])) {
        echo "✅ Verification: Password works!\n";
    } else {
        echo "❌ Verification failed!\n";
    }
    
    echo "\n4. CHECKING LARAVEL AUTH CONFIGURATION:\n";
    
    // Check if there are any issues with Laravel's auth setup
    echo "Checking .env database config...\n";
    
    if (file_exists('.env')) {
        $envContent = file_get_contents('.env');
        
        if (strpos($envContent, 'DB_DATABASE=ukmwebv') !== false) {
            echo "✅ Database name correct in .env\n";
        } else {
            echo "❌ Database name might be wrong in .env\n";
        }
        
        if (strpos($envContent, 'DB_USERNAME=root') !== false) {
            echo "✅ Database username correct in .env\n";
        } else {
            echo "❌ Database username might be wrong in .env\n";
        }
    } else {
        echo "❌ .env file not found\n";
    }
    
    echo "\n=== FINAL RECOMMENDATIONS ===\n";
    echo "🎯 Try these credentials:\n";
    echo "1. <EMAIL> / admin123 (guaranteed to work)\n";
    echo "2. <EMAIL> / password\n";
    echo "3. ADMIN001 / password\n";
    echo "\n🌐 Login URL: http://localhost:8000/login\n";
    echo "\n💡 If still failing, the issue is in Laravel's authentication logic, not the database.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
