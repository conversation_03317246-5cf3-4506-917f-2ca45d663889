<?php
/**
 * Debug method_exists error in detail
 */

echo "=== DEBUGGING METHOD_EXISTS ERROR IN DETAIL ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing UKM Controller directly...\n";
    
    // Test UKM controller
    try {
        $controller = new \App\Http\Controllers\UkmController();
        echo "   ✅ UkmController instantiated\n";
        
        // Test index method
        $request = new \Illuminate\Http\Request();
        echo "   Testing index method...\n";
        
        ob_start();
        $response = $controller->index($request);
        $output = ob_get_clean();
        
        echo "   ✅ Index method executed successfully\n";
        
    } catch (Exception $e) {
        echo "   ❌ Controller error: " . $e->getMessage() . "\n";
        echo "   📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
        echo "   📋 Stack trace:\n";
        foreach (explode("\n", $e->getTraceAsString()) as $line) {
            if (strpos($line, 'method_exists') !== false) {
                echo "   🔍 " . $line . "\n";
            }
        }
    }
    
    echo "\n2. Testing UKM relationships one by one...\n";
    
    $ukms = \App\Models\Ukm::limit(3)->get();
    
    foreach ($ukms as $ukm) {
        echo "   Testing UKM: {$ukm->name} (ID: {$ukm->id})\n";
        
        // Test each relationship individually
        $relationships = ['leader', 'activeMembers', 'achievements', 'publishedEvents'];
        
        foreach ($relationships as $relation) {
            try {
                echo "     Testing {$relation}... ";
                
                // Test relationship method exists
                if (method_exists($ukm, $relation)) {
                    echo "method exists... ";
                    
                    // Test calling the relationship
                    $result = $ukm->$relation();
                    echo "relationship called... ";
                    
                    // Test getting results
                    if ($relation === 'publishedEvents') {
                        $data = $result->limit(5)->get();
                    } else {
                        $data = $result->get();
                    }
                    echo "data retrieved... ";
                    
                    echo "✅ OK (count: " . $data->count() . ")\n";
                } else {
                    echo "❌ Method does not exist\n";
                }
                
            } catch (Exception $e) {
                echo "❌ ERROR: " . $e->getMessage() . "\n";
                if (strpos($e->getMessage(), 'method_exists') !== false) {
                    echo "       🔍 This is the method_exists error!\n";
                    echo "       📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
                }
            }
        }
        echo "\n";
    }
    
    echo "3. Testing specific UKM show method...\n";
    
    $ukm = \App\Models\Ukm::first();
    if ($ukm) {
        try {
            echo "   Testing show method for: {$ukm->name}\n";
            
            // Simulate show method logic step by step
            echo "   Step 1: Loading leader... ";
            $ukm->load(['leader']);
            echo "✅\n";
            
            echo "   Step 2: Loading activeMembers... ";
            $ukm->load(['activeMembers']);
            echo "✅\n";
            
            echo "   Step 3: Loading achievements manually... ";
            $achievements = $ukm->achievements()->get();
            $ukm->setRelation('achievements', $achievements);
            echo "✅\n";
            
            echo "   Step 4: Loading publishedEvents manually... ";
            $publishedEvents = $ukm->publishedEvents()->limit(5)->get();
            $ukm->setRelation('publishedEvents', $publishedEvents);
            echo "✅\n";
            
            echo "   ✅ All steps completed successfully\n";
            
        } catch (Exception $e) {
            echo "❌ Show method error: " . $e->getMessage() . "\n";
            echo "   📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
        }
    }
    
    echo "\n4. Testing Event model relationships...\n";
    
    // Check if Event model has issues
    try {
        $event = \App\Models\Event::first();
        if ($event) {
            echo "   Testing Event model: {$event->title}\n";
            
            // Test event relationships that might cause issues
            $eventRelations = ['ukm', 'registrations', 'attendances'];
            
            foreach ($eventRelations as $relation) {
                try {
                    echo "     Testing {$relation}... ";
                    if (method_exists($event, $relation)) {
                        $result = $event->$relation()->get();
                        echo "✅ OK (count: " . $result->count() . ")\n";
                    } else {
                        echo "❌ Method does not exist\n";
                    }
                } catch (Exception $e) {
                    echo "❌ ERROR: " . $e->getMessage() . "\n";
                }
            }
        } else {
            echo "   No events found\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Event model error: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== DEBUG COMPLETED ===\n";
    
} catch (\Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    echo "📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "📋 Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
