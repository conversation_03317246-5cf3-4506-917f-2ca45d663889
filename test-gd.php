<?php

echo "Testing GD Extension...\n";

if (extension_loaded('gd')) {
    echo "✅ GD Extension is loaded!\n\n";
    
    $info = gd_info();
    echo "GD Version: " . $info['GD Version'] . "\n";
    echo "JPEG Support: " . ($info['JPEG Support'] ? 'Yes' : 'No') . "\n";
    echo "PNG Support: " . ($info['PNG Support'] ? 'Yes' : 'No') . "\n";
    echo "GIF Read Support: " . ($info['GIF Read Support'] ? 'Yes' : 'No') . "\n";
    echo "GIF Create Support: " . ($info['GIF Create Support'] ? 'Yes' : 'No') . "\n";
    echo "WebP Support: " . (isset($info['WebP Support']) && $info['WebP Support'] ? 'Yes' : 'No') . "\n";
    
    echo "\n✅ GD is ready for certificate generation!\n";
} else {
    echo "❌ GD Extension is NOT loaded!\n";
    echo "Please enable GD extension in php.ini\n";
}
