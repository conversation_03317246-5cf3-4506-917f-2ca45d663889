<?php
require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== INSERTING UKM DATA ===\n\n";

use App\Models\Ukm;

// Clear existing UKM data
echo "1. Clearing existing UKM data...\n";
$existingCount = Ukm::count();
if ($existingCount > 0) {
    Ukm::query()->delete();
    echo "   ✅ Deleted {$existingCount} existing UKMs\n";
} else {
    echo "   ℹ️  No existing UKM data found\n";
}
echo "";

echo "2. Creating UKM data...\n";

$ukms = [
    [
        'name' => 'UKM Badminton',
        'slug' => 'badminton',
        'description' => 'Unit Kegiatan Mahasiswa Badminton Telkom Jakarta adalah wadah bagi mahasiswa yang memiliki passion dalam olahraga badminton. Kami mengembangkan kemampuan teknik, taktik, dan mental bertanding melalui latihan rutin dan kompetisi.',
        'category' => 'Olahraga',
        'email' => '<EMAIL>',
        'phone' => '081234567801',
        'instagram' => '@ukmbadminton_telkomjkt',
        'recruitment_open' => true,
        'max_members' => 50,
        'current_members' => 35,
        'status' => 'active',
        'established_year' => 2018,
        'vision' => 'Menjadi UKM badminton terdepan yang menghasilkan atlet berprestasi dan berkarakter.',
        'mission' => 'Mengembangkan potensi mahasiswa dalam bidang badminton melalui latihan berkualitas, kompetisi, dan pembinaan karakter.',
        'meeting_schedule' => 'Selasa & Kamis 16:00-18:00, Sabtu 08:00-10:00',
        'meeting_location' => 'GOR Telkom University Jakarta'
    ],
    [
        'name' => 'UKM DPM (Dewan Perwakilan Mahasiswa)',
        'slug' => 'dpm',
        'description' => 'Dewan Perwakilan Mahasiswa (DPM) adalah lembaga legislatif mahasiswa yang berperan sebagai pengawas dan mitra BEM dalam menjalankan program kerja kemahasiswaan.',
        'category' => 'Politik & Pemerintahan',
        'email' => '<EMAIL>',
        'phone' => '081234567802',
        'instagram' => '@dpm_telkomjkt',
        'recruitment_open' => true,
        'max_members' => 25,
        'current_members' => 20,
        'status' => 'active',
        'established_year' => 2017,
        'vision' => 'Menjadi lembaga legislatif mahasiswa yang aspiratif, demokratis, dan berintegritas.',
        'mission' => 'Menyuarakan aspirasi mahasiswa, mengawasi kinerja eksekutif mahasiswa, dan memperjuangkan kepentingan mahasiswa.',
        'meeting_schedule' => 'Senin 19:00-21:00',
        'meeting_location' => 'Ruang Sidang Kemahasiswaan'
    ],
    [
        'name' => 'UKM Esport',
        'slug' => 'esport',
        'description' => 'UKM Esport Telkom Jakarta adalah komunitas gamers yang fokus pada pengembangan kemampuan bermain game kompetitif. Kami menaungi berbagai divisi game seperti Mobile Legends, PUBG Mobile, Valorant, dan Dota 2.',
        'category' => 'Teknologi & Gaming',
        'email' => '<EMAIL>',
        'phone' => '081234567803',
        'instagram' => '@esport_telkomjkt',
        'website' => 'https://esport.telkomuniversity.ac.id',
        'recruitment_open' => true,
        'max_members' => 80,
        'current_members' => 65,
        'status' => 'active',
        'established_year' => 2019,
        'vision' => 'Menjadi UKM esport terdepan yang menghasilkan atlet esport berprestasi tingkat nasional.',
        'mission' => 'Mengembangkan ekosistem esport di kampus, membina atlet esport berkualitas, dan membangun komunitas gaming yang positif.',
        'meeting_schedule' => 'Senin, Rabu, Jumat 19:00-22:00',
        'meeting_location' => 'Lab Gaming & Multimedia'
    ],
    [
        'name' => 'UKM Futsal',
        'slug' => 'futsal',
        'description' => 'UKM Futsal Telkom Jakarta adalah wadah bagi mahasiswa yang memiliki passion dalam olahraga futsal. Kami mengembangkan teknik individu, kerjasama tim, dan strategi permainan melalui latihan intensif.',
        'category' => 'Olahraga',
        'email' => '<EMAIL>',
        'phone' => '081234567804',
        'instagram' => '@futsal_telkomjkt',
        'recruitment_open' => true,
        'max_members' => 40,
        'current_members' => 32,
        'status' => 'active',
        'established_year' => 2018,
        'vision' => 'Menjadi tim futsal universitas yang kompetitif dan berprestasi di tingkat regional.',
        'mission' => 'Mengembangkan kemampuan futsal mahasiswa, membangun sportivitas, dan mengharumkan nama Telkom University Jakarta.',
        'meeting_schedule' => 'Selasa & Kamis 16:00-18:00, Minggu 08:00-10:00',
        'meeting_location' => 'Lapangan Futsal Kampus'
    ],
    [
        'name' => 'UKM IMMA (Ikatan Mahasiswa Muslim Akuntansi)',
        'slug' => 'imma',
        'description' => 'IMMA adalah organisasi yang menghimpun mahasiswa muslim khususnya dari program studi akuntansi dan ekonomi. Kami fokus pada pengembangan spiritual, akademik, dan sosial.',
        'category' => 'Keagamaan',
        'email' => '<EMAIL>',
        'phone' => '081234567805',
        'instagram' => '@imma_telkomjkt',
        'recruitment_open' => true,
        'max_members' => 60,
        'current_members' => 45,
        'status' => 'active',
        'established_year' => 2017,
        'vision' => 'Menjadi wadah pengembangan mahasiswa muslim yang berakhlak mulia dan kompeten di bidang ekonomi.',
        'mission' => 'Menyelenggarakan kajian Islam, mengembangkan pemahaman ekonomi syariah, dan membangun ukhuwah islamiyah.',
        'meeting_schedule' => 'Jumat 13:00-15:00',
        'meeting_location' => 'Musholla Kampus'
    ],
    [
        'name' => 'UKM Mapala (Mahasiswa Pecinta Alam)',
        'slug' => 'mapala',
        'description' => 'UKM Mapala Telkom Jakarta adalah komunitas mahasiswa yang memiliki kecintaan terhadap alam dan lingkungan. Kami melakukan kegiatan pendakian gunung, camping, rock climbing, dan konservasi alam.',
        'category' => 'Alam & Lingkungan',
        'email' => '<EMAIL>',
        'phone' => '081234567806',
        'instagram' => '@mapala_telkomjkt',
        'recruitment_open' => true,
        'max_members' => 45,
        'current_members' => 38,
        'status' => 'active',
        'established_year' => 2018,
        'vision' => 'Menjadi komunitas pecinta alam yang berperan aktif dalam pelestarian lingkungan dan pembentukan karakter.',
        'mission' => 'Mengembangkan kecintaan terhadap alam, melatih mental dan fisik melalui kegiatan alam, serta berkontribusi dalam konservasi lingkungan.',
        'meeting_schedule' => 'Sabtu 14:00-17:00',
        'meeting_location' => 'Basecamp Mapala'
    ],
    [
        'name' => 'UKM PMK (Persekutuan Mahasiswa Kristen)',
        'slug' => 'pmk',
        'description' => 'PMK adalah wadah persekutuan bagi mahasiswa Kristen di Telkom University Jakarta. Kami menyelenggarakan ibadah, fellowship, retreat, dan pelayanan sosial.',
        'category' => 'Keagamaan',
        'email' => '<EMAIL>',
        'phone' => '081234567807',
        'instagram' => '@pmk_telkomjkt',
        'recruitment_open' => true,
        'max_members' => 40,
        'current_members' => 28,
        'status' => 'active',
        'established_year' => 2017,
        'vision' => 'Menjadi persekutuan yang membawa terang Kristus dalam kehidupan kampus dan masyarakat.',
        'mission' => 'Menguatkan iman mahasiswa Kristen, membangun persekutuan yang solid, dan melayani sesama dengan kasih.',
        'meeting_schedule' => 'Kamis 18:00-20:00',
        'meeting_location' => 'Ruang Persekutuan'
    ],
    [
        'name' => 'UKM Seni Budaya',
        'slug' => 'seni-budaya',
        'description' => 'UKM Seni Budaya Telkom Jakarta adalah wadah kreativitas mahasiswa dalam bidang seni dan budaya Indonesia. Kami mengembangkan berbagai kesenian tradisional seperti tari, musik tradisional, teater, dan seni rupa.',
        'category' => 'Seni & Budaya',
        'email' => '<EMAIL>',
        'phone' => '081234567808',
        'instagram' => '@senibudaya_telkomjkt',
        'recruitment_open' => true,
        'max_members' => 55,
        'current_members' => 42,
        'status' => 'active',
        'established_year' => 2017,
        'vision' => 'Menjadi pusat pengembangan seni dan budaya yang melestarikan kearifan lokal Indonesia.',
        'mission' => 'Mengembangkan bakat seni mahasiswa, melestarikan budaya Indonesia, dan memperkenalkan kesenian tradisional kepada generasi muda.',
        'meeting_schedule' => 'Rabu & Sabtu 15:00-18:00',
        'meeting_location' => 'Studio Seni & Budaya'
    ],
    [
        'name' => 'UKM Sistem Informasi',
        'slug' => 'sistem-informasi',
        'description' => 'UKM Sistem Informasi adalah komunitas mahasiswa yang fokus pada pengembangan teknologi informasi dan sistem. Kami menyelenggarakan workshop programming, seminar teknologi, hackathon, dan project development.',
        'category' => 'Teknologi & Informasi',
        'email' => '<EMAIL>',
        'phone' => '081234567809',
        'instagram' => '@si_telkomjkt',
        'website' => 'https://si.telkomuniversity.ac.id',
        'recruitment_open' => true,
        'max_members' => 70,
        'current_members' => 58,
        'status' => 'active',
        'established_year' => 2018,
        'vision' => 'Menjadi komunitas IT terdepan yang menghasilkan lulusan kompeten dan inovatif.',
        'mission' => 'Mengembangkan kemampuan programming, membangun solusi teknologi, dan mempersiapkan mahasiswa untuk industri IT.',
        'meeting_schedule' => 'Selasa & Kamis 19:00-21:00',
        'meeting_location' => 'Lab Komputer & Programming'
    ]
];

$created = 0;
$failed = 0;

foreach ($ukms as $ukmData) {
    try {
        $ukm = Ukm::create($ukmData);
        echo "   ✅ {$ukm->name}\n";
        $created++;
    } catch (Exception $e) {
        echo "   ❌ Failed: {$ukmData['name']} - " . $e->getMessage() . "\n";
        $failed++;
    }
}

echo "\n=== RESULT ===\n";
echo "✅ Successfully created: {$created} UKMs\n";
echo "❌ Failed: {$failed} UKMs\n";
echo "📊 Total UKMs in database: " . Ukm::count() . "\n";

echo "\n📋 UKM Categories:\n";
$categories = Ukm::select('category')->distinct()->pluck('category');
foreach ($categories as $category) {
    $count = Ukm::where('category', $category)->count();
    echo "   • {$category}: {$count} UKMs\n";
}

echo "\n🌐 Test URLs:\n";
echo "   UKM Index: http://localhost:8000/ukm\n";
echo "   Sample UKM: http://localhost:8000/ukm/badminton\n";
echo "   Admin UKMs: http://localhost:8000/admin/ukms\n";

echo "\n=== COMPLETE ===\n";
?>
