<?php

echo "=== CHECKING TABLE STRUCTURE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "❌ Table 'users' does not exist\n";
        exit;
    }
    
    echo "✅ Table 'users' exists\n\n";
    
    // Show table structure
    echo "📋 TABLE STRUCTURE:\n";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) - {$column['Null']} - {$column['Key']} - {$column['Default']}\n";
    }
    
    echo "\n📊 CURRENT DATA:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetchColumn();
    echo "Total records: {$count}\n";
    
    if ($count > 0) {
        echo "\nExisting records:\n";
        $stmt = $pdo->query("SELECT * FROM users LIMIT 5");
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Name: {$user['name']}, Email: {$user['email']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
