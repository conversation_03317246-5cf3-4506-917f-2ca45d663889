<?php

echo "=== FIXING SPATIE PERMISSION ROLES ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Updating database enum to support ketua_ukm...\n";
    
    // Update enum first
    try {
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('student', 'ketua_ukm', 'admin') DEFAULT 'student'");
        echo "   ✅ Database enum updated!\n";
    } catch (Exception $e) {
        echo "   ⚠️  Enum update: " . $e->getMessage() . "\n";
    }
    
    echo "2. Creating Spatie Permission roles...\n";
    
    // Reset cached roles and permissions
    app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    
    // Create roles
    $studentRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'student']);
    $ketuaUkmRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'ketua_ukm']);
    $adminRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin']);
    
    echo "   ✅ Created roles: student, ketua_ukm, admin\n";
    
    echo "3. Creating permissions...\n";
    
    $permissions = [
        'view_dashboard',
        'join_ukm',
        'register_event',
        'view_profile',
        'edit_profile',
        'manage_ukm',
        'edit_ukm',
        'create_event',
        'manage_ukm_members',
        'view_ukm_dashboard',
        'manage_users',
        'manage_all_ukms',
        'manage_all_events',
        'view_admin_dashboard',
        'approve_registrations',
    ];

    foreach ($permissions as $permission) {
        \Spatie\Permission\Models\Permission::firstOrCreate(['name' => $permission]);
    }
    
    echo "   ✅ Created " . count($permissions) . " permissions\n";
    
    echo "4. Assigning permissions to roles...\n";
    
    // Student permissions
    $studentRole->syncPermissions([
        'view_dashboard',
        'join_ukm',
        'register_event',
        'view_profile',
        'edit_profile',
    ]);
    
    // Ketua UKM permissions
    $ketuaUkmRole->syncPermissions([
        'view_dashboard',
        'join_ukm',
        'register_event',
        'view_profile',
        'edit_profile',
        'manage_ukm',
        'edit_ukm',
        'create_event',
        'manage_ukm_members',
        'view_ukm_dashboard',
    ]);
    
    // Admin permissions (all)
    $adminRole->syncPermissions(\Spatie\Permission\Models\Permission::all());
    
    echo "   ✅ Permissions assigned to roles\n";
    
    echo "5. Syncing existing users with Spatie roles...\n";
    
    $users = \App\Models\User::all();
    $synced = 0;
    
    foreach ($users as $user) {
        if ($user->role) {
            // Remove all existing roles
            $user->syncRoles([]);
            
            // Assign role based on role column
            $user->assignRole($user->role);
            $synced++;
        }
    }
    
    echo "   ✅ Synced {$synced} users with Spatie roles\n";
    
    echo "6. Testing role assignment...\n";
    
    $testUser = \App\Models\User::where('role', 'student')->first();
    if ($testUser) {
        echo "   Testing with user: {$testUser->name}\n";
        echo "   Current role: {$testUser->role}\n";
        echo "   Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . "\n";
        
        // Test update to ketua_ukm
        $testUser->update(['role' => 'ketua_ukm']);
        $testUser->syncRoleWithSpatie();
        $testUser->refresh();
        
        echo "   After update to ketua_ukm:\n";
        echo "     Role column: {$testUser->role}\n";
        echo "     Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . "\n";
        echo "     isKetuaUkm(): " . ($testUser->isKetuaUkm() ? 'true' : 'false') . "\n";
        
        if ($testUser->role === 'ketua_ukm' && $testUser->hasRole('ketua_ukm')) {
            echo "   ✅ SUCCESS: Role assignment works!\n";
            
            // Revert back
            $testUser->update(['role' => 'student']);
            $testUser->syncRoleWithSpatie();
            echo "   Reverted back to student\n";
        } else {
            echo "   ❌ FAILED: Role assignment failed\n";
        }
    }
    
    echo "\n=== SPATIE ROLES FIX COMPLETED ===\n";
    echo "✅ Database enum supports: student, ketua_ukm, admin\n";
    echo "✅ Spatie roles created and synced\n";
    echo "✅ Permissions assigned\n";
    echo "✅ Users synced with Spatie roles\n";
    echo "\nNow you can assign 'ketua_ukm' role and it will persist!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
