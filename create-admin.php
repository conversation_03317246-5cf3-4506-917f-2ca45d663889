<?php

echo "=== CREATING ADMIN USER ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Creating admin user...\n";
    
    // Check if admin already exists
    $existingAdmin = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if ($existingAdmin) {
        echo "   ⚠️  Admin user already exists!\n";
        echo "   Email: {$existingAdmin->email}\n";
        echo "   Name: {$existingAdmin->name}\n";
        echo "   Role: {$existingAdmin->role}\n";
        echo "   Status: {$existingAdmin->status}\n";
        
        // Update password if needed
        $existingAdmin->update([
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'status' => 'active',
        ]);
        echo "   ✅ Password updated to: admin123\n";
        echo "   ✅ Status set to: active\n";
    } else {
        // Create new admin
        $admin = \App\Models\User::create([
            'nim' => 'ADMIN001',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Administrasi',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        
        echo "   ✅ Admin user created successfully!\n";
        echo "   Email: {$admin->email}\n";
        echo "   Name: {$admin->name}\n";
        echo "   Role: {$admin->role}\n";
        echo "   Status: {$admin->status}\n";
    }
    
    echo "\n2. Testing admin login credentials...\n";
    
    $testAdmin = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if ($testAdmin) {
        echo "   ✅ Admin found in database\n";
        echo "   ID: {$testAdmin->id}\n";
        echo "   NIM: {$testAdmin->nim}\n";
        echo "   Name: {$testAdmin->name}\n";
        echo "   Email: {$testAdmin->email}\n";
        echo "   Role: {$testAdmin->role}\n";
        echo "   Status: {$testAdmin->status}\n";
        echo "   Email Verified: " . ($testAdmin->email_verified_at ? 'Yes' : 'No') . "\n";
        
        // Test password
        if (\Illuminate\Support\Facades\Hash::check('admin123', $testAdmin->password)) {
            echo "   ✅ Password verification: SUCCESS\n";
        } else {
            echo "   ❌ Password verification: FAILED\n";
        }
    } else {
        echo "   ❌ Admin not found in database\n";
    }
    
    echo "\n3. Creating additional test users...\n";
    
    // Create a test student
    $student = \App\Models\User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'nim' => '1301210001',
            'name' => 'Test Student',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('student123'),
            'phone' => '081234567891',
            'gender' => 'male',
            'faculty' => 'Informatika',
            'major' => 'Sistem Informasi',
            'batch' => '2021',
            'role' => 'student',
            'status' => 'active',
            'email_verified_at' => now(),
        ]
    );
    echo "   ✅ Test student created: {$student->email} | Password: student123\n";
    
    // Create a test ketua UKM
    $ketuaUkm = \App\Models\User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'nim' => '1301210002',
            'name' => 'Test Ketua UKM',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('ketua123'),
            'phone' => '************',
            'gender' => 'female',
            'faculty' => 'Informatika',
            'major' => 'Teknik Informatika',
            'batch' => '2021',
            'role' => 'ketua_ukm',
            'status' => 'active',
            'email_verified_at' => now(),
        ]
    );
    echo "   ✅ Test ketua UKM created: {$ketuaUkm->email} | Password: ketua123\n";
    
    echo "\n4. Summary of created accounts...\n";
    
    $users = \App\Models\User::whereIn('role', ['admin', 'student', 'ketua_ukm'])->get();
    
    foreach ($users as $user) {
        echo "   👤 {$user->name} ({$user->role})\n";
        echo "      Email: {$user->email}\n";
        echo "      Status: {$user->status}\n";
        echo "      Password: " . ($user->role === 'admin' ? 'admin123' : ($user->role === 'student' ? 'student123' : 'ketua123')) . "\n";
        echo "\n";
    }
    
    echo "=== ADMIN CREATION COMPLETED ===\n";
    echo "✅ Admin account ready!\n";
    echo "\n🔑 LOGIN CREDENTIALS:\n";
    echo "📧 Email: <EMAIL>\n";
    echo "🔒 Password: admin123\n";
    echo "👤 Role: admin\n";
    echo "✅ Status: active\n";
    echo "\n🌐 Login URL: http://localhost:8000/login\n";
    echo "\n📝 Additional Test Accounts:\n";
    echo "   Student: <EMAIL> | student123\n";
    echo "   Ketua UKM: <EMAIL> | ketua123\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
