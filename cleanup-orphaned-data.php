<?php

echo "=== CLEANING UP ORPHANED DATA ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // 1. Clean up orphaned ukm_members records
    echo "🧹 CLEANING ORPHANED UKM_MEMBERS RECORDS:\n";
    $stmt = $pdo->query("
        SELECT um.id, um.user_id, um.ukm_id, um.status
        FROM ukm_members um
        LEFT JOIN users u ON um.user_id = u.id
        WHERE u.id IS NULL
    ");
    $orphanedMembers = $stmt->fetchAll();
    
    if (count($orphanedMembers) > 0) {
        echo "Found " . count($orphanedMembers) . " orphaned ukm_members records:\n";
        foreach ($orphanedMembers as $record) {
            echo "- Deleting ukm_members.id: {$record['id']} (user_id: {$record['user_id']})\n";
            $stmt = $pdo->prepare("DELETE FROM ukm_members WHERE id = ?");
            $stmt->execute([$record['id']]);
        }
        echo "✅ Cleaned up " . count($orphanedMembers) . " orphaned ukm_members records\n";
    } else {
        echo "✅ No orphaned ukm_members records found\n";
    }
    
    // 2. Clean up orphaned UKM leader references
    echo "\n🧹 CLEANING ORPHANED UKM LEADER REFERENCES:\n";
    $stmt = $pdo->query("
        SELECT ukm.id, ukm.name, ukm.leader_id
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
        WHERE ukm.leader_id IS NOT NULL AND u.id IS NULL
    ");
    $orphanedLeaders = $stmt->fetchAll();
    
    if (count($orphanedLeaders) > 0) {
        echo "Found " . count($orphanedLeaders) . " UKMs with orphaned leader references:\n";
        foreach ($orphanedLeaders as $record) {
            echo "- Clearing leader for UKM: {$record['name']} (leader_id: {$record['leader_id']})\n";
            $stmt = $pdo->prepare("UPDATE ukms SET leader_id = NULL WHERE id = ?");
            $stmt->execute([$record['id']]);
        }
        echo "✅ Cleaned up " . count($orphanedLeaders) . " orphaned leader references\n";
    } else {
        echo "✅ No orphaned leader references found\n";
    }
    
    // 3. Fix current_members count for all UKMs
    echo "\n📊 FIXING CURRENT_MEMBERS COUNT:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id,
            ukm.name,
            ukm.current_members as stored_count,
            COUNT(um.id) as actual_count
        FROM ukms ukm
        LEFT JOIN ukm_members um ON ukm.id = um.ukm_id AND um.status = 'active'
        LEFT JOIN users u ON um.user_id = u.id
        WHERE um.user_id IS NULL OR u.id IS NOT NULL
        GROUP BY ukm.id, ukm.name, ukm.current_members
    ");
    $counts = $stmt->fetchAll();
    
    $fixedCount = 0;
    foreach ($counts as $count) {
        if ($count['stored_count'] != $count['actual_count']) {
            echo "- Fixing UKM: {$count['name']} ({$count['stored_count']} → {$count['actual_count']})\n";
            
            $stmt = $pdo->prepare("UPDATE ukms SET current_members = ? WHERE id = ?");
            $stmt->execute([$count['actual_count'], $count['id']]);
            $fixedCount++;
        }
    }
    
    if ($fixedCount > 0) {
        echo "✅ Fixed current_members count for {$fixedCount} UKMs\n";
    } else {
        echo "✅ All UKM member counts are accurate\n";
    }
    
    // 4. Clean up orphaned event registrations
    echo "\n🧹 CLEANING ORPHANED EVENT REGISTRATIONS:\n";
    $stmt = $pdo->query("
        SELECT er.id, er.user_id, er.event_id
        FROM event_registrations er
        LEFT JOIN users u ON er.user_id = u.id
        WHERE u.id IS NULL
    ");
    $orphanedRegistrations = $stmt->fetchAll();
    
    if (count($orphanedRegistrations) > 0) {
        echo "Found " . count($orphanedRegistrations) . " orphaned event registrations:\n";
        foreach ($orphanedRegistrations as $record) {
            echo "- Deleting event_registrations.id: {$record['id']} (user_id: {$record['user_id']})\n";
            $stmt = $pdo->prepare("DELETE FROM event_registrations WHERE id = ?");
            $stmt->execute([$record['id']]);
        }
        echo "✅ Cleaned up " . count($orphanedRegistrations) . " orphaned event registrations\n";
    } else {
        echo "✅ No orphaned event registrations found\n";
    }
    
    // 5. Verify UKM Sistem Informasi specifically
    echo "\n🔍 VERIFYING UKM SISTEM INFORMASI:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id,
            ukm.name,
            ukm.current_members,
            ukm.leader_id,
            u.name as leader_name,
            COUNT(um.id) as actual_members
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
        LEFT JOIN ukm_members um ON ukm.id = um.ukm_id AND um.status = 'active'
        LEFT JOIN users mu ON um.user_id = mu.id
        WHERE ukm.name LIKE '%sistem%' OR ukm.name LIKE '%informasi%'
        GROUP BY ukm.id, ukm.name, ukm.current_members, ukm.leader_id, u.name
    ");
    $sistemInfo = $stmt->fetchAll();
    
    foreach ($sistemInfo as $ukm) {
        echo "- UKM: {$ukm['name']}\n";
        echo "  ID: {$ukm['id']}\n";
        echo "  Current Members: {$ukm['current_members']}\n";
        echo "  Actual Members: {$ukm['actual_members']}\n";
        echo "  Leader: " . ($ukm['leader_name'] ?: 'None') . "\n";
        
        if ($ukm['current_members'] != $ukm['actual_members']) {
            echo "  🔧 Fixing member count...\n";
            $stmt = $pdo->prepare("UPDATE ukms SET current_members = ? WHERE id = ?");
            $stmt->execute([$ukm['actual_members'], $ukm['id']]);
            echo "  ✅ Fixed!\n";
        } else {
            echo "  ✅ Member count is correct\n";
        }
    }
    
    echo "\n✅ Data cleanup complete!\n";
    echo "🎯 UKM Sistem Informasi should now be accessible without errors.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
