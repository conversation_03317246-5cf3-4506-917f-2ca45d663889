<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Certificate Download with GD Extension...\n\n";

try {
    // Check GD extension
    if (!extension_loaded('gd')) {
        throw new Exception('GD extension is not loaded!');
    }
    echo "✅ GD Extension is loaded\n";

    // Find an event with attendances
    $event = \App\Models\Event::whereHas('attendances', function($query) {
        $query->where('status', 'present')
              ->where('verification_status', 'verified');
    })->first();

    if (!$event) {
        echo "❌ No event with verified attendances found\n";
        echo "Please create an event and mark some attendances as verified first.\n";
        exit;
    }

    echo "✅ Found event: {$event->title}\n";

    // Get a verified attendance
    $attendance = $event->attendances()
                        ->where('status', 'present')
                        ->where('verification_status', 'verified')
                        ->first();

    if (!$attendance) {
        echo "❌ No verified attendance found\n";
        exit;
    }

    echo "✅ Found verified attendance for user: {$attendance->user->name}\n";

    // Test certificate service
    $certificateService = new \App\Services\CertificateService();

    echo "\nTesting certificate download...\n";
    
    // Test download method
    $response = $certificateService->downloadCertificate($attendance);
    
    echo "✅ Certificate download successful!\n";
    echo "Response type: " . get_class($response) . "\n";
    echo "Content type: " . $response->headers->get('Content-Type') . "\n";
    echo "Content disposition: " . $response->headers->get('Content-Disposition') . "\n";

    // Check if it's a PDF response
    if ($response->headers->get('Content-Type') === 'application/pdf') {
        echo "✅ PDF certificate generated successfully!\n";
    } else {
        echo "⚠️  Unexpected content type\n";
    }

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test completed ===\n";
