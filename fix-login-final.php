<?php

echo "=== FIX LOGIN FINAL - COMPREHENSIVE SOLUTION ===\n";

// Database connection
$host = '127.0.0.1';
$dbname = 'ukmwebv';
$username = 'root';
$password = '';

try {
    // 1. Test database connection
    echo "1. Testing database connection...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ Database connection: SUCCESS\n";
    
    // 2. Check and recreate users table
    echo "2. Checking users table...\n";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $count = $stmt->fetchColumn();
        echo "   ✅ Users table exists with {$count} records\n";
        
        if ($count == 0) {
            echo "   ⚠️  Table is empty, will insert users\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Users table issue: " . $e->getMessage() . "\n";
        echo "   🔧 Creating users table...\n";
        
        $createTable = "
        CREATE TABLE users (
            id bigint unsigned NOT NULL AUTO_INCREMENT,
            nim varchar(255) NOT NULL,
            name varchar(255) NOT NULL,
            email varchar(255) NOT NULL,
            email_verified_at timestamp NULL DEFAULT NULL,
            password varchar(255) NOT NULL,
            phone varchar(255) DEFAULT NULL,
            gender enum('male','female') NOT NULL,
            faculty varchar(255) NOT NULL,
            major varchar(255) NOT NULL,
            batch varchar(255) NOT NULL,
            bio text,
            avatar varchar(255),
            role enum('admin','student','ketua_ukm') NOT NULL DEFAULT 'student',
            status enum('active','inactive','suspended','pending') NOT NULL DEFAULT 'pending',
            last_login_at timestamp NULL DEFAULT NULL,
            remember_token varchar(100) DEFAULT NULL,
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY users_email_unique (email),
            UNIQUE KEY users_nim_unique (nim)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createTable);
        echo "   ✅ Users table created\n";
    }
    
    // 3. Clear existing users and create fresh ones
    echo "3. Creating fresh users...\n";
    
    $pdo->exec("DELETE FROM users");
    echo "   🗑️  Existing users cleared\n";
    
    // 4. Create admin user with multiple password attempts
    echo "4. Creating admin user...\n";
    
    $passwords = [
        'admin123' => password_hash('admin123', PASSWORD_DEFAULT),
        'password' => password_hash('password', PASSWORD_DEFAULT),
        '123456' => password_hash('123456', PASSWORD_DEFAULT)
    ];
    
    foreach ($passwords as $plainPassword => $hashedPassword) {
        echo "   Testing password: {$plainPassword}\n";
        echo "   Hash: " . substr($hashedPassword, 0, 30) . "...\n";
        
        // Verify hash works
        if (password_verify($plainPassword, $hashedPassword)) {
            echo "   ✅ Hash verification: SUCCESS\n";
            
            // Insert admin with this password
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO users (
                        nim, name, email, password, phone, gender, faculty, major, batch,
                        role, status, email_verified_at, created_at, updated_at
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
                    )
                ");

                $result = $stmt->execute([
                    'ADMIN001',
                    'Administrator',
                    '<EMAIL>',
                    $hashedPassword,
                    '081234567890',
                    'male',
                    'Administrasi',
                    'Sistem Informasi',
                    '2024',
                    'admin',
                    'active',
                    date('Y-m-d H:i:s')
                ]);
                
                if ($result) {
                    echo "   ✅ Admin user created with password: {$plainPassword}\n";
                    break; // Success, exit loop
                }
            } catch (Exception $e) {
                echo "   ❌ Insert failed: " . $e->getMessage() . "\n";
                continue; // Try next password
            }
        } else {
            echo "   ❌ Hash verification: FAILED\n";
        }
    }
    
    // 5. Create student user
    echo "5. Creating student user...\n";
    
    $studentHash = password_hash('student123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (
            nim, name, email, password, phone, gender, faculty, major, batch,
            role, status, email_verified_at, created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
        )
    ");

    $stmt->execute([
        '1301210001',
        'Test Student',
        '<EMAIL>',
        $studentHash,
        '081234567891',
        'male',
        'Informatika',
        'Sistem Informasi',
        '2021',
        'student',
        'active',
        date('Y-m-d H:i:s')
    ]);
    
    echo "   ✅ Student user created\n";
    
    // 6. Verify all users
    echo "6. Verifying created users...\n";
    
    $stmt = $pdo->query("SELECT id, nim, name, email, role, status FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        echo "   - {$user['name']} ({$user['email']}) - {$user['role']} - {$user['status']}\n";
    }
    
    // 7. Test login simulation
    echo "7. Testing login simulation...\n";
    
    $testCredentials = [
        ['email' => '<EMAIL>', 'password' => 'admin123'],
        ['email' => '<EMAIL>', 'password' => 'password'],
        ['email' => '<EMAIL>', 'password' => '123456'],
        ['nim' => 'ADMIN001', 'password' => 'admin123'],
        ['email' => '<EMAIL>', 'password' => 'student123'],
    ];
    
    foreach ($testCredentials as $cred) {
        $field = isset($cred['email']) ? 'email' : 'nim';
        $value = isset($cred['email']) ? $cred['email'] : $cred['nim'];
        
        echo "\n   Testing: {$field} = {$value}, password = {$cred['password']}\n";
        
        $stmt = $pdo->prepare("SELECT * FROM users WHERE {$field} = ?");
        $stmt->execute([$value]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "     ✅ User found: {$user['name']}\n";
            echo "     Status: {$user['status']}\n";
            
            if (password_verify($cred['password'], $user['password'])) {
                echo "     ✅ Password correct\n";
                
                if ($user['status'] === 'active') {
                    echo "     ✅ LOGIN SHOULD WORK!\n";
                } else {
                    echo "     ❌ User not active\n";
                }
            } else {
                echo "     ❌ Password incorrect\n";
                echo "     Stored hash: " . substr($user['password'], 0, 30) . "...\n";
            }
        } else {
            echo "     ❌ User not found\n";
        }
    }
    
    // 8. Final summary
    echo "\n8. Final summary...\n";
    
    $adminUser = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $adminUser->execute(['<EMAIL>']);
    $admin = $adminUser->fetch();
    
    if ($admin) {
        echo "   ✅ Admin user ready\n";
        echo "   Email: {$admin['email']}\n";
        echo "   NIM: {$admin['nim']}\n";
        echo "   Status: {$admin['status']}\n";
        echo "   Role: {$admin['role']}\n";
        
        // Test all common passwords
        $commonPasswords = ['admin123', 'password', '123456', 'admin'];
        foreach ($commonPasswords as $testPass) {
            if (password_verify($testPass, $admin['password'])) {
                echo "   🔑 Working password: {$testPass}\n";
                break;
            }
        }
    }
    
    echo "\n=== LOGIN FIX COMPLETED ===\n";
    echo "🎯 Try logging in with:\n";
    echo "📧 Email: <EMAIL>\n";
    echo "🔒 Password: admin123 (or password, or 123456)\n";
    echo "🌐 URL: http://localhost:8000/login\n";
    echo "\n💡 If still not working, the issue is in Laravel authentication logic\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
