<?php
/**
 * Debug UKM logo display issues
 */

echo "=== DEBUGGING UKM LOGO DISPLAY ISSUES ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Get IMMA UKM
    $ukm = \App\Models\Ukm::where('name', 'IMMA')->first();
    
    if (!$ukm) {
        echo "❌ UKM IMMA not found\n";
        exit;
    }
    
    echo "✅ Found UKM: {$ukm->name}\n";
    echo "📄 Slug: {$ukm->slug}\n";
    echo "🖼️ Logo path: {$ukm->logo}\n\n";
    
    if ($ukm->logo) {
        $storagePath = storage_path('app/public/' . $ukm->logo);
        $publicPath = public_path('storage/' . $ukm->logo);
        $webUrl = asset('storage/' . $ukm->logo);
        
        echo "=== FILE PATHS ===\n";
        echo "Storage: $storagePath\n";
        echo "Public: $publicPath\n";
        echo "Web URL: $webUrl\n\n";
        
        echo "=== FILE EXISTENCE ===\n";
        echo "Storage file exists: " . (file_exists($storagePath) ? "✅" : "❌") . "\n";
        echo "Public file exists: " . (file_exists($publicPath) ? "✅" : "❌") . "\n\n";
        
        if (file_exists($storagePath)) {
            $fileInfo = getimagesize($storagePath);
            echo "=== FILE INFO ===\n";
            echo "File size: " . filesize($storagePath) . " bytes\n";
            echo "Image dimensions: " . ($fileInfo ? $fileInfo[0] . "x" . $fileInfo[1] : "Not an image") . "\n";
            echo "MIME type: " . ($fileInfo ? $fileInfo['mime'] : "Unknown") . "\n\n";
        }
        
        // Test if we can create a simple HTML to test the image
        $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Logo Test</title>
    <style>
        .logo { width: 100px; height: 100px; border: 2px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>UKM Logo Test</h1>
    <h2>UKM: {$ukm->name}</h2>
    <p>Logo path: {$ukm->logo}</p>
    <p>Web URL: $webUrl</p>
    
    <h3>Logo Display Test:</h3>
    <img src=\"$webUrl\" alt=\"{$ukm->name}\" class=\"logo\" 
         onload=\"document.getElementById('status').innerHTML='<span class=success>✅ Image loaded successfully!</span>'\"
         onerror=\"document.getElementById('status').innerHTML='<span class=error>❌ Image failed to load!</span>'\">
    
    <p id=\"status\">Loading...</p>
    
    <h3>Direct File Access Test:</h3>
    <p><a href=\"$webUrl\" target=\"_blank\">Click to test direct access</a></p>
</body>
</html>";
        
        file_put_contents(public_path('logo-test.html'), $testHtml);
        echo "=== TEST FILE CREATED ===\n";
        echo "🔗 Open: http://127.0.0.1:8000/logo-test.html\n\n";
    } else {
        echo "❌ No logo set for this UKM\n";
    }
    
    echo "=== ADMIN URLS TO TEST ===\n";
    echo "🔗 Admin View: http://127.0.0.1:8000/admin/ukms/{$ukm->slug}\n";
    echo "🔗 Admin Edit: http://127.0.0.1:8000/admin/ukms/{$ukm->slug}/edit\n\n";
    
    echo "=== DEBUG COMPLETED ===\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
