<?php

echo "=== COMPREHENSIVE DATABASE DIAGNOSIS ===\n\n";

// Database configuration
$host = '127.0.0.1';
$dbname = 'ukmwebv';
$username = 'root';
$password = '';

try {
    // 1. Test database connection
    echo "1. 🔌 TESTING DATABASE CONNECTION...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ Database connection: SUCCESS\n\n";
    
    // 2. Check if database exists and is accessible
    echo "2. 📊 CHECKING DATABASE STATUS...\n";
    $stmt = $pdo->query("SELECT DATABASE() as current_db");
    $currentDb = $stmt->fetch()['current_db'];
    echo "   ✅ Current database: $currentDb\n";
    
    // 3. List all tables
    echo "\n3. 📋 CHECKING TABLES...\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "   📊 Total tables: " . count($tables) . "\n";
    
    if (empty($tables)) {
        echo "   ❌ NO TABLES FOUND! Database is empty.\n";
        echo "   🔧 SOLUTION: Run migrations to create tables\n\n";
        return;
    }
    
    foreach ($tables as $table) {
        echo "   - $table\n";
    }
    
    // 4. Check critical tables
    echo "\n4. 🔍 CHECKING CRITICAL TABLES...\n";
    $criticalTables = ['users', 'ukms', 'events', 'event_registrations', 'migrations'];
    
    foreach ($criticalTables as $table) {
        if (in_array($table, $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "   ✅ $table: $count records\n";
            
            // Special check for users table
            if ($table === 'users' && $count > 0) {
                $stmt = $pdo->query("SELECT email, role, status FROM users LIMIT 5");
                $users = $stmt->fetchAll();
                echo "      👥 Sample users:\n";
                foreach ($users as $user) {
                    echo "         - {$user['email']} ({$user['role']}) - {$user['status']}\n";
                }
            }
        } else {
            echo "   ❌ $table: TABLE MISSING\n";
        }
    }
    
    // 5. Check migrations status
    echo "\n5. 🔄 CHECKING MIGRATIONS...\n";
    if (in_array('migrations', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM migrations");
        $migrationCount = $stmt->fetch()['count'];
        echo "   📊 Total migrations run: $migrationCount\n";
        
        if ($migrationCount > 0) {
            $stmt = $pdo->query("SELECT migration, batch FROM migrations ORDER BY batch DESC, id DESC LIMIT 5");
            $recentMigrations = $stmt->fetchAll();
            echo "   📋 Recent migrations:\n";
            foreach ($recentMigrations as $migration) {
                echo "      - {$migration['migration']} (batch {$migration['batch']})\n";
            }
        }
    } else {
        echo "   ❌ Migrations table missing\n";
    }
    
    // 6. Check for data loss indicators
    echo "\n6. 🚨 CHECKING FOR DATA LOSS INDICATORS...\n";
    
    // Check if fresh migration was run recently
    $stmt = $pdo->query("SELECT migration FROM migrations WHERE migration LIKE '%create_users_table%'");
    $userTableMigration = $stmt->fetch();
    
    if ($userTableMigration) {
        echo "   ⚠️  Users table migration found - checking if data was lost...\n";
        
        // Check if there are any users
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $userCount = $stmt->fetch()['count'];
        
        if ($userCount == 0) {
            echo "   🚨 CRITICAL: Users table exists but is EMPTY!\n";
            echo "   🔧 CAUSE: Migration fresh or database reset without seeding\n";
            echo "   💡 SOLUTION: Run seeder to recreate users\n";
        }
    }
    
    // 7. Check Laravel configuration
    echo "\n7. ⚙️  CHECKING LARAVEL CONFIGURATION...\n";
    
    // Check if .env file exists and has correct database config
    if (file_exists('.env')) {
        $envContent = file_get_contents('.env');
        if (strpos($envContent, 'DB_DATABASE=ukmwebv') !== false) {
            echo "   ✅ .env database configuration: CORRECT\n";
        } else {
            echo "   ❌ .env database configuration: INCORRECT\n";
            echo "   🔧 Check DB_DATABASE setting in .env file\n";
        }
    } else {
        echo "   ❌ .env file: MISSING\n";
    }
    
    // 8. Provide solutions
    echo "\n8. 🔧 RECOMMENDED SOLUTIONS...\n";
    
    if (count($tables) == 0) {
        echo "   📝 Database is empty - run migrations:\n";
        echo "      php artisan migrate\n";
        echo "      php artisan db:seed\n";
    } else {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $userCount = $stmt->fetch()['count'];
        
        if ($userCount == 0) {
            echo "   👥 No users found - run seeder:\n";
            echo "      php artisan db:seed --class=AdminUserSeeder\n";
            echo "   OR access: http://localhost:8000/create-users\n";
        } else {
            echo "   🔍 Users exist but login fails - check:\n";
            echo "      - Password hashing (bcrypt)\n";
            echo "      - User status (should be 'active')\n";
            echo "      - Email verification\n";
        }
    }
    
    echo "\n✅ DIAGNOSIS COMPLETE!\n";
    
} catch (PDOException $e) {
    echo "❌ DATABASE CONNECTION FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n\n";
    
    echo "🔧 POSSIBLE CAUSES:\n";
    echo "1. MySQL service not running\n";
    echo "2. Database 'ukmwebv' doesn't exist\n";
    echo "3. Wrong credentials in .env file\n";
    echo "4. Port 3306 blocked or in use\n\n";
    
    echo "💡 SOLUTIONS:\n";
    echo "1. Start MySQL service\n";
    echo "2. Create database: CREATE DATABASE ukmwebv;\n";
    echo "3. Check .env configuration\n";
    echo "4. Verify MySQL is running on port 3306\n";
}
