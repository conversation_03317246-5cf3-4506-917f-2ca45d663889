@echo off
echo ========================================
echo   FIXING REGISTER SUCCESS ROUTE
echo ========================================
echo.

echo [1/6] Clearing all caches...
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

echo.
echo [2/6] Checking route configuration...
php fix-register-success-route.php

echo.
echo [3/6] Listing all routes...
php artisan route:list | findstr register

echo.
echo [4/6] Testing registration flow...
php test-registration-flow.php

echo.
echo [5/6] Verifying view exists...
dir resources\views\auth\register-success.blade.php

echo.
echo [6/6] Starting server for testing...
echo.
echo ========================================
echo   REGISTER SUCCESS ROUTE FIXED!
echo ========================================
echo.
echo REGISTRATION FLOW:
echo 1. User fills registration form at /register
echo 2. Form submits to POST /register
echo 3. User created with status 'pending'
echo 4. Redirect to /register/success
echo 5. Success page shows:
echo    - Congratulations message
echo    - Status: Waiting for admin approval
echo    - Contact info: WhatsApp 081382640946
echo    - Admin email: <EMAIL>
echo.
echo TEST REGISTRATION:
echo 1. Go to: http://127.0.0.1:8000/register
echo 2. Fill form and submit
echo 3. Should redirect to: http://127.0.0.1:8000/register/success
echo 4. Should see success message with contact info
echo.
echo ADMIN APPROVAL PROCESS:
echo 1. Admin login: <EMAIL> / admin123
echo 2. Go to Admin Panel → Kelola Mahasiswa
echo 3. Find user with status "Menunggu Persetujuan"
echo 4. Edit user → Change status to "Aktif"
echo 5. User can now login
echo.
echo Starting Laravel server...
echo.

php artisan serve --host=127.0.0.1 --port=8000
