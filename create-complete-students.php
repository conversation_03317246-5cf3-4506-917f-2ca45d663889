<?php
echo "=== CREATING COMPLETE STUDENT DATA ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Password hash for 'pass123123'
    $passwordHash = '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm';
    
    // Delete existing student users to avoid duplicates
    echo "🗑️  Clearing existing student users...\n";
    $pdo->exec("DELETE FROM users WHERE email LIKE '%@student.telkomuniversity.ac.id'");
    echo "✅ Existing student users cleared\n\n";
    
    // Complete student data with faculty and major
    $students = [
        [
            'name' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'nim' => '12345678901',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Informatika',
            'batch' => '2023'
        ],
        [
            'name' => 'Ryemius Marghareta Siregar',
            'email' => '<EMAIL>',
            'nim' => '12345678902',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Sistem Informasi',
            'batch' => '2023'
        ],
        [
            'name' => 'Amanda Riski Agustian',
            'email' => '<EMAIL>',
            'nim' => '12345678903',
            'faculty' => 'Fakultas Ekonomi dan Bisnis',
            'major' => 'Akuntansi',
            'batch' => '2023'
        ],
        [
            'name' => 'Najla Ramadina Sulistyowati',
            'email' => '<EMAIL>',
            'nim' => '12345678904',
            'faculty' => 'Fakultas Komunikasi dan Bisnis',
            'major' => 'Ilmu Komunikasi',
            'batch' => '2023'
        ],
        [
            'name' => 'Nabilla Alyvia',
            'email' => '<EMAIL>',
            'nim' => '12345678905',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Informatika',
            'batch' => '2023'
        ],
        [
            'name' => 'Aras Agita Fasya',
            'email' => '<EMAIL>',
            'nim' => '12345678906',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Komputer',
            'batch' => '2023'
        ],
        [
            'name' => 'Aufa Hafiy Andhika',
            'email' => '<EMAIL>',
            'nim' => '12345678907',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Sistem Informasi',
            'batch' => '2023'
        ],
        [
            'name' => 'Rahadian Nungki Saputra',
            'email' => '<EMAIL>',
            'nim' => '12345678908',
            'faculty' => 'Fakultas Ekonomi dan Bisnis',
            'major' => 'Manajemen',
            'batch' => '2023'
        ],
        [
            'name' => 'Adit Kurniawan',
            'email' => '<EMAIL>',
            'nim' => '12345678909',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Informatika',
            'batch' => '2023'
        ],
        [
            'name' => 'Mikel Austin',
            'email' => '<EMAIL>',
            'nim' => '12345678910',
            'faculty' => 'Fakultas Komunikasi dan Bisnis',
            'major' => 'Digital Marketing',
            'batch' => '2023'
        ],
        [
            'name' => 'Antonius Valentino',
            'email' => '<EMAIL>',
            'nim' => '12345678911',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Teknik Informatika',
            'batch' => '2023'
        ],
        [
            'name' => 'Abraham Arif Mulia',
            'email' => '<EMAIL>',
            'nim' => '12345678912',
            'faculty' => 'Fakultas Ekonomi dan Bisnis',
            'major' => 'Akuntansi',
            'batch' => '2023'
        ],
        [
            'name' => 'Fathan Mubina',
            'email' => '<EMAIL>',
            'nim' => '12345678913',
            'faculty' => 'Fakultas Teknik Elektro',
            'major' => 'Sistem Informasi',
            'batch' => '2023'
        ],
        [
            'name' => 'Mutiara Hani Demayanti',
            'email' => '<EMAIL>',
            'nim' => '12345678914',
            'faculty' => 'Fakultas Komunikasi dan Bisnis',
            'major' => 'Ilmu Komunikasi',
            'batch' => '2023'
        ]
    ];
    
    $sql = "INSERT INTO users (name, email, nim, password, role, status, faculty, major, batch, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, 'student', 'active', ?, ?, ?, NOW(), NOW(), NOW())";
    $stmt = $pdo->prepare($sql);
    
    $created = 0;
    $failed = 0;
    
    echo "Creating complete student data...\n\n";
    
    foreach ($students as $student) {
        try {
            if ($stmt->execute([
                $student['name'],
                $student['email'],
                $student['nim'],
                $passwordHash,
                $student['faculty'],
                $student['major'],
                $student['batch']
            ])) {
                echo "   ✅ {$student['name']}\n";
                echo "      📧 {$student['email']}\n";
                echo "      🆔 {$student['nim']}\n";
                echo "      🏫 {$student['faculty']}\n";
                echo "      📚 {$student['major']}\n";
                echo "      📅 Angkatan {$student['batch']}\n";
                echo "      🔑 Password: pass123123\n\n";
                $created++;
            } else {
                echo "   ❌ Failed to create: {$student['name']}\n\n";
                $failed++;
            }
        } catch (Exception $e) {
            echo "   ❌ Error creating {$student['name']}: " . $e->getMessage() . "\n\n";
            $failed++;
        }
    }
    
    echo "=== RESULT ===\n";
    echo "✅ Successfully created: {$created} students\n";
    echo "❌ Failed: {$failed} students\n";
    
    // Show statistics
    echo "\n📊 Statistics:\n";
    $result = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'student'");
    $totalStudents = $result->fetch(PDO::FETCH_ASSOC)['total'];
    echo "   Total Students: {$totalStudents}\n";
    
    // Faculty distribution
    echo "\n🏫 Faculty Distribution:\n";
    $result = $pdo->query("SELECT faculty, COUNT(*) as count FROM users WHERE role = 'student' AND faculty IS NOT NULL GROUP BY faculty ORDER BY count DESC");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   • {$row['faculty']}: {$row['count']} students\n";
    }
    
    // Major distribution
    echo "\n📚 Major Distribution:\n";
    $result = $pdo->query("SELECT major, COUNT(*) as count FROM users WHERE role = 'student' AND major IS NOT NULL GROUP BY major ORDER BY count DESC");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   • {$row['major']}: {$row['count']} students\n";
    }
    
    echo "\n🌐 URLs to Test:\n";
    echo "   Admin Dashboard: http://localhost:8000/admin/dashboard\n";
    echo "   Student Management: http://localhost:8000/admin/students\n";
    echo "   Login: http://localhost:8000/login\n";
    
    echo "\n🔑 Login Credentials:\n";
    echo "   Any student email above\n";
    echo "   Password: pass123123\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== COMPLETE ===\n";
?>
