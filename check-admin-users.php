<?php
require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;

echo "=== ADMIN USERS IN DATABASE ===\n\n";

try {
    $admins = User::where('role', 'admin')->get();
    
    if ($admins->count() > 0) {
        echo "Found " . $admins->count() . " admin user(s):\n\n";
        
        foreach($admins as $admin) {
            echo "📧 Email: " . $admin->email . "\n";
            echo "👤 Name: " . $admin->name . "\n";
            echo "🔑 Role: " . $admin->role . "\n";
            echo "📱 Phone: " . ($admin->phone ?? 'Not set') . "\n";
            echo "📊 Status: " . $admin->status . "\n";
            echo "📅 Created: " . $admin->created_at . "\n";
            echo "---\n\n";
        }
    } else {
        echo "❌ No admin users found!\n";
        echo "Let's check all users:\n\n";
        
        $users = User::all();
        foreach($users as $user) {
            echo "📧 " . $user->email . " | 👤 " . $user->name . " | 🔑 " . $user->role . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== END ===\n";
?>
