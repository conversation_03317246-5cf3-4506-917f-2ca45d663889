<?php

echo "=== CHECKING ADMIN USER ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking if admin user exists...\n";
    
    // Check admin user
    $admin = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if ($admin) {
        echo "   ✅ Admin user found!\n";
        echo "   ID: {$admin->id}\n";
        echo "   NIM: {$admin->nim}\n";
        echo "   Name: {$admin->name}\n";
        echo "   Email: {$admin->email}\n";
        echo "   Role: {$admin->role}\n";
        echo "   Status: {$admin->status}\n";
        echo "   Email Verified: " . ($admin->email_verified_at ? 'Yes' : 'No') . "\n";
        echo "   Created: {$admin->created_at}\n";
        
        // Test password
        echo "\n2. Testing password...\n";
        if (\Illuminate\Support\Facades\Hash::check('admin123', $admin->password)) {
            echo "   ✅ Password 'admin123': CORRECT\n";
        } else {
            echo "   ❌ Password 'admin123': INCORRECT\n";
            echo "   Current hash: {$admin->password}\n";
            
            // Try to update password
            echo "   🔧 Updating password...\n";
            $admin->update([
                'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
                'status' => 'active',
                'email_verified_at' => now(),
            ]);
            echo "   ✅ Password updated to 'admin123'\n";
        }
        
    } else {
        echo "   ❌ Admin user NOT found!\n";
        echo "   Creating admin user...\n";
        
        $admin = \App\Models\User::create([
            'nim' => 'ADMIN001',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Administrasi',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        
        echo "   ✅ Admin user created!\n";
        echo "   Email: {$admin->email}\n";
        echo "   Password: admin123\n";
    }
    
    echo "\n3. Checking all users in database...\n";
    $users = \App\Models\User::all();
    echo "   Total users: {$users->count()}\n";
    
    foreach ($users as $user) {
        echo "   - {$user->name} ({$user->email}) - Role: {$user->role} - Status: {$user->status}\n";
    }
    
    echo "\n4. Testing login process...\n";
    
    // Simulate login attempt
    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'admin123'
    ];
    
    $user = \App\Models\User::where('email', $credentials['email'])->first();
    
    if ($user) {
        echo "   ✅ User found for email: {$credentials['email']}\n";
        
        if (\Illuminate\Support\Facades\Hash::check($credentials['password'], $user->password)) {
            echo "   ✅ Password verification: SUCCESS\n";
            
            if ($user->status === 'active') {
                echo "   ✅ User status: ACTIVE\n";
                echo "   ✅ Login should work!\n";
            } else {
                echo "   ❌ User status: {$user->status} (should be 'active')\n";
                $user->update(['status' => 'active']);
                echo "   ✅ Status updated to 'active'\n";
            }
            
        } else {
            echo "   ❌ Password verification: FAILED\n";
        }
    } else {
        echo "   ❌ User not found for email: {$credentials['email']}\n";
    }
    
    echo "\n5. Final verification...\n";
    $finalAdmin = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if ($finalAdmin && 
        \Illuminate\Support\Facades\Hash::check('admin123', $finalAdmin->password) && 
        $finalAdmin->status === 'active') {
        
        echo "   ✅ ADMIN LOGIN READY!\n";
        echo "   📧 Email: <EMAIL>\n";
        echo "   🔒 Password: admin123\n";
        echo "   🌐 URL: http://localhost:8000/login\n";
    } else {
        echo "   ❌ Something is still wrong...\n";
        if ($finalAdmin) {
            echo "   User exists: YES\n";
            echo "   Password correct: " . (\Illuminate\Support\Facades\Hash::check('admin123', $finalAdmin->password) ? 'YES' : 'NO') . "\n";
            echo "   Status active: " . ($finalAdmin->status === 'active' ? 'YES' : 'NO') . "\n";
        } else {
            echo "   User exists: NO\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
