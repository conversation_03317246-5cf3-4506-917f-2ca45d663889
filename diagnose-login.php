<?php

echo "=== LOGIN DIAGNOSIS ===\n";

// 1. Check database connection
echo "1. Testing database connection...\n";

$host = '127.0.0.1';
$dbname = 'ukmwebL';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ Database connection: SUCCESS\n";
    
    // 2. Check if users table exists
    echo "2. Checking users table...\n";
    try {
        $stmt = $pdo->query("DESCRIBE users");
        echo "   ✅ Users table: EXISTS\n";
        
        // Show table structure
        echo "   Table structure:\n";
        while ($row = $stmt->fetch()) {
            echo "     - {$row['Field']} ({$row['Type']})\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Users table: NOT EXISTS\n";
        echo "   Error: " . $e->getMessage() . "\n";
        
        // Create users table manually
        echo "   🔧 Creating users table...\n";
        
        $createTable = "
        CREATE TABLE users (
            id bigint unsigned NOT NULL AUTO_INCREMENT,
            nim varchar(255) NOT NULL,
            name varchar(255) NOT NULL,
            email varchar(255) NOT NULL,
            email_verified_at timestamp NULL DEFAULT NULL,
            password varchar(255) NOT NULL,
            phone varchar(255) DEFAULT NULL,
            gender enum('male','female') NOT NULL,
            faculty varchar(255) NOT NULL,
            major varchar(255) NOT NULL,
            batch varchar(255) NOT NULL,
            role enum('admin','student','ketua_ukm') NOT NULL DEFAULT 'student',
            status enum('active','inactive','suspended','pending') NOT NULL DEFAULT 'pending',
            remember_token varchar(100) DEFAULT NULL,
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY users_email_unique (email),
            UNIQUE KEY users_nim_unique (nim)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        try {
            $pdo->exec($createTable);
            echo "   ✅ Users table created successfully!\n";
        } catch (Exception $e) {
            echo "   ❌ Failed to create users table: " . $e->getMessage() . "\n";
            exit;
        }
    }
    
    // 3. Check current users
    echo "3. Checking existing users...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "   Total users: {$result['count']}\n";
    
    if ($result['count'] > 0) {
        $stmt = $pdo->query("SELECT id, nim, name, email, role, status FROM users LIMIT 10");
        while ($user = $stmt->fetch()) {
            echo "   - {$user['name']} ({$user['email']}) - {$user['role']} - {$user['status']}\n";
        }
    }
    
    // 4. Create admin user
    echo "4. Creating admin user...\n";
    
    // Check if admin exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "   ⚠️  Admin already exists, updating...\n";
        
        // Update admin
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            UPDATE users SET 
                password = ?, 
                status = 'active', 
                email_verified_at = NOW(),
                role = 'admin'
            WHERE email = ?
        ");
        $stmt->execute([$hashedPassword, '<EMAIL>']);
        echo "   ✅ Admin updated successfully!\n";
        
    } else {
        echo "   🔧 Creating new admin...\n";
        
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (
                nim, name, email, password, phone, gender, faculty, major, batch,
                role, status, email_verified_at, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
            )
        ");
        
        $result = $stmt->execute([
            'ADMIN001',
            'Administrator',
            '<EMAIL>',
            $hashedPassword,
            '081234567890',
            'male',
            'Administrasi',
            'Sistem Informasi',
            '2024',
            'admin',
            'active',
            date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            echo "   ✅ Admin created successfully!\n";
        } else {
            echo "   ❌ Failed to create admin\n";
        }
    }
    
    // 5. Create test student
    echo "5. Creating test student...\n";
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $student = $stmt->fetch();
    
    if (!$student) {
        $hashedPassword = password_hash('student123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (
                nim, name, email, password, phone, gender, faculty, major, batch,
                role, status, email_verified_at, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
            )
        ");
        
        $stmt->execute([
            '1301210001',
            'Test Student',
            '<EMAIL>',
            $hashedPassword,
            '081234567891',
            'male',
            'Informatika',
            'Sistem Informasi',
            '2021',
            'student',
            'active',
            date('Y-m-d H:i:s')
        ]);
        echo "   ✅ Test student created!\n";
    } else {
        echo "   ⚠️  Test student already exists\n";
    }
    
    // 6. Final verification
    echo "6. Final verification...\n";
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "   ✅ Admin found in database\n";
        echo "   ID: {$admin['id']}\n";
        echo "   Name: {$admin['name']}\n";
        echo "   Email: {$admin['email']}\n";
        echo "   Role: {$admin['role']}\n";
        echo "   Status: {$admin['status']}\n";
        
        // Test password
        if (password_verify('admin123', $admin['password'])) {
            echo "   ✅ Password verification: SUCCESS\n";
        } else {
            echo "   ❌ Password verification: FAILED\n";
        }
        
        echo "\n🎉 LOGIN READY!\n";
        echo "📧 Email: <EMAIL>\n";
        echo "🔒 Password: admin123\n";
        echo "🌐 URL: http://localhost:8000/login\n";
        
        echo "\n📝 Test Accounts:\n";
        echo "Student: <EMAIL> | student123\n";
        
    } else {
        echo "   ❌ Admin still not found!\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    echo "\n🔧 TROUBLESHOOTING:\n";
    echo "1. Make sure MySQL/XAMPP is running\n";
    echo "2. Check database name 'ukmwebL' exists\n";
    echo "3. Check .env file database settings\n";
    echo "4. Try creating database manually:\n";
    echo "   CREATE DATABASE ukmwebL;\n";
}
