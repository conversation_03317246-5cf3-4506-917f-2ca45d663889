# 🔄 RESET DATABASE MANUAL - LANGKAH MUDAH

## 🎯 **LANGKAH 1: Buka phpMyAdmin**

1. **Buka browser**
2. **Ketik:** `http://localhost/phpmyadmin`
3. **Login dengan:**
   - Username: `root`
   - Password: (kosong)

## 🎯 **LANGKAH 2: Drop Database Lama**

1. **Klik database `ukmwebv` di sidebar kiri** (jika ada)
2. **<PERSON>lik tab "Operations"**
3. **Scroll ke bawah, cari "Remove database"**
4. **<PERSON>lik "Drop the database (DROP)"**
5. **Kon<PERSON>rma<PERSON> dengan klik "OK"**

## 🎯 **LANGKAH 3: Buat Database Baru**

1. **Klik "New" di sidebar kiri**
2. **Ketik nama database:** `ukmwebv`
3. **Pilih Collation:** `utf8mb4_unicode_ci`
4. **<PERSON><PERSON> "Create"**

## 🎯 **LANGKAH 4: Jalankan SQL untuk Buat Table**

1. **Pastikan database `ukmwebv` terpilih**
2. **Klik tab "SQL"**
3. **Copy paste SQL berikut:**

```sql
-- Create users table
CREATE TABLE users (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    nim varchar(255) NOT NULL,
    name varchar(255) NOT NULL,
    email varchar(255) NOT NULL,
    email_verified_at timestamp NULL DEFAULT NULL,
    password varchar(255) NOT NULL,
    phone varchar(255) DEFAULT NULL,
    gender enum('male','female') NOT NULL,
    faculty varchar(255) NOT NULL,
    major varchar(255) NOT NULL,
    batch varchar(255) NOT NULL,
    role enum('admin','student','ketua_ukm') NOT NULL DEFAULT 'student',
    status enum('active','inactive','suspended','pending') NOT NULL DEFAULT 'pending',
    remember_token varchar(100) DEFAULT NULL,
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY users_email_unique (email),
    UNIQUE KEY users_nim_unique (nim)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create sessions table
CREATE TABLE sessions (
    id varchar(255) NOT NULL,
    user_id bigint unsigned DEFAULT NULL,
    ip_address varchar(45) DEFAULT NULL,
    user_agent text,
    payload longtext NOT NULL,
    last_activity int NOT NULL,
    PRIMARY KEY (id),
    KEY sessions_user_id_index (user_id),
    KEY sessions_last_activity_index (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create cache table
CREATE TABLE cache (
    `key` varchar(255) NOT NULL,
    value mediumtext NOT NULL,
    expiration int NOT NULL,
    PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create cache_locks table
CREATE TABLE cache_locks (
    `key` varchar(255) NOT NULL,
    owner varchar(255) NOT NULL,
    expiration int NOT NULL,
    PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create jobs table
CREATE TABLE jobs (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    queue varchar(255) NOT NULL,
    payload longtext NOT NULL,
    attempts tinyint unsigned NOT NULL,
    reserved_at int unsigned DEFAULT NULL,
    available_at int unsigned NOT NULL,
    created_at int unsigned NOT NULL,
    PRIMARY KEY (id),
    KEY jobs_queue_index (queue)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create job_batches table
CREATE TABLE job_batches (
    id varchar(255) NOT NULL,
    name varchar(255) NOT NULL,
    total_jobs int NOT NULL,
    pending_jobs int NOT NULL,
    failed_jobs int NOT NULL,
    failed_job_ids longtext NOT NULL,
    options mediumtext,
    cancelled_at int DEFAULT NULL,
    created_at int NOT NULL,
    finished_at int DEFAULT NULL,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create failed_jobs table
CREATE TABLE failed_jobs (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    uuid varchar(255) NOT NULL,
    connection text NOT NULL,
    queue text NOT NULL,
    payload longtext NOT NULL,
    exception longtext NOT NULL,
    failed_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY failed_jobs_uuid_unique (uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

4. **Klik "Go" untuk menjalankan**

## 🎯 **LANGKAH 5: Insert Data User**

**Jalankan SQL berikut untuk membuat user:**

```sql
-- Insert admin user
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    'ADMIN001',
    'Administrator',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '081234567890',
    'male',
    'Administrasi',
    'Sistem Informasi',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- Insert test student
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    '1301210001',
    'Test Student',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '081234567891',
    'male',
    'Informatika',
    'Sistem Informasi',
    '2021',
    'student',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- Insert test ketua UKM
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    '1301210002',
    'Test Ketua UKM',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '081234567892',
    'female',
    'Informatika',
    'Teknik Informatika',
    '2021',
    'ketua_ukm',
    'active',
    NOW(),
    NOW(),
    NOW()
);
```

## 🎯 **LANGKAH 6: Verifikasi Data**

**Jalankan SQL untuk cek data:**

```sql
SELECT id, nim, name, email, role, status, created_at 
FROM users 
ORDER BY id;
```

**Harusnya muncul 3 user:**
1. Administrator (admin)
2. Test Student (student)  
3. Test Ketua UKM (ketua_ukm)

## 🎯 **LANGKAH 7: Clear Laravel Cache**

**Buka terminal di folder project dan jalankan:**

```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

## 🎯 **LANGKAH 8: Test Login**

1. **Buka:** `http://localhost:8000/login`
2. **Test login dengan:**

### **Admin:**
- **Email:** `<EMAIL>`
- **Password:** `admin123`

### **Student:**
- **Email:** `<EMAIL>`
- **Password:** `student123`

### **Ketua UKM:**
- **Email:** `<EMAIL>`
- **Password:** `ketua123`

## ✅ **CHECKLIST:**

- [ ] Database `ukmwebv` dibuat
- [ ] Table users dibuat
- [ ] Table sessions dibuat
- [ ] Table cache dibuat
- [ ] Table jobs dibuat
- [ ] Admin user diinsert
- [ ] Test student diinsert
- [ ] Test ketua UKM diinsert
- [ ] Laravel cache di-clear
- [ ] Login test berhasil

---

## 🚨 **JIKA MASIH ERROR:**

1. **Cek .env file** pastikan `DB_DATABASE=ukmwebv`
2. **Restart XAMPP** (stop dan start MySQL)
3. **Clear browser cache** (Ctrl+F5)
4. **Cek Laravel log** di `storage/logs/laravel.log`

**Ikuti langkah di atas step-by-step dan beri tahu hasilnya!** 🚀
