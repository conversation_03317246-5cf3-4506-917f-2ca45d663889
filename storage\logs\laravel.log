[2025-06-21 08:59:03] local.ERROR: method_exists(): Argument #1 ($object_or_class) must be of type object|string, null given (View: C:\Users\<USER>\Desktop\ukmwebLbasedfunc\resources\views\ukms\show.blade.php) {"userId":10,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): method_exists(): Argument #1 ($object_or_class) must be of type object|string, null given (View: C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\resources\\views\\ukms\\show.blade.php) at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\storage\\framework\\views\\9f1b850b351794372236aace020fef72.php:218)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(TypeError), 1)
#1 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\storage\\framework\\views\\9f1b850b351794372236aace020fef72.php:218)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\storage\\framework\\views\\9f1b850b351794372236aace020fef72.php(218): method_exists(NULL, 'count')
#1 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#3 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#7 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#8 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#9 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#11 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\test-ukm-page-directly.php:97)
[stacktrace]
#0 {main}
"} 
[2025-06-21 09:05:00] local.ERROR: Call to a member function count() on null {"exception":"[object] (Error(code: 0): Call to a member function count() on null at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\test-ukm-page-directly.php:97)
[stacktrace]
#0 {main}
"} 
[2025-06-21 09:17:23] local.ERROR: Call to a member function count() on null {"exception":"[object] (Error(code: 0): Call to a member function count() on null at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\debug-achievements-display.php:43)
[stacktrace]
#0 {main}
"} 
[2025-06-21 09:18:46] local.ERROR: Call to a member function count() on null {"exception":"[object] (Error(code: 0): Call to a member function count() on null at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\debug-achievements-display.php:144)
[stacktrace]
#0 {main}
"} 
[2025-06-21 09:19:18] local.ERROR: Call to a member function count() on null {"exception":"[object] (Error(code: 0): Call to a member function count() on null at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\debug-achievements-display.php:144)
[stacktrace]
#0 {main}
"} 
[2025-06-21 09:21:08] local.ERROR: Call to a member function count() on null {"exception":"[object] (Error(code: 0): Call to a member function count() on null at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\debug-achievements-display.php:153)
[stacktrace]
#0 {main}
"} 
[2025-06-21 10:09:27] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-21 10:09:27] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-21 10:09:27] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":2,"rejected_count":0,"pending_members":[]} 
[2025-06-21 10:10:01] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-21 10:10:01] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-21 10:10:01] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-21 10:22:29] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-21 10:22:29] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-21 10:22:29] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-21 10:22:51] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-21 10:22:51] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-21 10:22:51] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-21 10:23:18] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-21 10:23:18] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-21 10:23:18] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-21 10:23:41] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-21 10:23:41] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-21 10:23:41] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-21 11:15:40] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-21 11:15:40] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-21 11:15:40] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":0,"rejected_count":0,"pending_members":[]} 
[2025-06-21 11:20:37] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-21 11:20:37] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-21 11:20:37] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":0,"rejected_count":0,"pending_members":[]} 
[2025-06-22 03:30:30] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-22 03:30:30] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-22 03:30:30] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-22 03:30:42] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-22 03:30:42] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-22 03:30:42] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":0,"rejected_count":0,"pending_members":[]} 
[2025-06-22 04:06:46] local.INFO: Ketua UKM accessing members page {"user_id":7,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-22 04:06:46] local.INFO: UKM lookup result {"user_id":7,"ukm_found":"IMMA","ukm_id":5} 
[2025-06-22 04:06:46] local.INFO: Member counts for UKM {"ukm_id":5,"ukm_name":"IMMA","pending_count":0,"active_count":2,"rejected_count":0,"pending_members":[]} 
[2025-06-22 04:21:32] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:31:36] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:31:36] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:31:36] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:31:37] local.INFO: Template image loaded successfully for certificate {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg"} 
[2025-06-22 04:33:26] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:33:26] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:37:02] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:49:38] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/oEnQNVi3NXvnWlZmsNQQq2ncQ2gLcr0BJOYkggge.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:57:31] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:57:31] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:59:01] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 04:59:01] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 05:11:27] local.INFO: Event created successfully {"event_id":6,"title":"Pengenalan anggota","ukm_id":5,"user_id":7} 
[2025-06-22 05:18:40] local.INFO: Event created successfully {"event_id":8,"title":"wdada","ukm_id":5,"user_id":7} 
[2025-06-22 05:26:31] local.ERROR: preg_match(): Compilation failed: missing closing parenthesis at offset 20 {"exception":"[object] (ErrorException(code: 0): preg_match(): Compilation failed: missing closing parenthesis at offset 20 at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\test-approval-buttons.php:137)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'preg_match(): C...', 'C:\\\\Users\\\\<USER>\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'preg_match(): C...', 'C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\test-approval-buttons.php(137): preg_match('/approveRegistr...', '@extends('layou...')
#3 {main}
"} 
[2025-06-22 05:28:57] local.ERROR: Call to undefined method App\Services\NotificationService::sendEventRegistrationApproved() {"userId":7,"exception":"[object] (Error(code: 0): Call to undefined method App\\Services\\NotificationService::sendEventRegistrationApproved() at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Controllers\\KetuaUkmController.php:812)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\KetuaUkmController->approveEventRegistration(Object(Illuminate\\Http\\Request), Object(App\\Models\\Event), '10')
#1 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('approveEventReg...', Array)
#2 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\KetuaUkmController), 'approveEventReg...')
#3 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#4 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Controllers\\KetuaUkmController.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(190): App\\Http\\Controllers\\KetuaUkmController->App\\Http\\Controllers\\{closure}(Object(Illuminate\\Http\\Request), Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Middleware\\CheckUserStatus.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Users\\balqis annisa\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/7CTiW7RMtdeKk2BAIQDJxBMoLAhkVnjnxSEITUO4.jpg","size":108682,"mime":"image/jpeg","base64_length":144935} 
[2025-06-22 06:27:44] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '5-2' for key 'event_registrations_user_id_event_id_unique' (Connection: mysql, SQL: insert into `event_registrations` (`event_id`, `user_id`, `status`, `motivation`, `updated_at`, `created_at`) values (2, 5, pending, Test registration for notification testing, 2025-06-22 06:27:44, 2025-06-22 06:27:44)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '5-2' for key 'event_registrations_user_id_event_id_unique' (Connection: mysql, SQL: insert into `event_registrations` (`event_id`, `user_id`, `status`, `motivation`, `updated_at`, `created_at`) values (2, 5, pending, Test registration for notification testing, 2025-06-22 06:27:44, 2025-06-22 06:27:44)) at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:817)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `ev...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `ev...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `ev...', Array, 'id')
#3 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `ev...', Array, 'id')
#4 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\EventRegistration))
#10 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\EventRegistration), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\test-real-approval-notification.php(41): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '5-2' for key 'event_registrations_user_id_event_id_unique' at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `ev...', Array)
#2 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `ev...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `ev...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `ev...', Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `ev...', Array, 'id')
#6 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\EventRegistration))
#12 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\EventRegistration), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\test-real-approval-notification.php(41): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 {main}
"} 
[2025-06-22 06:34:55] local.INFO: Event created successfully {"event_id":9,"title":"Ibadah mingguan","ukm_id":6,"user_id":11} 
[2025-06-22 06:37:45] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 2 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 2 at C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php \\n = \\\\App\\\\...', false)
#2 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('\\n = \\\\App\\\\Models...', true)
#4 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('\\n = \\\\App\\\\Models...', true)
#5 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('\\n = \\\\App\\\\Models...')
#6 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-06-22 06:39:02] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 06:39:18] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 06:39:22] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 06:39:30] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 06:39:58] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 06:56:30] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 07:05:29] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 07:08:04] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/5sMvguMGMPZXtjoPlAwZDXgNOgJgZpf17JCYScK5.jpg","size":412808,"mime":"image/jpeg","base64_length":550435} 
[2025-06-22 07:09:08] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 07:16:34] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-22 07:27:51] local.INFO: Event created successfully {"event_id":10,"title":"Camp Rohani","ukm_id":6,"user_id":11} 
[2025-06-23 06:10:45] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-23 07:32:34] local.INFO: Event created successfully {"event_id":11,"title":"tester sertif","ukm_id":5,"user_id":7} 
[2025-06-23 07:37:04] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/S1aMNCraspNEQPmdSdGsSqIUm5LgUpS83PnJTz6l.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-24 08:39:48] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/PxMxsb8RXgyopg0UZokSS8f8KvXxWVAqjHp1Oj1V.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-24 09:54:23] local.INFO: Ketua UKM accessing members page {"user_id":10,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-24 09:54:23] local.INFO: UKM lookup result {"user_id":10,"ukm_found":"Sistem Informasi","ukm_id":7} 
[2025-06-24 09:54:23] local.INFO: Member counts for UKM {"ukm_id":7,"ukm_name":"Sistem Informasi","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-24 09:57:23] local.INFO: Event created successfully {"event_id":12,"title":"Pengenalan SI","ukm_id":7,"user_id":10} 
[2025-06-24 10:06:04] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\ukmwebLbasedfunc\\public\\storage/events/certificates/aVgl5yKzaUJiSNOy9aXagIwf7VLMuxaD3mfOggPL.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-06-28 09:16:35] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1701 Cannot truncate a table referenced in a foreign key constraint (`ukmwebv`.`events`, CONSTRAINT `events_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukmwebv`.`ukms` (`id`)) (Connection: mysql, SQL: truncate table `ukms`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1701 Cannot truncate a table referenced in a foreign key constraint (`ukmwebv`.`events`, CONSTRAINT `events_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukmwebv`.`ukms` (`id`)) (Connection: mysql, SQL: truncate table `ukms`) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('truncate table ...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('truncate table ...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4060): Illuminate\\Database\\Connection->statement('truncate table ...', Array)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Query\\Builder->truncate()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2208): Illuminate\\Database\\Eloquent\\Builder->forwardCallTo(Object(Illuminate\\Database\\Query\\Builder), 'truncate', Array)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('truncate', Array)
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'truncate', Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('truncate', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\create-ukm-data.php(15): Illuminate\\Database\\Eloquent\\Model::__callStatic('truncate', Array)
#9 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1701 Cannot truncate a table referenced in a foreign key constraint (`ukmwebv`.`events`, CONSTRAINT `events_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukmwebv`.`ukms` (`id`)) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}('truncate table ...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('truncate table ...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('truncate table ...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4060): Illuminate\\Database\\Connection->statement('truncate table ...', Array)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Query\\Builder->truncate()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2208): Illuminate\\Database\\Eloquent\\Builder->forwardCallTo(Object(Illuminate\\Database\\Query\\Builder), 'truncate', Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('truncate', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'truncate', Array)
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('truncate', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\create-ukm-data.php(15): Illuminate\\Database\\Eloquent\\Model::__callStatic('truncate', Array)
#11 {main}
"} 
[2025-06-28 09:21:16] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1701 Cannot truncate a table referenced in a foreign key constraint (`ukmwebv`.`events`, CONSTRAINT `events_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukmwebv`.`ukms` (`id`)) (Connection: mysql, SQL: truncate table `ukms`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1701 Cannot truncate a table referenced in a foreign key constraint (`ukmwebv`.`events`, CONSTRAINT `events_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukmwebv`.`ukms` (`id`)) (Connection: mysql, SQL: truncate table `ukms`) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('truncate table ...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('truncate table ...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4060): Illuminate\\Database\\Connection->statement('truncate table ...', Array)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Query\\Builder->truncate()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2208): Illuminate\\Database\\Eloquent\\Builder->forwardCallTo(Object(Illuminate\\Database\\Query\\Builder), 'truncate', Array)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('truncate', Array)
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'truncate', Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('truncate', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\insert-ukm-data.php(13): Illuminate\\Database\\Eloquent\\Model::__callStatic('truncate', Array)
#9 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1701 Cannot truncate a table referenced in a foreign key constraint (`ukmwebv`.`events`, CONSTRAINT `events_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukmwebv`.`ukms` (`id`)) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}('truncate table ...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('truncate table ...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('truncate table ...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4060): Illuminate\\Database\\Connection->statement('truncate table ...', Array)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Query\\Builder->truncate()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2208): Illuminate\\Database\\Eloquent\\Builder->forwardCallTo(Object(Illuminate\\Database\\Query\\Builder), 'truncate', Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('truncate', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'truncate', Array)
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('truncate', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\insert-ukm-data.php(13): Illuminate\\Database\\Eloquent\\Model::__callStatic('truncate', Array)
#11 {main}
"} 
[2025-06-28 09:29:32] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `ukm_achievements` where year(`created_at`) = 2025) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `ukm_achievements` where year(`created_at`) = 2025) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3104}()
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3619): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3547): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\HomeController.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#64 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select count(*)...')
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select count(*)...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3104}()
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3619): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3547): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\HomeController.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}
"} 
[2025-06-28 09:29:52] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `ukm_achievements` where year(`created_at`) = 2025) {"userId":24,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `ukm_achievements` where year(`created_at`) = 2025) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3104}()
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3619): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3547): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\HomeController.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#64 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select count(*)...')
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select count(*)...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3104}()
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3619): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3547): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\HomeController.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}
"} 
[2025-06-28 09:29:56] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `ukm_achievements` where year(`created_at`) = 2025) {"userId":24,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `ukm_achievements` where year(`created_at`) = 2025) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3104}()
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3619): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3547): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\HomeController.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#64 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select count(*)...')
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select count(*)...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3104}()
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3619): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3547): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\HomeController.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}
"} 
[2025-06-28 09:31:15] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'permissions' already exists (Connection: mysql, SQL: create table `permissions` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `guard_name` varchar(255) not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'permissions' already exists (Connection: mysql, SQL: create table `permissions` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `guard_name` varchar(255) not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `p...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `p...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `p...')
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('permissions', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\database\\migrations\\2025_05_26_134429_create_permission_tables.php(23): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_26_1344...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_26_1344...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'permissions' already exists at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}('create table `p...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `p...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `p...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `p...')
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('permissions', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\database\\migrations\\2025_05_26_134429_create_permission_tables.php(23): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_26_1344...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_26_1344...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-28 09:35:44] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `ukm_achievements` where year(`created_at`) = 2025) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `ukm_achievements` where year(`created_at`) = 2025) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3104}()
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3619): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3547): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\HomeController.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#64 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'ukmwebv.ukm_achievements' doesn't exist at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select count(*)...')
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select count(*)...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3104}()
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3619): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3547): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\HomeController.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}
"} 
[2025-06-28 09:41:54] local.ERROR: json_decode(): Argument #1 ($json) must be of type string, array given (View: C:\Users\<USER>\Desktop\TAWEBB\ukmwebLbasedfunc\ukmwebLbasedfunc\resources\views\ukms\show.blade.php) {"userId":24,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): json_decode(): Argument #1 ($json) must be of type string, array given (View: C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\resources\\views\\ukms\\show.blade.php) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php:427)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(TypeError), 1)
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#59 {main}

[previous exception] [object] (TypeError(code: 0): json_decode(): Argument #1 ($json) must be of type string, array given at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php:427)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php(427): json_decode(Array, true)
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#62 {main}
"} 
[2025-06-28 12:33:32] local.ERROR: json_decode(): Argument #1 ($json) must be of type string, array given (View: C:\Users\<USER>\Desktop\TAWEBB\ukmwebLbasedfunc\ukmwebLbasedfunc\resources\views\ukms\show.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): json_decode(): Argument #1 ($json) must be of type string, array given (View: C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\resources\\views\\ukms\\show.blade.php) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php:427)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(TypeError), 1)
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#59 {main}

[previous exception] [object] (TypeError(code: 0): json_decode(): Argument #1 ($json) must be of type string, array given at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php:427)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php(427): json_decode(Array, true)
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#62 {main}
"} 
[2025-06-28 12:47:11] local.ERROR: json_decode(): Argument #1 ($json) must be of type string, array given (View: C:\Users\<USER>\Desktop\TAWEBB\ukmwebLbasedfunc\ukmwebLbasedfunc\resources\views\ukms\show.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): json_decode(): Argument #1 ($json) must be of type string, array given (View: C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\resources\\views\\ukms\\show.blade.php) at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php:427)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(TypeError), 1)
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#59 {main}

[previous exception] [object] (TypeError(code: 0): json_decode(): Argument #1 ($json) must be of type string, array given at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php:427)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\storage\\framework\\views\\a0d9428bb9915ebc74d6d1d649bd7d42.php(427): json_decode(Array, true)
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#62 {main}
"} 
[2025-06-28 16:22:47] local.INFO: Event created successfully {"event_id":1,"title":"Pengenalan Anggota","ukm_id":7,"user_id":20} 
[2025-06-28 16:28:49] local.INFO: Event created successfully {"event_id":2,"title":"Bakti Sosial","ukm_id":7,"user_id":20} 
[2025-06-30 04:41:29] local.INFO: Ketua UKM accessing members page {"user_id":3,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-30 04:41:29] local.INFO: UKM lookup result {"user_id":3,"ukm_found":"UKM Futsal","ukm_id":4} 
[2025-06-30 04:41:29] local.INFO: Member counts for UKM {"ukm_id":4,"ukm_name":"UKM Futsal","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-30 04:41:32] local.INFO: Ketua UKM accessing members page {"user_id":3,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-30 04:41:32] local.INFO: UKM lookup result {"user_id":3,"ukm_found":"UKM Futsal","ukm_id":4} 
[2025-06-30 04:41:32] local.INFO: Member counts for UKM {"ukm_id":4,"ukm_name":"UKM Futsal","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-30 04:41:44] local.INFO: Ketua UKM accessing members page {"user_id":3,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-06-30 04:41:44] local.INFO: UKM lookup result {"user_id":3,"ukm_found":"UKM Futsal","ukm_id":4} 
[2025-06-30 04:41:44] local.INFO: Member counts for UKM {"ukm_id":4,"ukm_name":"UKM Futsal","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-06-30 04:53:21] local.INFO: Destroy method called for ketua UKM {"user_id":20,"user_name":"Ryemius Marghareta Siregar","user_role":"ketua_ukm","led_ukms_count":1} 
[2025-06-30 04:53:21] local.WARNING: Attempted to destroy ketua UKM who is still leading UKMs {"user_id":20,"led_ukms_count":1} 
[2025-06-30 04:56:21] local.INFO: Destroy method called for ketua UKM {"user_id":22,"user_name":"Najla Ramadina Sulistyowati","user_role":"ketua_ukm","led_ukms_count":0} 
[2025-06-30 04:56:21] local.INFO: Successfully converted ketua UKM to student {"user_id":22,"user_name":"Najla Ramadina Sulistyowati"} 
[2025-06-30 05:18:04] local.INFO: Event created successfully {"event_id":3,"title":"Pengenalan Sistem Informasi","ukm_id":9,"user_id":19} 
[2025-06-30 05:25:29] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\storage/events/certificates/ame7a23ngaT10kYlgPYj9Zcn4Ruwj7JD3UaA5Cej.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-07-16 05:54:18] local.INFO: Ketua UKM accessing members page {"user_id":3,"user_email":"<EMAIL>","user_role":"ketua_ukm"} 
[2025-07-16 05:54:18] local.INFO: UKM lookup result {"user_id":3,"ukm_found":"UKM Futsal","ukm_id":4} 
[2025-07-16 05:54:18] local.INFO: Member counts for UKM {"ukm_id":4,"ukm_name":"UKM Futsal","pending_count":0,"active_count":1,"rejected_count":0,"pending_members":[]} 
[2025-07-16 06:08:22] local.INFO: Event created successfully {"event_id":4,"title":"Tanding persahabatan","ukm_id":4,"user_id":3} 
[2025-07-16 06:13:28] local.INFO: Event created successfully {"event_id":5,"title":"pertandingan persahabatan","ukm_id":4,"user_id":3} 
[2025-07-16 06:21:21] local.INFO: Template image loaded successfully {"path":"C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\storage/events/certificates/vd5Z1OffObSE2TT51vOrUansVoQ7QuIkqOFgRCm5.png","size":133100,"mime":"image/png","base64_length":177490} 
[2025-07-17 19:35:08] local.ERROR: This password does not use the Bcrypt algorithm. {"exception":"[object] (RuntimeException(code: 0): This password does not use the Bcrypt algorithm. at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\BcryptHasher.php:88)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashManager.php(76): Illuminate\\Hashing\\BcryptHasher->check(Object(SensitiveParameterValue), '7267bf86984cfb0...', Array)
#1 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(158): Illuminate\\Hashing\\HashManager->check(Object(SensitiveParameterValue), '7267bf86984cfb0...')
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(481): Illuminate\\Auth\\EloquentUserProvider->validateCredentials(Object(App\\Models\\User), Object(SensitiveParameterValue))
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(419): Illuminate\\Auth\\SessionGuard->hasValidCredentials(Object(App\\Models\\User), Array)
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Timebox.php(34): Illuminate\\Auth\\SessionGuard->{closure:Illuminate\\Auth\\SessionGuard::attempt():411}(Object(Illuminate\\Support\\Timebox))
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(411): Illuminate\\Support\\Timebox->call(Object(Closure), 200000)
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(339): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Requests\\Auth\\LoginRequest.php(61): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php(27): App\\Http\\Requests\\Auth\\LoginRequest->authenticate()
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\AuthenticatedSessionController->store(Object(App\\Http\\Requests\\Auth\\LoginRequest))
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\AuthenticatedSessionController), 'store')
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\HandleCsrfException.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleCsrfException->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Middleware\\RefreshUserRole.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RefreshUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#64 {main}
"} 
[2025-07-17 19:40:57] local.ERROR: syntax error, unexpected token "public" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"public\" at C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\app\\Http\\Controllers\\KetuaUkmController.php:246)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}('C:\\\\Users\\\\<USER>\\\\...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\TAWEBB\\ukmwebLbasedfunc\\ukmwebLbasedfunc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#35 {main}
"} 
[2025-07-17 19:43:43] local.INFO: Destroy method called for ketua UKM {"user_id":28,"user_name":"Mikel Austin","user_role":"ketua_ukm","led_ukms_count":0} 
[2025-07-17 19:43:43] local.INFO: Successfully converted ketua UKM to student {"user_id":28,"user_name":"Mikel Austin"} 
