<?php

echo "=== TESTING PDF GENERATION WITHOUT GD EXTENSION ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking PHP extensions...\n";
    
    // Check GD extension
    $hasGD = extension_loaded('gd');
    echo "   GD Extension: " . ($hasGD ? 'INSTALLED' : 'NOT INSTALLED') . "\n";
    
    if ($hasGD) {
        $gdInfo = gd_info();
        echo "   GD Version: " . $gdInfo['GD Version'] . "\n";
        echo "   JPEG Support: " . ($gdInfo['JPEG Support'] ? 'Yes' : 'No') . "\n";
        echo "   PNG Support: " . ($gdInfo['PNG Support'] ? 'Yes' : 'No') . "\n";
    }
    
    // Check other relevant extensions
    $extensions = ['mbstring', 'dom', 'xml', 'fileinfo'];
    foreach ($extensions as $ext) {
        $loaded = extension_loaded($ext);
        echo "   {$ext}: " . ($loaded ? 'INSTALLED' : 'NOT INSTALLED') . "\n";
    }
    
    echo "2. Testing DomPDF capabilities...\n";
    
    // Test basic DomPDF functionality
    try {
        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML('<html><body><h1>Test</h1></body></html>');
        echo "   ✅ DomPDF basic functionality works\n";
    } catch (\Exception $e) {
        echo "   ❌ DomPDF basic test failed: " . $e->getMessage() . "\n";
    }
    
    echo "3. Testing image handling without GD...\n";
    
    // Find an event with certificate template
    $event = \App\Models\Event::whereNotNull('certificate_template')->first();
    
    if (!$event) {
        echo "   ❌ No event with certificate template found\n";
        exit;
    }
    
    echo "   ✅ Using event: {$event->title}\n";
    echo "   Template: {$event->certificate_template}\n";
    
    $templatePath = storage_path('app/public/' . $event->certificate_template);
    if (!file_exists($templatePath)) {
        echo "   ❌ Template file not found: {$templatePath}\n";
        exit;
    }
    
    echo "   ✅ Template file exists\n";
    
    $fileSize = filesize($templatePath);
    $mimeType = mime_content_type($templatePath);
    echo "   File size: " . round($fileSize / 1024, 2) . " KB\n";
    echo "   MIME type: {$mimeType}\n";
    
    echo "4. Testing base64 image embedding...\n";
    
    try {
        // Test base64 encoding
        $imageData = file_get_contents($templatePath);
        $base64 = base64_encode($imageData);
        $dataUri = 'data:' . $mimeType . ';base64,' . $base64;
        
        echo "   ✅ Base64 encoding successful\n";
        echo "   Base64 length: " . strlen($base64) . " characters\n";
        echo "   Data URI length: " . strlen($dataUri) . " characters\n";
        
        // Test HTML with base64 image
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                @page { margin: 0; size: A4 landscape; }
                body {
                    margin: 0;
                    padding: 0;
                    width: 297mm;
                    height: 210mm;
                    background-image: url("' . $dataUri . '");
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    font-family: Arial, sans-serif;
                }
                .name {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 48px;
                    font-weight: bold;
                    color: #2c3e50;
                    text-transform: uppercase;
                    background: rgba(255,255,255,0.2);
                    padding: 20px;
                    border-radius: 10px;
                }
            </style>
        </head>
        <body>
            <div class="name">TEST CERTIFICATE</div>
        </body>
        </html>';
        
        echo "   ✅ HTML with base64 image generated\n";
        echo "   HTML length: " . strlen($html) . " characters\n";
        
    } catch (\Exception $e) {
        echo "   ❌ Base64 encoding failed: " . $e->getMessage() . "\n";
    }
    
    echo "5. Testing PDF generation with template...\n";
    
    try {
        // Test PDF generation with base64 image
        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML($html)
                  ->setPaper('A4', 'landscape')
                  ->setOptions([
                      'isHtml5ParserEnabled' => true,
                      'isPhpEnabled' => false,
                      'defaultFont' => 'Arial',
                      'dpi' => 150, // Lower DPI to reduce memory usage
                      'debugKeepTemp' => false,
                      'debugCss' => false,
                      'debugLayout' => false,
                      'debugLayoutLines' => false,
                      'debugLayoutBlocks' => false,
                      'debugLayoutInline' => false,
                      'debugLayoutPaddingBox' => false,
                  ]);
        
        echo "   ✅ PDF object created successfully\n";
        
        // Try to generate PDF output
        $pdfOutput = $pdf->output();
        echo "   ✅ PDF output generated\n";
        echo "   PDF size: " . round(strlen($pdfOutput) / 1024, 2) . " KB\n";
        
        // Save test PDF
        $testFilename = 'test-certificate-' . time() . '.pdf';
        $testPath = storage_path('app/public/certificates/' . $testFilename);
        
        // Create directory if not exists
        $dir = dirname($testPath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        file_put_contents($testPath, $pdfOutput);
        echo "   ✅ Test PDF saved: {$testPath}\n";
        
        if (file_exists($testPath)) {
            $savedSize = filesize($testPath);
            echo "   Saved PDF size: " . round($savedSize / 1024, 2) . " KB\n";
            
            if ($savedSize > 10000) { // More than 10KB indicates substantial content
                echo "   ✅ PDF appears to contain substantial content (likely includes template)\n";
            } else {
                echo "   ⚠️  PDF is very small (may not include template image)\n";
            }
        }
        
    } catch (\Exception $e) {
        echo "   ❌ PDF generation failed: " . $e->getMessage() . "\n";
        echo "   Error details: " . $e->getTraceAsString() . "\n";
    }
    
    echo "6. Testing alternative PDF libraries...\n";
    
    // Check if other PDF libraries are available
    $pdfLibraries = [
        'TCPDF' => class_exists('TCPDF'),
        'mPDF' => class_exists('Mpdf\\Mpdf'),
        'wkhtmltopdf' => shell_exec('which wkhtmltopdf 2>/dev/null'),
    ];
    
    foreach ($pdfLibraries as $lib => $available) {
        echo "   {$lib}: " . ($available ? 'AVAILABLE' : 'NOT AVAILABLE') . "\n";
    }
    
    echo "7. Testing simple certificate without template...\n";
    
    try {
        // Test simple certificate without background image
        $simpleHtml = '
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                @page { margin: 0; size: A4 landscape; }
                body {
                    margin: 0;
                    padding: 40px;
                    width: 297mm;
                    height: 210mm;
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }
                .certificate-border {
                    border: 8px solid #fff;
                    padding: 60px 40px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 20px;
                }
                .title {
                    font-size: 48px;
                    font-weight: bold;
                    margin-bottom: 30px;
                    text-transform: uppercase;
                    letter-spacing: 4px;
                }
                .name {
                    font-size: 42px;
                    font-weight: bold;
                    margin: 30px 0;
                    text-transform: uppercase;
                    letter-spacing: 3px;
                    border-bottom: 3px solid #fff;
                    padding-bottom: 10px;
                    display: inline-block;
                }
            </style>
        </head>
        <body>
            <div class="certificate-border">
                <div class="title">SERTIFIKAT</div>
                <div class="name">TEST PARTICIPANT</div>
                <div>Telah mengikuti kegiatan TEST EVENT</div>
            </div>
        </body>
        </html>';
        
        $simplePdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML($simpleHtml)
                        ->setPaper('A4', 'landscape');
        
        $simplePdfOutput = $simplePdf->output();
        echo "   ✅ Simple certificate PDF generated\n";
        echo "   Simple PDF size: " . round(strlen($simplePdfOutput) / 1024, 2) . " KB\n";
        
        // Save simple test PDF
        $simpleFilename = 'test-simple-certificate-' . time() . '.pdf';
        $simplePath = storage_path('app/public/certificates/' . $simpleFilename);
        file_put_contents($simplePath, $simplePdfOutput);
        echo "   ✅ Simple PDF saved: {$simplePath}\n";
        
    } catch (\Exception $e) {
        echo "   ❌ Simple PDF generation failed: " . $e->getMessage() . "\n";
    }
    
    echo "8. Memory and performance analysis...\n";
    
    $memoryUsage = memory_get_usage(true);
    $memoryPeak = memory_get_peak_usage(true);
    echo "   Current memory usage: " . round($memoryUsage / 1024 / 1024, 2) . " MB\n";
    echo "   Peak memory usage: " . round($memoryPeak / 1024 / 1024, 2) . " MB\n";
    
    $memoryLimit = ini_get('memory_limit');
    echo "   PHP memory limit: {$memoryLimit}\n";
    
    if ($memoryPeak > 128 * 1024 * 1024) { // More than 128MB
        echo "   ⚠️  High memory usage detected\n";
    } else {
        echo "   ✅ Memory usage within reasonable limits\n";
    }
    
    echo "9. Recommendations...\n";
    
    if (!$hasGD) {
        echo "   GD Extension Issues:\n";
        echo "   - DomPDF may have limitations with image processing\n";
        echo "   - Base64 embedding should still work\n";
        echo "   - Consider installing GD extension for better compatibility\n";
        echo "   - Alternative: Use CSS-only designs\n";
    }
    
    echo "   PDF Generation Options:\n";
    echo "   1. ✅ Base64 image embedding (current approach)\n";
    echo "   2. ✅ CSS-only certificate designs\n";
    echo "   3. ⚠️  Install GD extension for full compatibility\n";
    echo "   4. ⚠️  Use alternative PDF libraries (TCPDF, mPDF)\n";
    echo "   5. ⚠️  Use external tools (wkhtmltopdf)\n";
    
    echo "10. Cleanup...\n";
    
    // Clean up test files
    if (isset($testPath) && file_exists($testPath)) {
        unlink($testPath);
        echo "   ✅ Cleaned up test PDF\n";
    }
    
    if (isset($simplePath) && file_exists($simplePath)) {
        unlink($simplePath);
        echo "   ✅ Cleaned up simple PDF\n";
    }
    
    echo "\n=== PDF GENERATION TEST COMPLETED ===\n";
    echo "✅ PDF generation analysis complete!\n";
    echo "\nKey Findings:\n";
    echo "🔧 GD EXTENSION: " . ($hasGD ? 'Available' : 'Missing - may cause issues') . "\n";
    echo "🔧 BASE64 EMBEDDING: " . (isset($base64) ? 'Working' : 'Failed') . "\n";
    echo "🔧 PDF GENERATION: " . (isset($pdfOutput) ? 'Working' : 'Failed') . "\n";
    echo "🔧 SIMPLE CERTIFICATES: " . (isset($simplePdfOutput) ? 'Working' : 'Failed') . "\n";
    echo "\nRecommendations:\n";
    if (!$hasGD) {
        echo "⚠️  Install GD extension for full image support\n";
        echo "✅ Use CSS-only designs as fallback\n";
        echo "✅ Base64 embedding may still work\n";
    } else {
        echo "✅ GD extension available - full image support\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
