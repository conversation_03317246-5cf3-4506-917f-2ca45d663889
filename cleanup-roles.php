<?php

echo "=== CLEANING UP ROLES ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking for users with 'ketua_mahasiswa' role...\n";
    
    $ketuaMahasiswaUsers = \App\Models\User::where('role', 'ketua_mahasiswa')->get();
    
    if ($ketuaMahasiswaUsers->count() > 0) {
        echo "   Found {$ketuaMahasiswaUsers->count()} users with 'ketua_mahasiswa' role:\n";
        
        foreach ($ketuaMahasiswaUsers as $user) {
            echo "      - {$user->name} ({$user->email})\n";
            
            // Convert to regular student
            $user->update(['role' => 'student']);
            echo "        → Converted to 'student' role\n";
        }
    } else {
        echo "   ✅ No users found with 'ketua_mahasiswa' role\n";
    }
    
    echo "\n2. Current role distribution:\n";
    
    $roleStats = \App\Models\User::selectRaw('role, COUNT(*) as count')
        ->groupBy('role')
        ->get();
    
    foreach ($roleStats as $stat) {
        echo "   {$stat->role}: {$stat->count} users\n";
    }
    
    echo "\n3. Validating role system...\n";
    
    $validRoles = ['student', 'ketua_ukm', 'admin'];
    $invalidUsers = \App\Models\User::whereNotIn('role', $validRoles)->get();
    
    if ($invalidUsers->count() > 0) {
        echo "   ❌ Found {$invalidUsers->count()} users with invalid roles:\n";
        foreach ($invalidUsers as $user) {
            echo "      - {$user->name}: {$user->role}\n";
        }
    } else {
        echo "   ✅ All users have valid roles\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== CLEANUP COMPLETED ===\n";
echo "\nValid roles in system:\n";
echo "- student (Mahasiswa)\n";
echo "- ketua_ukm (Ketua UKM)\n";
echo "- admin (Administrator)\n";
echo "\nRole 'ketua_mahasiswa' has been removed from the system.\n";
