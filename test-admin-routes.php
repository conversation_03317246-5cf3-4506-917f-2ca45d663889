<?php

// Test script untuk memverifikasi admin routes
echo "=== TESTING ADMIN ROUTES ===\n\n";

try {
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking admin routes...\n";
    
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    
    $adminRoutes = [
        'admin.dashboard' => '/admin',
        'admin.dashboard.alt' => '/admin/dashboard',
        'admin.stats' => '/admin/stats',
        'admin.users.index' => '/admin/users',
        'admin.users.create' => '/admin/users/create',
        'admin.users.store' => '/admin/users',
        'admin.users.show' => '/admin/users/{user}',
        'admin.users.edit' => '/admin/users/{user}/edit',
        'admin.users.update' => '/admin/users/{user}',
        'admin.users.destroy' => '/admin/users/{user}',
        'admin.ukms.index' => '/admin/ukms',
        'admin.ukms.create' => '/admin/ukms/create',
        'admin.ukms.store' => '/admin/ukms',
        'admin.ukms.show' => '/admin/ukms/{ukm}',
        'admin.ukms.edit' => '/admin/ukms/{ukm}/edit',
        'admin.ukms.update' => '/admin/ukms/{ukm}',
        'admin.ukms.destroy' => '/admin/ukms/{ukm}',
    ];
    
    $foundRoutes = 0;
    $totalRoutes = count($adminRoutes);
    
    foreach ($adminRoutes as $routeName => $expectedUri) {
        $route = $routes->getByName($routeName);
        if ($route) {
            echo "   ✅ {$routeName}: {$route->uri()}\n";
            $foundRoutes++;
        } else {
            echo "   ❌ {$routeName}: NOT FOUND\n";
        }
    }
    
    echo "\n2. Route Summary:\n";
    echo "   Found: {$foundRoutes}/{$totalRoutes} routes\n";
    
    if ($foundRoutes === $totalRoutes) {
        echo "   ✅ All admin routes are properly registered!\n";
    } else {
        echo "   ❌ Some admin routes are missing!\n";
    }
    
    echo "\n3. Checking middleware...\n";
    
    $adminDashboardRoute = $routes->getByName('admin.dashboard');
    if ($adminDashboardRoute) {
        $middleware = $adminDashboardRoute->middleware();
        echo "   Admin dashboard middleware: " . implode(', ', $middleware) . "\n";
        
        if (in_array('auth', $middleware) && in_array('admin', $middleware)) {
            echo "   ✅ Proper middleware applied (auth, admin)\n";
        } else {
            echo "   ❌ Missing required middleware\n";
        }
    }
    
    echo "\n4. Checking admin users...\n";
    
    $adminUsers = \App\Models\User::where('role', 'admin')->get();
    if ($adminUsers->count() > 0) {
        echo "   ✅ Found {$adminUsers->count()} admin user(s):\n";
        foreach ($adminUsers as $admin) {
            echo "      - {$admin->name} ({$admin->email})\n";
        }
    } else {
        echo "   ❌ No admin users found!\n";
    }
    
    echo "\n5. Testing admin controller methods...\n";
    
    $adminController = new \App\Http\Controllers\Admin\AdminController();
    
    // Test if methods exist
    $methods = ['dashboard', 'stats'];
    foreach ($methods as $method) {
        if (method_exists($adminController, $method)) {
            echo "   ✅ Method '{$method}' exists\n";
        } else {
            echo "   ❌ Method '{$method}' missing\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== ADMIN ROUTES TEST COMPLETED ===\n";
echo "\nAdmin Access Information:\n";
echo "- Login URL: http://127.0.0.1:8000/login\n";
echo "- Admin Email: <EMAIL>\n";
echo "- Admin Password: admin123\n";
echo "- Admin Dashboard: http://127.0.0.1:8000/admin\n";
echo "- Admin Dashboard Alt: http://127.0.0.1:8000/admin/dashboard\n";
echo "- User Management: http://127.0.0.1:8000/admin/users\n";
echo "- UKM Management: http://127.0.0.1:8000/admin/ukms\n";
echo "- Admin Stats API: http://127.0.0.1:8000/admin/stats\n";
