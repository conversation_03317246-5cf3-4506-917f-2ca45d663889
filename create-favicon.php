<?php
echo "=== FAVICON CREATOR FROM TELKOM LOGO ===\n\n";

$sourceFile = 'storage/app/public/Telkom.png';
$publicDir = 'public/';

// Check if source file exists
if (!file_exists($sourceFile)) {
    echo "❌ Source file not found: $sourceFile\n";
    exit(1);
}

echo "✅ Source file found: $sourceFile\n";

// Copy logo to public directory
$logoDestination = $publicDir . 'telkom-logo.png';
if (copy($sourceFile, $logoDestination)) {
    echo "✅ Logo copied to: $logoDestination\n";
} else {
    echo "❌ Failed to copy logo\n";
    exit(1);
}

// Create favicon.ico (simple copy for now, browsers can handle PNG)
$faviconDestination = $publicDir . 'favicon.ico';
if (copy($sourceFile, $faviconDestination)) {
    echo "✅ Favicon created: $faviconDestination\n";
} else {
    echo "❌ Failed to create favicon\n";
}

// Create apple-touch-icon
$appleIconDestination = $publicDir . 'apple-touch-icon.png';
if (copy($sourceFile, $appleIconDestination)) {
    echo "✅ Apple touch icon created: $appleIconDestination\n";
} else {
    echo "❌ Failed to create apple touch icon\n";
}

// Create different sizes if GD extension is available
if (extension_loaded('gd')) {
    echo "\n📐 Creating different favicon sizes...\n";
    
    $sourceImage = imagecreatefrompng($sourceFile);
    if ($sourceImage) {
        $sizes = [16, 32, 48, 64, 128, 180];
        
        foreach ($sizes as $size) {
            $resized = imagecreatetruecolor($size, $size);
            
            // Enable alpha blending for transparency
            imagealphablending($resized, false);
            imagesavealpha($resized, true);
            $transparent = imagecolorallocatealpha($resized, 255, 255, 255, 127);
            imagefill($resized, 0, 0, $transparent);
            imagealphablending($resized, true);
            
            // Get original dimensions
            $originalWidth = imagesx($sourceImage);
            $originalHeight = imagesy($sourceImage);
            
            // Resize image
            imagecopyresampled($resized, $sourceImage, 0, 0, 0, 0, $size, $size, $originalWidth, $originalHeight);
            
            // Save resized image
            $filename = $publicDir . "favicon-{$size}x{$size}.png";
            if (imagepng($resized, $filename)) {
                echo "  ✅ Created: favicon-{$size}x{$size}.png\n";
            }
            
            imagedestroy($resized);
        }
        
        imagedestroy($sourceImage);
    } else {
        echo "❌ Failed to load source image for resizing\n";
    }
} else {
    echo "⚠️  GD extension not available, skipping size variations\n";
}

echo "\n🎯 Next steps:\n";
echo "1. Update layout files to use new favicon\n";
echo "2. Clear browser cache to see changes\n";
echo "3. Test favicon in different browsers\n\n";

echo "=== FAVICON CREATION COMPLETE ===\n";
?>
