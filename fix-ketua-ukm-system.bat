@echo off
echo ========================================
echo   FIXING KETUA UKM SYSTEM
echo ========================================
echo.

echo [1/8] Clearing all caches...
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

echo.
echo [2/8] Running migrations...
php artisan migrate --force

echo.
echo [3/8] Creating admin user...
php create-admin-user.php

echo.
echo [4/8] Debugging role issues...
php debug-role-issue.php

echo.
echo [5/8] Creating sample ketua UKM users...
php artisan tinker --execute="
\$ketua1 = App\Models\User::updateOrCreate(['email' => '<EMAIL>'], [
    'nim' => '1234567890',
    'name' => 'Ahmad <PERSON> UKM',
    'email' => '<EMAIL>',
    'password' => Hash::make('password123'),
    'phone' => '081234567890',
    'gender' => 'male',
    'faculty' => 'Fakultas Informatika',
    'major' => 'Sistem Informasi',
    'batch' => '2022',
    'role' => 'ketua_ukm',
    'status' => 'active',
    'email_verified_at' => now(),
]);

\$ketua2 = App\Models\User::updateOrCreate(['email' => '<EMAIL>'], [
    'nim' => '1234567891',
    'name' => 'Siti Ketua UKM',
    'email' => '<EMAIL>',
    'password' => Hash::make('password123'),
    'phone' => '081234567891',
    'gender' => 'female',
    'faculty' => 'Fakultas Teknik Elektro',
    'major' => 'Teknik Informatika',
    'batch' => '2022',
    'role' => 'ketua_ukm',
    'status' => 'active',
    'email_verified_at' => now(),
]);

echo 'Sample Ketua UKM users created successfully!';
"

echo.
echo [6/8] Creating sample UKM with leader assignment...
php artisan tinker --execute="
\$ukm = App\Models\Ukm::updateOrCreate(['slug' => 'ukm-teknologi'], [
    'name' => 'UKM Teknologi',
    'slug' => 'ukm-teknologi',
    'description' => 'UKM yang fokus pada pengembangan teknologi dan inovasi',
    'vision' => 'Menjadi wadah pengembangan teknologi terdepan',
    'mission' => 'Mengembangkan kemampuan teknologi mahasiswa',
    'category' => 'technology',
    'max_members' => 50,
    'current_members' => 0,
    'meeting_schedule' => 'Setiap Rabu, 19:00 - 21:00',
    'meeting_location' => 'Lab Komputer 1',
    'status' => 'active',
    'is_recruiting' => true,
    'leader_id' => App\Models\User::where('email', '<EMAIL>')->first()->id,
    'established_date' => now(),
    'contact_info' => json_encode([
        'email' => '<EMAIL>',
        'phone' => '081234567890',
        'instagram' => 'ukm_teknologi_telkom'
    ])
]);

echo 'Sample UKM with leader created successfully!';
"

echo.
echo [7/8] Testing routes...
php artisan route:list | findstr ketua-ukm

echo.
echo [8/8] Final verification...
php test-unified-login.php

echo.
echo ========================================
echo   KETUA UKM SYSTEM READY!
echo ========================================
echo.
echo FIXES IMPLEMENTED:
echo ✓ Fixed 404 error on UKM edit form
echo ✓ Added Ketua UKM dashboard and routes
echo ✓ Added "Kelola UKM" menu for ketua_ukm role
echo ✓ Fixed field names in UKM edit form
echo ✓ Added event management for Ketua UKM
echo ✓ Role persistence debugging
echo.
echo LOGIN INFORMATION:
echo Admin: <EMAIL> / admin123
echo Ketua UKM 1: <EMAIL> / password123
echo Ketua UKM 2: <EMAIL> / password123
echo.
echo KETUA UKM FEATURES:
echo - Dashboard: /ketua-ukm/dashboard
echo - Edit UKM: /ketua-ukm/ukm/edit
echo - Manage Events: /ketua-ukm/events
echo - Create Events: /ketua-ukm/events/create
echo.
echo WORKFLOW:
echo 1. Admin assigns ketua_ukm role to user
echo 2. Admin assigns ketua to UKM in UKM edit form
echo 3. Ketua UKM sees "Kelola UKM" menu
echo 4. Ketua UKM can edit UKM and manage events
echo.
echo Starting Laravel server...
echo.

php artisan serve --host=127.0.0.1 --port=8000
