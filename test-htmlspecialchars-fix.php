<?php

echo "=== TESTING HTMLSPECIALCHARS ERROR FIX ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing EventRegistration model casts...\n";
    
    $registration = \App\Models\EventRegistration::first();
    if ($registration) {
        echo "   ✅ Registration found: ID {$registration->id}\n";
        
        // Check field types
        $availabilityForm = $registration->availability_form;
        $additionalData = $registration->additional_data;
        
        echo "   Testing availability_form field:\n";
        if (is_array($availabilityForm)) {
            echo "     ✅ availability_form is array: " . json_encode($availabilityForm) . "\n";
        } elseif (is_string($availabilityForm)) {
            echo "     ⚠️  availability_form is string: {$availabilityForm}\n";
        } elseif (is_null($availabilityForm)) {
            echo "     ℹ️  availability_form is null\n";
        } else {
            echo "     ❌ availability_form is unexpected type: " . gettype($availabilityForm) . "\n";
        }
        
        echo "   Testing additional_data field:\n";
        if (is_array($additionalData)) {
            echo "     ✅ additional_data is array: " . json_encode($additionalData) . "\n";
        } elseif (is_string($additionalData)) {
            echo "     ⚠️  additional_data is string: {$additionalData}\n";
        } elseif (is_null($additionalData)) {
            echo "     ℹ️  additional_data is null\n";
        } else {
            echo "     ❌ additional_data is unexpected type: " . gettype($additionalData) . "\n";
        }
        
    } else {
        echo "   ❌ No registration found for testing\n";
    }
    
    echo "2. Testing view file fixes...\n";
    
    $viewPath = resource_path('views/ketua-ukm/events/registration-details.blade.php');
    if (file_exists($viewPath)) {
        $viewContent = file_get_contents($viewPath);
        
        // Check for proper array handling
        $checks = [
            'is_array($availabilityData)' => 'Availability form array check',
            'is_array($value)' => 'Value array check in loops',
            'implode(\', \', $value)' => 'Array to string conversion',
            'ucfirst(str_replace(\'_\', \' \', $key))' => 'Key formatting',
        ];
        
        foreach ($checks as $pattern => $description) {
            if (strpos($viewContent, $pattern) !== false) {
                echo "   ✅ {$description}: FOUND\n";
            } else {
                echo "   ❌ {$description}: MISSING\n";
            }
        }
        
        // Check for dangerous direct array output
        $dangerousPatterns = [
            '{{ $registration->availability_form }}' => 'Direct availability_form output',
            '{{ $registration->additional_data }}' => 'Direct additional_data output',
        ];
        
        foreach ($dangerousPatterns as $pattern => $description) {
            if (strpos($viewContent, $pattern) !== false) {
                echo "   ❌ {$description}: STILL EXISTS (dangerous)\n";
            } else {
                echo "   ✅ {$description}: REMOVED (safe)\n";
            }
        }
        
    } else {
        echo "   ❌ View file not found\n";
    }
    
    echo "3. Testing sample data creation...\n";
    
    // Create sample registration with array data for testing
    $event = \App\Models\Event::first();
    $user = \App\Models\User::where('role', 'student')->first();
    
    if ($event && $user) {
        echo "   Creating test registration with array data...\n";
        
        $testRegistration = \App\Models\EventRegistration::create([
            'user_id' => $user->id,
            'event_id' => $event->id,
            'status' => 'approved',
            'motivation' => 'Test motivation for array handling',
            'availability_form' => [
                'full_attendance' => 'yes',
                'partial_days' => ['day1', 'day2'],
                'special_requirements' => 'None',
            ],
            'additional_data' => [
                'dietary_restrictions' => 'Vegetarian',
                'emergency_contact' => '081234567890',
                'transportation' => 'Public transport',
                'skills' => ['PHP', 'Laravel', 'JavaScript'],
            ],
        ]);
        
        echo "   ✅ Test registration created: ID {$testRegistration->id}\n";
        
        // Test the data types
        echo "   Testing created registration data types:\n";
        echo "     availability_form type: " . gettype($testRegistration->availability_form) . "\n";
        echo "     additional_data type: " . gettype($testRegistration->additional_data) . "\n";
        
        // Clean up test data
        $testRegistration->delete();
        echo "   ✅ Test registration cleaned up\n";
        
    } else {
        echo "   ⚠️  Cannot create test registration (missing event or user)\n";
    }
    
    echo "4. Testing route and controller...\n";
    
    if ($registration && $registration->event) {
        $event = $registration->event;
        
        try {
            $url = route('ketua-ukm.events.registrations.show', [$event->slug, $registration->id]);
            echo "   ✅ Route generation successful: {$url}\n";
        } catch (Exception $e) {
            echo "   ❌ Route generation failed: " . $e->getMessage() . "\n";
        }
        
        // Check controller method
        $controller = new \App\Http\Controllers\KetuaUkmController();
        if (method_exists($controller, 'showRegistrationDetails')) {
            echo "   ✅ Controller method exists\n";
        } else {
            echo "   ❌ Controller method missing\n";
        }
    }
    
    echo "5. Testing potential error scenarios...\n";
    
    // Test different data scenarios
    $scenarios = [
        'null availability_form' => null,
        'empty array availability_form' => [],
        'string availability_form' => '{"test": "value"}',
        'nested array availability_form' => ['skills' => ['PHP', 'Laravel'], 'level' => 'intermediate'],
    ];
    
    foreach ($scenarios as $description => $testData) {
        echo "   Testing {$description}:\n";
        
        try {
            // Simulate the view logic
            $availabilityData = is_array($testData) ? $testData : json_decode($testData, true);
            
            if (is_array($availabilityData)) {
                echo "     ✅ Handled as array successfully\n";
                foreach ($availabilityData as $key => $value) {
                    if (is_array($value)) {
                        $output = implode(', ', $value);
                        echo "     ✅ Array value converted to string: {$output}\n";
                    } else {
                        echo "     ✅ String value handled: {$value}\n";
                    }
                }
            } else {
                echo "     ✅ Handled as non-array: " . ($testData ?? 'null') . "\n";
            }
        } catch (Exception $e) {
            echo "     ❌ Error handling {$description}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== HTMLSPECIALCHARS ERROR FIX COMPLETED ===\n";
    echo "✅ Registration details page should now work without htmlspecialchars errors!\n";
    echo "\nWhat was fixed:\n";
    echo "🔧 ISSUE: Array fields (availability_form, additional_data) displayed directly as strings\n";
    echo "🔧 SOLUTION: Added proper array handling with type checking\n";
    echo "🔧 ENHANCEMENT: Safe array-to-string conversion using implode()\n";
    echo "\nKey improvements:\n";
    echo "✅ Proper type checking before display\n";
    echo "✅ Safe array-to-string conversion\n";
    echo "✅ Graceful handling of nested arrays\n";
    echo "✅ Fallback for non-array data\n";
    echo "\nTesting instructions:\n";
    echo "1. Login as ketua UKM user\n";
    echo "2. Go to event registrations\n";
    echo "3. Click 'Detail' on any registration\n";
    echo "4. Should load without htmlspecialchars errors\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
