<?php

echo "=== CHECKING DATABASE SCHEMA ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check ukm_members table structure
    echo "📋 UKM_MEMBERS TABLE STRUCTURE:\n";
    $stmt = $pdo->query("DESCRIBE ukm_members");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']} ({$column['Null']}, Default: {$column['Default']})\n";
        
        // Check if status column has enum restriction
        if ($column['Field'] === 'status') {
            echo "  🔍 STATUS COLUMN DETAILS:\n";
            echo "  Type: {$column['Type']}\n";
            echo "  Null: {$column['Null']}\n";
            echo "  Default: {$column['Default']}\n";
            
            // If it's an enum, show possible values
            if (strpos($column['Type'], 'enum') !== false) {
                echo "  ⚠️  ENUM detected - this might cause the truncation error!\n";
                echo "  Possible values: {$column['Type']}\n";
            }
        }
    }
    
    // Check current status values in use
    echo "\n📊 CURRENT STATUS VALUES IN USE:\n";
    $stmt = $pdo->query("SELECT DISTINCT status, COUNT(*) as count FROM ukm_members GROUP BY status");
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($statuses as $status) {
        echo "- '{$status['status']}': {$status['count']} records\n";
    }
    
    // Test if we can insert 'rejected' status
    echo "\n🧪 TESTING 'rejected' STATUS:\n";
    try {
        $stmt = $pdo->prepare("SELECT * FROM ukm_members WHERE status = 'rejected' LIMIT 1");
        $stmt->execute();
        echo "✅ 'rejected' status can be queried\n";
    } catch (Exception $e) {
        echo "❌ Error querying 'rejected' status: " . $e->getMessage() . "\n";
    }
    
    // Show SQL to fix enum if needed
    echo "\n🔧 IF STATUS IS ENUM AND MISSING 'rejected', RUN THIS SQL:\n";
    echo "ALTER TABLE ukm_members MODIFY COLUMN status ENUM('pending', 'active', 'rejected', 'inactive') DEFAULT 'pending';\n";
    
    // Alternative: Change to VARCHAR
    echo "\n🔧 OR CHANGE STATUS TO VARCHAR (RECOMMENDED):\n";
    echo "ALTER TABLE ukm_members MODIFY COLUMN status VARCHAR(20) DEFAULT 'pending';\n";
    
} catch (Exception $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
}
