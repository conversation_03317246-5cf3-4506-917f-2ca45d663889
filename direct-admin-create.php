<?php

echo "=== DIRECT ADMIN CREATION ===\n";

// Database connection details from .env
$host = '127.0.0.1';
$dbname = 'ukmwebL';
$username = 'root';
$password = '';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Users table exists\n";
    } else {
        echo "❌ Users table does not exist\n";
        exit;
    }
    
    // Check current users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "📊 Current users in database: {$result['count']}\n";
    
    // Check if admin exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        echo "⚠️  Admin user already exists!\n";
        echo "   ID: {$existingAdmin['id']}\n";
        echo "   Name: {$existingAdmin['name']}\n";
        echo "   Email: {$existingAdmin['email']}\n";
        echo "   Role: {$existingAdmin['role']}\n";
        echo "   Status: {$existingAdmin['status']}\n";
        
        // Update password and status
        $newPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ?, status = 'active', email_verified_at = NOW() WHERE email = ?");
        $stmt->execute([$newPassword, '<EMAIL>']);
        
        echo "✅ Password updated to 'admin123'\n";
        echo "✅ Status set to 'active'\n";
        
    } else {
        echo "🔧 Creating new admin user...\n";
        
        // Create admin user
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (
                nim, name, email, password, phone, gender, faculty, major, batch,
                role, status, email_verified_at, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
        ");

        $result = $stmt->execute([
            'ADMIN001',
            'Administrator',
            '<EMAIL>',
            $hashedPassword,
            '081234567890',
            'male',
            'Administrasi',
            'Sistem Informasi',
            '2024',
            'admin',
            'active',
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            echo "✅ Admin user created successfully!\n";
        } else {
            echo "❌ Failed to create admin user\n";
        }
    }
    
    // Verify admin user
    echo "\n🔍 Verifying admin user...\n";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin verification successful!\n";
        echo "   ID: {$admin['id']}\n";
        echo "   NIM: {$admin['nim']}\n";
        echo "   Name: {$admin['name']}\n";
        echo "   Email: {$admin['email']}\n";
        echo "   Role: {$admin['role']}\n";
        echo "   Status: {$admin['status']}\n";
        echo "   Email Verified: {$admin['email_verified_at']}\n";
        
        // Test password
        if (password_verify('admin123', $admin['password'])) {
            echo "✅ Password verification: SUCCESS\n";
        } else {
            echo "❌ Password verification: FAILED\n";
        }
        
        echo "\n🎉 ADMIN READY!\n";
        echo "📧 Email: <EMAIL>\n";
        echo "🔒 Password: admin123\n";
        echo "🌐 Login URL: http://localhost:8000/login\n";
        
    } else {
        echo "❌ Admin verification failed\n";
    }
    
    // Show all users
    echo "\n📋 All users in database:\n";
    $stmt = $pdo->query("SELECT id, nim, name, email, role, status FROM users ORDER BY id");
    while ($user = $stmt->fetch()) {
        echo "   {$user['id']}. {$user['name']} ({$user['email']}) - {$user['role']} - {$user['status']}\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
