<?php

echo "=== TESTING EVENT STATUS AUTO-UPDATE LOGIC ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing Event model methods...\n";
    
    $event = \App\Models\Event::where('status', 'published')->first();
    if (!$event) {
        echo "   Creating test event...\n";
        $ukm = \App\Models\Ukm::first();
        $event = \App\Models\Event::create([
            'ukm_id' => $ukm->id,
            'title' => 'Test Event Status Update',
            'slug' => 'test-event-status-update',
            'description' => 'Test event for status update logic',
            'start_datetime' => now()->addDays(1),
            'end_datetime' => now()->addDays(1)->addHours(2),
            'location' => 'Test Location',
            'status' => 'published',
            'registration_open' => true,
        ]);
        echo "   ✅ Test event created: {$event->title}\n";
    } else {
        echo "   ✅ Using existing event: {$event->title}\n";
    }
    
    echo "2. Testing getCurrentStatus method...\n";
    
    $currentStatus = $event->getCurrentStatus();
    echo "   Current status: {$currentStatus}\n";
    echo "   Database status: {$event->status}\n";
    echo "   Start datetime: {$event->start_datetime}\n";
    echo "   End datetime: {$event->end_datetime}\n";
    echo "   Current time: " . now() . "\n";
    
    echo "3. Testing updateStatusBasedOnDates method...\n";
    
    // Test scenario 1: Future event
    echo "   Testing future event scenario...\n";
    $event->update([
        'start_datetime' => now()->addDays(1),
        'end_datetime' => now()->addDays(1)->addHours(2),
    ]);
    $event->updateStatusBasedOnDates();
    $event->refresh();
    echo "     Status after future date: {$event->status}\n";
    echo "     Registration open: " . ($event->registration_open ? 'Yes' : 'No') . "\n";
    
    // Test scenario 2: Current/ongoing event
    echo "   Testing ongoing event scenario...\n";
    $event->update([
        'start_datetime' => now()->subHour(),
        'end_datetime' => now()->addHour(),
    ]);
    $event->updateStatusBasedOnDates();
    $event->refresh();
    echo "     Status after ongoing date: {$event->status}\n";
    echo "     Registration open: " . ($event->registration_open ? 'Yes' : 'No') . "\n";
    
    // Test scenario 3: Past/completed event
    echo "   Testing completed event scenario...\n";
    $event->update([
        'start_datetime' => now()->subDays(1),
        'end_datetime' => now()->subHours(2),
    ]);
    $event->updateStatusBasedOnDates();
    $event->refresh();
    echo "     Status after past date: {$event->status}\n";
    echo "     Registration open: " . ($event->registration_open ? 'Yes' : 'No') . "\n";
    
    echo "4. Testing isRegistrationOpen method...\n";
    
    // Reset to future event for registration test
    $event->update([
        'start_datetime' => now()->addDays(1),
        'end_datetime' => now()->addDays(1)->addHours(2),
        'status' => 'published',
        'registration_open' => true,
    ]);
    
    $isOpen = $event->isRegistrationOpen();
    echo "   Registration open for future event: " . ($isOpen ? 'Yes' : 'No') . "\n";
    
    // Test with ongoing event
    $event->update([
        'start_datetime' => now()->subHour(),
        'end_datetime' => now()->addHour(),
    ]);
    $event->updateStatusBasedOnDates();
    $event->refresh();
    
    $isOpen = $event->isRegistrationOpen();
    echo "   Registration open for ongoing event: " . ($isOpen ? 'Yes' : 'No') . "\n";
    
    echo "5. Testing boot event listeners...\n";
    
    // Reset event to published future event
    $event->update([
        'status' => 'published',
        'start_datetime' => now()->addDays(1),
        'end_datetime' => now()->addDays(1)->addHours(2),
        'registration_open' => true,
    ]);
    
    echo "   Before date change - Status: {$event->status}, Registration: " . ($event->registration_open ? 'Open' : 'Closed') . "\n";
    
    // Change to ongoing event (should trigger boot listener)
    $event->update([
        'start_datetime' => now()->subHour(),
        'end_datetime' => now()->addHour(),
    ]);
    $event->refresh();
    
    echo "   After date change to ongoing - Status: {$event->status}, Registration: " . ($event->registration_open ? 'Open' : 'Closed') . "\n";
    
    // Change to completed event
    $event->update([
        'start_datetime' => now()->subDays(1),
        'end_datetime' => now()->subHours(2),
    ]);
    $event->refresh();
    
    echo "   After date change to completed - Status: {$event->status}, Registration: " . ($event->registration_open ? 'Open' : 'Closed') . "\n";
    
    echo "6. Testing status display logic...\n";
    
    // Test different status scenarios for display
    $scenarios = [
        'Future Event' => [
            'start_datetime' => now()->addDays(1),
            'end_datetime' => now()->addDays(1)->addHours(2),
            'expected_status' => 'published',
            'expected_registration' => true,
        ],
        'Ongoing Event' => [
            'start_datetime' => now()->subHour(),
            'end_datetime' => now()->addHour(),
            'expected_status' => 'ongoing',
            'expected_registration' => false,
        ],
        'Completed Event' => [
            'start_datetime' => now()->subDays(1),
            'end_datetime' => now()->subHours(2),
            'expected_status' => 'completed',
            'expected_registration' => false,
        ],
    ];
    
    foreach ($scenarios as $scenarioName => $scenario) {
        echo "   Testing {$scenarioName}:\n";
        
        $event->update([
            'status' => 'published',
            'start_datetime' => $scenario['start_datetime'],
            'end_datetime' => $scenario['end_datetime'],
            'registration_open' => true,
        ]);
        
        $event->updateStatusBasedOnDates();
        $event->refresh();
        
        $statusMatch = $event->status === $scenario['expected_status'];
        $registrationMatch = $event->registration_open === $scenario['expected_registration'];
        
        echo "     Status: {$event->status} " . ($statusMatch ? '✅' : '❌') . "\n";
        echo "     Registration: " . ($event->registration_open ? 'Open' : 'Closed') . " " . ($registrationMatch ? '✅' : '❌') . "\n";
    }
    
    echo "7. Testing view status display...\n";
    
    // Test how status appears in views
    $event->update([
        'status' => 'published',
        'start_datetime' => now()->addDays(1),
        'end_datetime' => now()->addDays(1)->addHours(2),
        'registration_open' => true,
    ]);
    $event->updateStatusBasedOnDates();
    $event->refresh();
    
    $currentStatus = $event->getCurrentStatus();
    $displayStatus = ucfirst($currentStatus);
    
    echo "   Current status for display: {$displayStatus}\n";
    echo "   Registration status: " . ($event->isRegistrationOpen() ? 'Buka' : 'Tutup') . "\n";
    
    // Clean up test event if we created it
    if ($event->title === 'Test Event Status Update') {
        $event->delete();
        echo "   ✅ Test event cleaned up\n";
    }
    
    echo "\n=== EVENT STATUS AUTO-UPDATE LOGIC COMPLETED ===\n";
    echo "✅ Event status will now automatically update based on dates!\n";
    echo "\nLogic Summary:\n";
    echo "🕐 FUTURE EVENT (start_datetime > now):\n";
    echo "   - Status: published\n";
    echo "   - Registration: Open (if within registration period)\n";
    echo "\n⏰ ONGOING EVENT (start_datetime <= now <= end_datetime):\n";
    echo "   - Status: ongoing\n";
    echo "   - Registration: Closed\n";
    echo "\n✅ COMPLETED EVENT (end_datetime < now):\n";
    echo "   - Status: completed\n";
    echo "   - Registration: Closed\n";
    echo "   - Attendance records created\n";
    echo "\nTriggers:\n";
    echo "✅ Automatic when admin/ketua UKM updates event dates\n";
    echo "✅ Manual via 'Update Status' button in admin panel\n";
    echo "✅ Can be scheduled via cron job for real-time updates\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
