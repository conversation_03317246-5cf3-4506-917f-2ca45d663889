<?php
echo "=== UPDATING UKM LOGOS (SIMPLE) ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Logo mappings for UKMs
    $logoMappings = [
        'badminton' => 'ukms/logos/badminton-logo.png',
        'dpm' => 'ukms/logos/dpm-logo.png',
        'esport' => 'ukms/logos/esport-logo.png',
        'futsal' => 'ukms/logos/futsal-logo.png',
        'imma' => 'ukms/logos/imma-logo.png',
        'mapala' => 'ukms/logos/mapala-logo.png',
        'pmk' => 'ukms/logos/pmk-logo.png',
        'seni-budaya' => 'ukms/logos/seni-budaya-logo.png',
        'sistem-informasi' => 'ukms/logos/sistem-informasi-logo.png'
    ];
    
    echo "Updating UKM logo paths...\n\n";
    
    $sql = "UPDATE ukms SET logo = ?, updated_at = NOW() WHERE slug = ?";
    $stmt = $pdo->prepare($sql);
    
    $updated = 0;
    $failed = 0;
    
    foreach ($logoMappings as $slug => $logoPath) {
        try {
            if ($stmt->execute([$logoPath, $slug])) {
                echo "   ✅ Updated {$slug}: {$logoPath}\n";
                $updated++;
            } else {
                echo "   ❌ Failed to update: {$slug}\n";
                $failed++;
            }
        } catch (Exception $e) {
            echo "   ❌ Error updating {$slug}: " . $e->getMessage() . "\n";
            $failed++;
        }
    }
    
    echo "\n=== RESULT ===\n";
    echo "✅ Successfully updated: {$updated} UKMs\n";
    echo "❌ Failed: {$failed} UKMs\n";
    
    // Show UKMs with logos
    echo "\n📊 UKMs with Logo Paths:\n";
    $result = $pdo->query("SELECT name, slug, logo FROM ukms WHERE logo IS NOT NULL ORDER BY name");
    $count = 0;
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $count++;
        echo "   {$count}. {$row['name']}\n";
        echo "      📁 Path: {$row['logo']}\n";
        echo "      🌐 URL: http://localhost:8000/storage/{$row['logo']}\n\n";
    }
    
    echo "📋 Next Steps:\n";
    echo "1. Add actual logo files to storage/app/public/ukms/logos/\n";
    echo "2. Or use placeholder images from online sources\n";
    echo "3. Check admin page: http://localhost:8000/admin/ukms\n";
    
    echo "\n🌐 Test URLs:\n";
    echo "   Admin UKMs: http://localhost:8000/admin/ukms\n";
    echo "   Public UKMs: http://localhost:8000/ukm\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== UPDATE COMPLETE ===\n";
?>
