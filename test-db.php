<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    echo "Database connected successfully!\n";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "Users table does not exist!\n";
        exit(1);
    }
    echo "Users table exists\n";
    
    // Check current users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetch()['count'];
    echo "Current users in database: $count\n";
    
    if ($count > 0) {
        $stmt = $pdo->query("SELECT email, role, status FROM users");
        echo "Existing users:\n";
        while ($user = $stmt->fetch()) {
            echo "  - {$user['email']} ({$user['role']}) - {$user['status']}\n";
        }
    } else {
        echo "No users found. Creating admin user...\n";
        
        // Insert admin user with bcrypt password for 'admin123'
        $hashedPassword = '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukh4Q0k2G'; // admin123
        
        $stmt = $pdo->prepare("INSERT INTO users (nim, name, email, password, phone, gender, faculty, major, batch, role, status, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())");
        
        $stmt->execute([
            'ADMIN001',
            'Administrator',
            '<EMAIL>',
            $hashedPassword,
            '081234567890',
            'male',
            'Administrasi',
            'Sistem Informasi',
            '2024',
            'admin',
            'active'
        ]);
        
        echo "Admin user created!\n";
        
        // Insert student user
        $stmt->execute([
            '1103210001',
            'John Doe',
            '<EMAIL>',
            $hashedPassword, // same password for testing
            '081234567892',
            'male',
            'Informatika',
            'Teknik Informatika',
            '2021',
            'student',
            'active'
        ]);
        
        echo "Student user created!\n";
        
        // Insert ketua UKM user
        $stmt->execute([
            '1103210002',
            'Jane Smith',
            '<EMAIL>',
            $hashedPassword, // same password for testing
            '081234567893',
            'female',
            'Informatika',
            'Sistem Informasi',
            '2021',
            'ketua_ukm',
            'active'
        ]);
        
        echo "Ketua UKM user created!\n";
    }
    
    echo "\nLogin Credentials (password: admin123):\n";
    echo "Admin: <EMAIL>\n";
    echo "Student: <EMAIL>\n";
    echo "Ketua UKM: <EMAIL>\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
