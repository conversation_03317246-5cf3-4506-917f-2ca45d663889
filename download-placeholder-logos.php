<?php
echo "=== DOWNLOADING PLACEHOLDER LOGOS ===\n\n";

try {
    // Create logos directory if it doesn't exist
    $logoDir = 'storage/app/public/ukms/logos';
    if (!file_exists($logoDir)) {
        mkdir($logoDir, 0755, true);
        echo "✅ Created logos directory: {$logoDir}\n";
    }
    
    // UKM logo mappings with placeholder URLs
    $ukms = [
        'badminton' => [
            'name' => 'BADMINTON',
            'url' => 'https://via.placeholder.com/200x200/3B82F6/FFFFFF?text=🏸'
        ],
        'dpm' => [
            'name' => 'DPM',
            'url' => 'https://via.placeholder.com/200x200/1F2937/FFFFFF?text=🏛️'
        ],
        'esport' => [
            'name' => 'ESPORT',
            'url' => 'https://via.placeholder.com/200x200/8B5CF6/FFFFFF?text=🎮'
        ],
        'futsal' => [
            'name' => 'FUTSAL',
            'url' => 'https://via.placeholder.com/200x200/10B981/FFFFFF?text=⚽'
        ],
        'imma' => [
            'name' => 'IMMA',
            'url' => 'https://via.placeholder.com/200x200/059669/FFFFFF?text=🕌'
        ],
        'mapala' => [
            'name' => 'MAPALA',
            'url' => 'https://via.placeholder.com/200x200/92400E/FFFFFF?text=🏔️'
        ],
        'pmk' => [
            'name' => 'PMK',
            'url' => 'https://via.placeholder.com/200x200/DC2626/FFFFFF?text=✝️'
        ],
        'seni-budaya' => [
            'name' => 'SENI',
            'url' => 'https://via.placeholder.com/200x200/F59E0B/FFFFFF?text=🎭'
        ],
        'sistem-informasi' => [
            'name' => 'SI',
            'url' => 'https://via.placeholder.com/200x200/6366F1/FFFFFF?text=💻'
        ]
    ];
    
    echo "Downloading placeholder logos...\n\n";
    
    $downloaded = 0;
    $failed = 0;
    
    foreach ($ukms as $slug => $data) {
        try {
            $logoFilename = $slug . '-logo.png';
            $logoPath = $logoDir . '/' . $logoFilename;
            
            // Download logo from placeholder service
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]);
            
            $logoContent = file_get_contents($data['url'], false, $context);
            
            if ($logoContent !== false) {
                if (file_put_contents($logoPath, $logoContent)) {
                    echo "   ✅ Downloaded: {$logoFilename} ({$data['name']})\n";
                    echo "      📁 Saved to: {$logoPath}\n";
                    echo "      🌐 URL: http://localhost:8000/storage/ukms/logos/{$logoFilename}\n\n";
                    $downloaded++;
                } else {
                    echo "   ❌ Failed to save: {$logoFilename}\n";
                    $failed++;
                }
            } else {
                echo "   ❌ Failed to download: {$logoFilename}\n";
                $failed++;
            }
            
            // Small delay to be nice to the placeholder service
            usleep(500000); // 0.5 seconds
            
        } catch (Exception $e) {
            echo "   ❌ Error processing {$slug}: " . $e->getMessage() . "\n";
            $failed++;
        }
    }
    
    echo "=== RESULT ===\n";
    echo "✅ Successfully downloaded: {$downloaded} logos\n";
    echo "❌ Failed: {$failed} logos\n";
    
    if ($downloaded > 0) {
        echo "\n📋 Next Steps:\n";
        echo "1. Check admin page: http://localhost:8000/admin/ukms\n";
        echo "2. Replace placeholder logos with actual UKM logos\n";
        echo "3. Upload better quality logos through admin interface\n";
        
        echo "\n🌐 Test URLs:\n";
        echo "   Admin UKMs: http://localhost:8000/admin/ukms\n";
        echo "   Public UKMs: http://localhost:8000/ukm\n";
        
        echo "\n📁 Logo Files Created:\n";
        foreach ($ukms as $slug => $data) {
            $logoFilename = $slug . '-logo.png';
            if (file_exists($logoDir . '/' . $logoFilename)) {
                echo "   ✅ {$logoFilename}\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== DOWNLOAD COMPLETE ===\n";
?>
