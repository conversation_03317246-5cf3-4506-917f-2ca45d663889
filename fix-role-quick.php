<?php

echo "Fixing role enum...\n";

try {
    // Database connection
    $host = 'localhost';
    $dbname = 'ukm_web_db';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database\n";
    
    // Update the enum
    $sql = "ALTER TABLE users MODIFY COLUMN role ENUM('student', 'ketua_ukm', 'admin') DEFAULT 'student'";
    $pdo->exec($sql);
    
    echo "✅ Role enum updated successfully!\n";
    echo "Now 'ketua_ukm' role is supported.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
