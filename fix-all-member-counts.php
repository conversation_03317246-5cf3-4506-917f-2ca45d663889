<?php
// Fix all UKM member counts
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$ukms = \App\Models\Ukm::all();

foreach ($ukms as $ukm) {
    $actualActiveCount = $ukm->members()->wherePivot('status', 'active')->count();
    
    if ($ukm->current_members != $actualActiveCount) {
        echo "Fixing {$ukm->name}: {$ukm->current_members} -> {$actualActiveCount}\n";
        $ukm->update(['current_members' => $actualActiveCount]);
    }
}

echo "All UKM member counts fixed!\n";
?>