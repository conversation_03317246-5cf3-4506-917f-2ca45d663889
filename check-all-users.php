<?php
echo "=== CHECKING ALL USERS ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check all users
    echo "📊 All Users in Database:\n";
    $result = $pdo->query("SELECT id, name, email, role, status, faculty, major, batch FROM users ORDER BY role, name");
    $userCount = 0;
    $roleCount = [];
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $userCount++;
        $role = $row['role'] ?: 'NULL';
        $roleCount[$role] = ($roleCount[$role] ?? 0) + 1;
        
        echo "   {$userCount}. {$row['name']} ({$role})\n";
        echo "      📧 {$row['email']}\n";
        echo "      ✅ Status: {$row['status']}\n";
        if ($row['faculty']) echo "      🏫 Faculty: {$row['faculty']}\n";
        if ($row['major']) echo "      📚 Major: {$row['major']}\n";
        if ($row['batch']) echo "      📅 Batch: {$row['batch']}\n";
        echo "\n";
    }
    
    echo "📈 Role Statistics:\n";
    foreach ($roleCount as $role => $count) {
        echo "   • {$role}: {$count} users\n";
    }
    
    // Check specifically for student emails we created
    echo "\n🔍 Checking Our Created Students:\n";
    $studentEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];
    
    $found = 0;
    $missing = 0;
    
    foreach ($studentEmails as $email) {
        $stmt = $pdo->prepare("SELECT name, role, status FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "   ✅ Found: {$user['name']} (Role: {$user['role']}, Status: {$user['status']})\n";
            $found++;
        } else {
            echo "   ❌ Missing: {$email}\n";
            $missing++;
        }
    }
    
    echo "\n📊 Our Students Status:\n";
    echo "   Found: {$found}\n";
    echo "   Missing: {$missing}\n";
    
    if ($missing > 0) {
        echo "\n📋 Need to create missing students:\n";
        echo "1. Run: php insert-mahasiswa-simple.php\n";
        echo "2. Or use phpMyAdmin with mahasiswa-users-phpmyadmin.sql\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== CHECK COMPLETE ===\n";
?>
