<?php
require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== SEO IMPLEMENTATION TEST ===\n\n";

// Test 1: Check if SeoHelper class exists and works
echo "1. Testing SeoHelper class...\n";
try {
    $seoData = \App\Helpers\SeoHelper::generateMeta([
        'title' => 'Test Page',
        'description' => 'This is a test page for SEO'
    ]);
    
    echo "   ✅ SeoHelper::generateMeta() works\n";
    echo "   📋 Generated title: " . $seoData['title'] . "\n";
    echo "   📋 Generated description: " . substr($seoData['description'], 0, 50) . "...\n";
    
    $orgSchema = \App\Helpers\SeoHelper::getOrganizationSchema();
    echo "   ✅ Organization schema generated\n";
    echo "   📋 Organization name: " . $orgSchema['name'] . "\n";
    
} catch (Exception $e) {
    echo "   ❌ SeoHelper error: " . $e->getMessage() . "\n";
}

// Test 2: Check sitemap routes
echo "\n2. Testing sitemap routes...\n";
$sitemapRoutes = [
    'sitemap.index' => '/sitemap.xml',
    'sitemap.pages' => '/sitemap-pages.xml',
    'sitemap.ukms' => '/sitemap-ukms.xml',
    'sitemap.events' => '/sitemap-events.xml'
];

foreach ($sitemapRoutes as $routeName => $expectedPath) {
    try {
        $url = route($routeName);
        echo "   ✅ Route {$routeName}: {$url}\n";
    } catch (Exception $e) {
        echo "   ❌ Route {$routeName}: ERROR - " . $e->getMessage() . "\n";
    }
}

// Test 3: Check if robots.txt is updated
echo "\n3. Testing robots.txt...\n";
$robotsPath = 'public/robots.txt';
if (file_exists($robotsPath)) {
    $robotsContent = file_get_contents($robotsPath);
    
    if (strpos($robotsContent, 'Sitemap:') !== false) {
        echo "   ✅ robots.txt contains sitemap reference\n";
    } else {
        echo "   ❌ robots.txt missing sitemap reference\n";
    }
    
    if (strpos($robotsContent, 'Disallow: /admin') !== false) {
        echo "   ✅ robots.txt disallows admin areas\n";
    } else {
        echo "   ❌ robots.txt doesn't disallow admin areas\n";
    }
} else {
    echo "   ❌ robots.txt file not found\n";
}

// Test 4: Check view files
echo "\n4. Testing view files...\n";
$viewFiles = [
    'sitemaps/index.blade.php',
    'sitemaps/urlset.blade.php'
];

foreach ($viewFiles as $viewFile) {
    $viewPath = "resources/views/{$viewFile}";
    if (file_exists($viewPath)) {
        echo "   ✅ View file exists: {$viewFile}\n";
    } else {
        echo "   ❌ View file missing: {$viewFile}\n";
    }
}

// Test 5: Check controller files
echo "\n5. Testing controller files...\n";
$controllerFiles = [
    'app/Http/Controllers/SitemapController.php',
    'app/Console/Commands/GenerateSitemap.php',
    'app/Console/Commands/SeoAudit.php'
];

foreach ($controllerFiles as $controllerFile) {
    if (file_exists($controllerFile)) {
        echo "   ✅ Controller exists: " . basename($controllerFile) . "\n";
    } else {
        echo "   ❌ Controller missing: " . basename($controllerFile) . "\n";
    }
}

// Test 6: Test UKM data for SEO
echo "\n6. Testing UKM data for SEO...\n";
try {
    $ukms = \App\Models\Ukm::active()->limit(3)->get();
    
    foreach ($ukms as $ukm) {
        echo "   📋 UKM: {$ukm->name}\n";
        
        if (!empty($ukm->description)) {
            $cleanDesc = \App\Helpers\SeoHelper::cleanText($ukm->description, 100);
            echo "      ✅ Description: " . substr($cleanDesc, 0, 50) . "...\n";
        } else {
            echo "      ❌ Missing description\n";
        }
        
        if (!empty($ukm->slug)) {
            echo "      ✅ SEO-friendly slug: {$ukm->slug}\n";
        } else {
            echo "      ❌ Missing slug\n";
        }
        
        try {
            $ukmSchema = \App\Helpers\SeoHelper::getUkmSchema($ukm);
            echo "      ✅ Schema.org data generated\n";
        } catch (Exception $e) {
            echo "      ❌ Schema.org error: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Error testing UKM data: " . $e->getMessage() . "\n";
}

// Test 7: Check layout updates
echo "\n7. Testing layout updates...\n";
$layoutPath = 'resources/views/layouts/app.blade.php';
if (file_exists($layoutPath)) {
    $layoutContent = file_get_contents($layoutPath);
    
    if (strpos($layoutContent, '@yield(\'seo_title\'') !== false) {
        echo "   ✅ Layout supports seo_title\n";
    } else {
        echo "   ❌ Layout missing seo_title support\n";
    }
    
    if (strpos($layoutContent, 'canonical') !== false) {
        echo "   ✅ Layout supports canonical URLs\n";
    } else {
        echo "   ❌ Layout missing canonical URL support\n";
    }
    
    if (strpos($layoutContent, 'structured_data') !== false) {
        echo "   ✅ Layout supports structured data\n";
    } else {
        echo "   ❌ Layout missing structured data support\n";
    }
    
    if (strpos($layoutContent, 'og:image:width') !== false) {
        echo "   ✅ Layout has enhanced Open Graph tags\n";
    } else {
        echo "   ❌ Layout missing enhanced Open Graph tags\n";
    }
} else {
    echo "   ❌ Layout file not found\n";
}

echo "\n=== SEO IMPLEMENTATION TEST COMPLETE ===\n";
echo "\n📋 Next steps:\n";
echo "1. Run: php artisan sitemap:generate\n";
echo "2. Run: php artisan seo:audit\n";
echo "3. Test sitemap: http://localhost:8000/sitemap.xml\n";
echo "4. Submit sitemap to Google Search Console\n";
echo "5. Monitor SEO performance with Google Analytics\n";
?>
