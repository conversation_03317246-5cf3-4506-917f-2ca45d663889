-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 21, 2025 at 07:25 AM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.4.8

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ukmwebv`
--

-- --------------------------------------------------------

--
-- Table structure for table `attendances`
--

CREATE TABLE `attendances` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `event_id` bigint(20) UNSIGNED NOT NULL,
  `status` enum('present','absent','late','excused') NOT NULL DEFAULT 'present',
  `check_in_time` datetime NOT NULL,
  `check_out_time` datetime DEFAULT NULL,
  `check_in_method` enum('qr_code','manual','pin_code') NOT NULL DEFAULT 'qr_code',
  `qr_token` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `recorded_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `certificates`
--

CREATE TABLE `certificates` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `event_id` bigint(20) UNSIGNED NOT NULL,
  `certificate_number` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `issued_date` datetime NOT NULL,
  `issued_by` bigint(20) UNSIGNED NOT NULL,
  `verification_code` varchar(255) NOT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT 1,
  `downloaded_at` datetime DEFAULT NULL,
  `download_count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `events`
--

CREATE TABLE `events` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ukm_id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `requirements` text DEFAULT NULL,
  `poster` varchar(255) DEFAULT NULL,
  `proposal_file` varchar(255) DEFAULT NULL,
  `rab_file` varchar(255) DEFAULT NULL,
  `lpj_file` varchar(255) DEFAULT NULL,
  `gallery` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`gallery`)),
  `type` enum('workshop','seminar','competition','meeting','social','other') NOT NULL,
  `location` varchar(255) NOT NULL,
  `start_datetime` datetime NOT NULL,
  `end_datetime` datetime NOT NULL,
  `registration_start` datetime DEFAULT NULL,
  `registration_end` datetime DEFAULT NULL,
  `max_participants` int(11) DEFAULT NULL,
  `current_participants` int(11) NOT NULL DEFAULT 0,
  `registration_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('waiting','published','ongoing','completed','cancelled') DEFAULT 'waiting',
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `approval_notes` text DEFAULT NULL,
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0,
  `registration_open` tinyint(1) NOT NULL DEFAULT 1,
  `certificate_available` tinyint(1) NOT NULL DEFAULT 0,
  `certificate_template` varchar(255) DEFAULT NULL,
  `contact_person` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`contact_person`)),
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `rejected_by` bigint(20) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `cancelled_by` bigint(20) UNSIGNED DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancellation_reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `events`
--

INSERT INTO `events` (`id`, `ukm_id`, `title`, `slug`, `description`, `requirements`, `poster`, `proposal_file`, `rab_file`, `lpj_file`, `gallery`, `type`, `location`, `start_datetime`, `end_datetime`, `registration_start`, `registration_end`, `max_participants`, `current_participants`, `registration_fee`, `status`, `approved_by`, `approved_at`, `rejection_reason`, `approval_notes`, `requires_approval`, `registration_open`, `certificate_available`, `certificate_template`, `contact_person`, `notes`, `created_at`, `updated_at`, `rejected_by`, `rejected_at`, `cancelled_by`, `cancelled_at`, `cancellation_reason`) VALUES
(1, 4, 'Pengenalan sistem informasi', 'pengenalan-sistem-informasi', 'Acara', NULL, 'events/posters/9jEOqYzqRcSoHjVG9Epx2mlC1CtUapxXmwFHbufw.png', 'events/proposals/GUIUDo8HPebZUdx5LQk4FU1lXFlShPGLal77uKB0.docx', 'events/rab/ULrOdCAXoPuuEEGgc9WngyjqYWAWBEXDDrRSnYWC.docx', NULL, NULL, 'workshop', 'Kampus A', '2025-06-08 18:31:00', '2025-06-08 22:31:00', NULL, NULL, NULL, 1, 0.00, 'completed', 1, '2025-06-10 04:45:56', NULL, NULL, 0, 0, 0, 'events/certificates/FI7bhxmmLAylLrO5qqW9etMIJXsygDEu9eHzHCsK.jpg', '[]', NULL, '2025-06-10 04:45:34', '2025-06-10 05:58:58', NULL, NULL, NULL, NULL, NULL),
(2, 4, 'Sosialisasi Stuktur organisasi', 'sosialisasi-stuktur-organisasi', 'Tester date', NULL, 'events/posters/LzWrEXGvijR4XG8MAYb68w96ovaD1hdQmfqcVXAj.png', NULL, NULL, NULL, NULL, 'workshop', 'Lapangan SMK', '2025-06-10 15:43:41', '2025-06-11 14:43:41', NULL, NULL, 5, 1, 0.00, 'completed', 1, '2025-06-10 06:19:38', NULL, NULL, 0, 0, 0, 'events/certificates/7TKvR9HlGKrojkQ1kCX9pMMDvlNIcjdne6FyNWE7.jpg', '[]', NULL, '2025-06-10 06:19:11', '2025-06-11 08:43:41', NULL, NULL, NULL, NULL, NULL),
(3, 4, 'Absensi bulanan', 'absensi-bulanan', 'Rapat organisasi', NULL, 'events/posters/KJpJuQYaRS2afu8NpvxMsBhD63ib00ZEIHXnEBRF.png', NULL, NULL, NULL, NULL, 'meeting', 'Aula LT3', '2025-06-11 19:53:00', '2025-06-12 00:01:00', NULL, NULL, 20, 1, 0.00, 'completed', 1, '2025-06-11 05:55:07', NULL, NULL, 0, 0, 0, 'events/certificates/ixJqsHXi3mK2ONbz18m9wgmXxomLmZo5iFWo6wBt.jpg', '[]', NULL, '2025-06-11 05:54:34', '2025-06-12 01:45:29', NULL, NULL, NULL, NULL, NULL),
(4, 4, 'Bukber', 'bukber', 'test', NULL, 'events/posters/74cGFXU2yGmA9XtPXyAnTc2tTU85FjQus8L1BgWK.png', 'events/proposals/mS1rf9qwE3FKmlFbzrleRPdtuom8rwtJvJw0JQb2.docx', 'events/rab/bHUPKFhDn7IPooHRVbmgF6yOUDa1bVC6iYvlvy4r.docx', 'events/lpj/RmKuySGNGmxn0HahMUIBKUEltq6iSDUMBjz4G4xX.docx', NULL, 'meeting', 'Kampus C', '2025-06-03 21:54:00', '2025-06-04 21:55:00', NULL, NULL, NULL, 1, 0.00, 'completed', 1, '2025-06-11 07:54:47', NULL, NULL, 0, 0, 0, 'events/certificates/VkowMRHns9Rhj8aclYUyFhB3xZl1PzM626wmaO26.png', '[]', NULL, '2025-06-11 07:54:33', '2025-06-16 20:21:44', NULL, NULL, NULL, NULL, NULL),
(5, 4, 'Rapat Ormawa', 'rapat-ormawa', 'Test', NULL, 'events/posters/lQdUkxK9qPChzyMQAwXfNsUcUaLtUgBTpSltIBsI.png', NULL, NULL, NULL, NULL, 'seminar', 'Online', '2025-06-07 12:46:00', '2025-06-08 13:46:00', '2025-06-04 12:51:00', '2025-06-06 12:51:00', 200, 1, 15000.00, 'completed', 1, '2025-06-11 22:47:04', NULL, NULL, 1, 0, 1, 'events/certificates/PwRwNtk5h7v9XuIeEjZz9rLSLzXRYZTciXTYzUl8.jpg', '[]', NULL, '2025-06-11 22:46:43', '2025-06-12 01:44:12', NULL, NULL, NULL, NULL, NULL),
(8, 4, 'Test event date', 'test-event-date', 'test', NULL, 'events/posters/dL8MewAZ3SamdpPaxiDwUpo9rv6pZThWroMcEGkM.png', NULL, NULL, NULL, NULL, 'meeting', 'Zoom', '2025-06-13 15:05:00', '2025-06-13 15:10:00', '2025-06-09 09:05:00', '2025-06-13 08:05:00', 90, 1, 0.00, 'completed', 1, '2025-06-12 01:08:22', NULL, NULL, 1, 0, 1, 'events/certificates/epwjvpD0Yj93CGUE5Eg4yqDgoPwLezdk1lzw8dAl.jpg', '[]', NULL, '2025-06-12 01:07:29', '2025-06-16 04:01:17', NULL, NULL, NULL, NULL, NULL),
(9, 4, 'Sertif Tambahan', 'sertif-tambahan', 'Test Sertif', NULL, 'events/posters/caoGgFg8ijyrH0IXOY6nAJp7IG1p1JMlhUm4GENb.png', NULL, NULL, NULL, NULL, 'social', 'Online', '2025-06-04 16:10:00', '2025-06-05 18:10:00', '2025-06-01 09:10:00', '2025-06-02 16:17:00', 22, 1, 0.00, 'completed', 1, '2025-06-12 02:11:27', NULL, NULL, 1, 0, 1, 'events/certificates/zREJMSFDZ3XmizYHtkqKlWoBTgqJXa1Lvww8LlMg.png', '[]', NULL, '2025-06-12 02:11:09', '2025-06-12 02:19:33', NULL, NULL, NULL, NULL, NULL),
(10, 4, 'Test new pdf', 'test-new-pdf', 'tester', NULL, 'events/posters/Qndq5RphFIiSfciySzQLdPfv038TlANwz6DdiNTl.png', NULL, NULL, NULL, NULL, 'other', 'Tester', '2025-06-14 18:00:00', '2025-06-15 18:00:00', '2025-06-11 11:00:00', '2025-06-14 11:00:00', 2, 1, 0.00, 'completed', NULL, NULL, NULL, NULL, 0, 0, 0, 'events/certificates/LIzyTVoMhh5TXR0IQUY9Y2zhv2rFvrPXipPrawp6.png', NULL, NULL, '2025-06-12 04:01:35', '2025-06-16 04:01:17', NULL, NULL, NULL, NULL, NULL),
(11, 4, 'tester', 'tester', '111', NULL, 'events/posters/GZzDwAu3E489V2KirYqt7sy3NJnEKCBJD9yal1tO.jpg', NULL, NULL, NULL, NULL, 'workshop', 'test', '2025-06-16 18:56:00', '2025-06-18 18:56:00', '2025-06-04 11:56:00', '2025-06-16 11:56:00', 22, 0, 0.00, 'completed', 1, '2025-06-16 04:58:31', NULL, NULL, 0, 0, 0, 'events/certificates/NhQz0KFNjlvjaEV92wFBVz3Ow3sZrvIhKjhz4ZDb.png', NULL, NULL, '2025-06-16 04:57:49', '2025-06-19 00:51:02', NULL, NULL, NULL, NULL, NULL),
(12, 4, 'Workshop Web Development', 'workshop-web-development', 'Workshop intensif tentang pengembangan web modern menggunakan Laravel dan Vue.js. Peserta akan belajar dari dasar hingga membuat aplikasi web yang kompleks.', NULL, 'events/posters/dBWHKGP2hYFKsnlkab11FP5JkldG9UB2TLlEjhMq.jpg', NULL, NULL, NULL, NULL, 'workshop', 'Lab Komputer 1, Gedung Tokong Nanas', '2025-06-10 09:00:00', '2025-06-14 17:00:00', '2025-06-09 13:53:00', '2025-06-09 15:05:00', 30, 2, 0.00, 'completed', NULL, NULL, NULL, NULL, 0, 0, 0, 'events/certificates/s3D9l9ZewndxlO6st836CGodobDIYTykJGMiCYjO.png', NULL, NULL, '2025-06-16 06:53:28', '2025-06-17 01:05:54', NULL, NULL, NULL, NULL, NULL),
(13, 4, 'workshop', 'workshop', 'rwajaura', NULL, 'events/posters/2oTR9OBVC3MtYXtKpsL9EkBM7lWCl8mNvxOZBz8B.png', 'events/proposals/ufv70YwSyq0RGxIMl7EhiagLG1NMyhaElnNt5S70.docx', 'events/rab/RmKT05AI5IMgrDLtueqv6UAxiEQq0WUmBo9aFgwz.docx', NULL, NULL, 'workshop', 'Kamous C', '2025-06-18 14:52:00', '2025-06-18 16:52:00', '2025-06-06 07:52:00', '2025-06-17 07:52:00', 33, 0, 0.00, 'completed', 1, '2025-06-17 01:01:26', NULL, NULL, 1, 0, 1, 'events/certificates/nA3PSfLkyFrL5ZCuU4LvYpgkfzEWBCwASMNY90Lv.png', '[]', NULL, '2025-06-17 00:54:04', '2025-06-19 00:51:02', NULL, NULL, NULL, NULL, NULL),
(14, 4, 'Pengabdian masyarakat', 'pengabdian-masyarakat', 'Tester kampus C', NULL, 'events/posters/7lDbDWm0IHDn25CSUzG5PVowfPOFnMNpAB25KRZb.png', 'events/proposals/8Pncd1KPSOyTSetRXJeX12GBQ2BluO0AIxwZInjo.docx', 'events/rab/i6HukYXAurN3G0eogH1bHr1g3gAcD27iKU9aF47h.docx', NULL, NULL, 'social', 'Kampus C', '2025-06-19 20:31:00', '2025-06-20 20:31:00', '2025-06-17 13:31:00', '2025-06-19 13:31:00', 8, 0, 0.00, 'ongoing', NULL, NULL, NULL, NULL, 0, 0, 0, 'events/certificates/lyKXtbd7lDKbo8LLTR6E4V0kSxV6g32eercQ55zt.png', NULL, NULL, '2025-06-18 06:32:33', '2025-06-19 23:19:12', NULL, NULL, NULL, NULL, NULL),
(15, 4, 'Tester1111', 'tester1111', 'deded', NULL, 'events/posters/FG4R4bAZAityMuAz2BNR7NRBkLNFV3Ea60eGMKZN.jpg', 'events/proposals/UGWHsoeOvCXOux0oGrnLmdvIKj7pffRuqRbbe3xU.docx', 'events/rab/oJIdYx1GfZwGTBuI3C9pTPCbuEDJPc2Zav5uju7N.docx', NULL, NULL, 'social', 'Online', '2025-06-21 16:11:00', '2025-06-22 16:11:00', '2025-06-17 09:11:00', '2025-06-21 09:11:00', 2, 1, 0.00, 'published', 1, '2025-06-19 02:23:12', NULL, NULL, 1, 0, 1, 'events/certificates/u3sQsihThCRPsZw5V5nVzFOqdEuwPWn6NcKgXRgT.png', '[]', NULL, '2025-06-19 02:13:18', '2025-06-19 02:36:29', NULL, NULL, NULL, NULL, NULL),
(16, 4, 'tesster222', 'tesster222', 'da', NULL, 'events/posters/hBPgWFl9yYQOOYrMLnAdtCo6yTsdJZwbgzflCv5S.jpg', 'events/proposals/1ANowgDs2nt2UlW3gUKDhialfGgLtLQGJWnkeCCs.docx', 'events/rab/bI3wLZrFLyCWZfPDpi7TxvoKNGhd8JdH7qTsO200.docx', NULL, NULL, 'seminar', 'Online', '2025-06-13 16:35:00', '2025-06-13 19:35:00', '2025-06-03 09:35:00', '2025-06-11 09:35:00', 2, 2, 0.00, 'completed', 1, '2025-06-19 02:38:17', NULL, NULL, 1, 0, 1, 'events/certificates/eSMFwJpzMF2iSRNGz6ACgiCTiYIjSCiuPJwccsPp.png', '[]', NULL, '2025-06-19 02:37:44', '2025-06-19 03:44:48', NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `event_attendances`
--

CREATE TABLE `event_attendances` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `event_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `event_registration_id` bigint(20) UNSIGNED NOT NULL,
  `status` enum('present','absent','pending') NOT NULL DEFAULT 'pending',
  `proof_file` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `submitted_at` timestamp NULL DEFAULT NULL,
  `verification_status` enum('pending','verified','rejected') NOT NULL DEFAULT 'pending',
  `verified_by` bigint(20) UNSIGNED DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `verification_notes` text DEFAULT NULL,
  `certificate_generated` tinyint(1) NOT NULL DEFAULT 0,
  `certificate_file` varchar(255) DEFAULT NULL,
  `certificate_downloaded_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `event_attendances`
--

INSERT INTO `event_attendances` (`id`, `event_id`, `user_id`, `event_registration_id`, `status`, `proof_file`, `notes`, `submitted_at`, `verification_status`, `verified_by`, `verified_at`, `verification_notes`, `certificate_generated`, `certificate_file`, `certificate_downloaded_at`, `created_at`, `updated_at`) VALUES
(4, 4, 3, 7, 'present', 'attendances/proofs/XaYbJt71B1lycHICVDgbY3O6Ovnri1AgKtjPUnNp.png', ';efrfnljfklrn.afa.nlflewfknfwkk.maefmwf;fnwLMW', '2025-06-11 07:56:35', 'verified', 4, '2025-06-12 01:42:31', NULL, 1, 'certificates/bukber__1749721477.pdf', '2025-06-12 03:58:48', '2025-06-11 07:56:13', '2025-06-12 03:58:48'),
(5, 3, 3, 6, 'present', 'attendances/proofs/uHSzCzKuX0zZfo4pRxHIQ7hAYlvIZXBaj4nfb46y.png', 'wowo ga rispek banget', '2025-06-12 01:13:45', 'verified', 4, '2025-06-12 01:34:02', NULL, 1, 'certificates/absensi-bulanan__1749717272.pdf', '2025-06-12 02:08:20', '2025-06-11 22:58:04', '2025-06-12 02:08:20'),
(6, 1, 3, 10, 'present', NULL, 'Test attendance - should show verify buttons', '2025-06-12 01:26:44', 'verified', 4, '2025-06-12 01:42:51', NULL, 1, 'certificates/pengenalan-sistem-informasi__1749724945.pdf', '2025-06-12 03:42:26', '2025-06-12 01:26:44', '2025-06-12 03:42:26'),
(7, 1, 11, 11, 'present', NULL, 'Test attendance - already verified', '2025-06-12 01:26:44', 'verified', 1, '2025-06-12 01:26:44', NULL, 0, NULL, NULL, '2025-06-12 01:26:44', '2025-06-12 01:26:44'),
(8, 9, 3, 12, 'present', 'attendances/proofs/8ijsQj3oFAn211h0UhkuzmxnQJYBKbODWlkAP1uE.png', 'ascac', '2025-06-12 02:19:43', 'verified', 4, '2025-06-12 02:20:22', NULL, 1, 'certificates/sertif-tambahan__1749725478.pdf', '2025-06-12 03:46:19', '2025-06-12 02:19:34', '2025-06-12 03:51:19'),
(9, 12, 14, 15, 'present', 'attendances/proofs/eV5hDvX6CLjD0NExMjuY62emOJEYnoyinybXcUEj.jpg', 'FUYUJGJGK', '2025-06-17 01:06:50', 'verified', 4, '2025-06-17 01:07:31', NULL, 1, 'certificates/workshop-web-development__1750147671.pdf', '2025-06-17 01:07:57', '2025-06-17 01:06:32', '2025-06-17 01:07:57'),
(10, 16, 3, 18, 'present', 'attendances/proofs/JjTLe5p6nlqbLpVmocpwmPjMdDwKAsyxnbG9IF16.png', 'wtw', '2025-06-19 03:45:47', 'verified', 4, '2025-06-19 03:46:17', NULL, 0, NULL, '2025-06-19 04:12:44', '2025-06-19 03:45:31', '2025-06-19 04:12:44');

-- --------------------------------------------------------

--
-- Table structure for table `event_registrations`
--

CREATE TABLE `event_registrations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `event_id` bigint(20) UNSIGNED NOT NULL,
  `status` enum('pending','approved','rejected','cancelled') NOT NULL DEFAULT 'pending',
  `availability_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`availability_form`)),
  `registration_notes` text DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancellation_reason` text DEFAULT NULL,
  `motivation` text DEFAULT NULL,
  `additional_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`additional_data`)),
  `payment_proof` varchar(255) DEFAULT NULL,
  `payment_status` enum('pending','paid','verified','refunded') NOT NULL DEFAULT 'pending',
  `approved_at` datetime DEFAULT NULL,
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `event_registrations`
--

INSERT INTO `event_registrations` (`id`, `user_id`, `event_id`, `status`, `availability_form`, `registration_notes`, `cancelled_at`, `cancellation_reason`, `motivation`, `additional_data`, `payment_proof`, `payment_status`, `approved_at`, `approved_by`, `rejection_reason`, `created_at`, `updated_at`) VALUES
(3, 3, 2, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"pribadi\",\"emergency_contact\":\"54231234354\"}', '-', NULL, NULL, '-', '[]', NULL, 'verified', '2025-06-10 13:23:22', NULL, NULL, '2025-06-10 06:23:22', '2025-06-10 06:23:22'),
(6, 3, 3, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"jalan_kaki\",\"emergency_contact\":\"3243543423\"}', 'adadada', NULL, NULL, '231', '[]', NULL, 'verified', '2025-06-11 14:48:48', NULL, NULL, '2025-06-11 07:48:48', '2025-06-11 07:48:48'),
(7, 3, 4, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"umum\",\"emergency_contact\":\"342234324\"}', 'rwreef', NULL, NULL, '432', '[]', NULL, 'verified', '2025-06-11 14:55:14', NULL, NULL, '2025-06-11 07:55:14', '2025-06-11 07:55:14'),
(8, 3, 5, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"jalan_kaki\",\"emergency_contact\":\"2345543\"}', '234we', NULL, NULL, '1', '[]', NULL, 'verified', '2025-06-12 05:48:32', NULL, NULL, '2025-06-11 22:48:32', '2025-06-11 22:48:32'),
(9, 3, 8, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"pribadi\",\"emergency_contact\":\"34354645\"}', 'hhkh', NULL, NULL, '33', '[]', NULL, 'verified', '2025-06-12 08:11:29', NULL, NULL, '2025-06-12 01:11:29', '2025-06-12 01:11:29'),
(10, 3, 1, 'approved', NULL, NULL, NULL, NULL, 'Test registration for attendance', NULL, NULL, 'pending', '2025-06-12 08:26:44', NULL, NULL, '2025-06-12 01:26:44', '2025-06-12 01:26:44'),
(11, 11, 1, 'approved', NULL, NULL, NULL, NULL, 'Test registration for attendance', NULL, NULL, 'pending', '2025-06-12 08:26:44', NULL, NULL, '2025-06-12 01:26:44', '2025-06-12 01:26:44'),
(12, 3, 9, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"pribadi\",\"emergency_contact\":\"4184931\"}', 'ekjflnk', NULL, NULL, 'dwad', '[]', NULL, 'verified', '2025-06-12 09:18:03', NULL, NULL, '2025-06-12 02:18:03', '2025-06-12 02:18:03'),
(13, 3, 10, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"jalan_kaki\",\"emergency_contact\":\"w2343532\"}', '131dewwf', NULL, NULL, 'aa', '[]', NULL, 'verified', '2025-06-12 11:02:53', NULL, NULL, '2025-06-12 04:02:53', '2025-06-12 04:02:53'),
(14, 3, 12, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"umum\",\"emergency_contact\":\"34234243\"}', 'edaede', NULL, NULL, 'dadada', '[]', NULL, 'verified', '2025-06-17 05:55:23', NULL, NULL, '2025-06-16 22:55:23', '2025-06-16 22:55:23'),
(15, 14, 12, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"jalan_kaki\",\"emergency_contact\":\"adwa\"}', 'dwad', NULL, NULL, 'jjljfgre', '[]', NULL, 'verified', '2025-06-17 08:04:39', NULL, NULL, '2025-06-17 01:04:39', '2025-06-17 01:04:39'),
(16, 11, 15, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"pribadi\",\"emergency_contact\":\"54231234354\"}', 'wdada', NULL, NULL, 'a', '[]', NULL, 'verified', '2025-06-19 09:24:07', NULL, NULL, '2025-06-19 02:24:07', '2025-06-19 02:24:07'),
(18, 3, 16, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"pribadi\",\"emergency_contact\":\"2123435\"}', 'afa', NULL, NULL, 'awd', '[]', NULL, 'verified', '2025-06-19 09:38:48', NULL, NULL, '2025-06-19 02:38:48', '2025-06-19 02:38:48'),
(19, 12, 16, 'approved', '{\"full_attendance\":\"yes\",\"partial_reason\":null,\"transportation\":\"umum\",\"emergency_contact\":\"d233\"}', '32', NULL, NULL, 'dawd', '[]', NULL, 'verified', '2025-06-19 09:39:36', NULL, NULL, '2025-06-19 02:39:36', '2025-06-19 02:39:36');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2024_01_02_000001_create_ukms_table', 1),
(5, '2024_01_02_000002_create_ukm_members_table', 1),
(6, '2024_01_02_000003_create_events_table', 1),
(7, '2024_01_02_000004_create_event_registrations_table', 1),
(8, '2024_01_02_000005_create_attendances_table', 1),
(9, '2024_01_02_000006_create_certificates_table', 1),
(10, '2024_01_02_000007_add_registration_open_to_events_table', 1),
(11, '2024_06_08_140000_create_notifications_table', 1),
(12, '2024_06_08_160000_add_event_management_fields', 1),
(13, '2024_06_08_170000_create_event_attendances_table', 1),
(14, '2025_06_09_131507_ensure_event_approval_columns_exist', 2),
(15, '2024_12_19_000001_add_leader_id_to_ukms_table', 3),
(16, '2025_05_26_133735_add_role_to_users_table', 4),
(17, '2025_05_26_134429_create_permission_tables', 4),
(18, '2025_05_29_060039_update_user_role_enum_add_ketua_ukm', 4),
(19, '2025_05_30_104125_add_registration_open_to_events_table', 4),
(20, '2025_06_05_042358_add_missing_fields_to_ukms_table', 4),
(21, '2025_06_05_044803_add_approval_fields_to_events_table', 4),
(22, '2025_06_07_120000_add_registration_fields_to_ukm_members_table', 4),
(23, '2025_06_09_081825_add_event_approval_tracking_fields', 4),
(24, '2025_06_09_091302_remove_redundant_approval_status_from_events', 4),
(25, '2025_06_09_143237_insert_admin_users', 4),
(26, '2025_06_10_091614_fix_users_role_column_safely', 4),
(27, '2025_06_10_093800_fix_users_status_enum', 5),
(28, '2025_06_10_094052_fix_users_status_enum', 5),
(29, '2025_06_10_105800_add_achievements_and_organization_structure_to_ukms', 6),
(30, '2025_06_18_161453_create_ukm_achievements_table', 7),
(31, '2025_06_18_184309_update_event_status_draft_to_waiting', 7);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(1, 'App\\Models\\User', 2),
(2, 'App\\Models\\User', 3),
(2, 'App\\Models\\User', 11),
(3, 'App\\Models\\User', 4),
(3, 'App\\Models\\User', 13);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `type`, `title`, `message`, `data`, `read_at`, `created_at`, `updated_at`) VALUES
(1, 3, 'ukm_application_approved', 'Pendaftaran UKM Diterima', 'Selamat! Pendaftaran Anda ke UKM Sistem informasi telah diterima. Selamat bergabung!', '{\"ukm_name\":\"Sistem informasi\",\"action\":\"approved\"}', '2025-06-11 22:47:33', '2025-06-11 22:39:02', '2025-06-11 22:47:33'),
(2, 14, 'ukm_application_approved', 'Pendaftaran UKM Diterima', 'Selamat! Pendaftaran Anda ke UKM Sistem informasi telah diterima. Selamat bergabung!', '{\"ukm_name\":\"Sistem informasi\",\"action\":\"approved\"}', NULL, '2025-06-17 00:51:10', '2025-06-17 00:51:10');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'view_dashboard', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(2, 'join_ukm', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(3, 'register_event', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(4, 'view_profile', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(5, 'edit_profile', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(6, 'manage_ukm', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(7, 'edit_ukm', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(8, 'create_event', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(9, 'manage_ukm_members', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(10, 'view_ukm_dashboard', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(11, 'manage_users', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(12, 'manage_all_ukms', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(13, 'manage_all_events', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(14, 'view_admin_dashboard', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(15, 'approve_registrations', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05');

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(2, 'student', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05'),
(3, 'ketua_ukm', 'web', '2025-06-10 02:31:05', '2025-06-10 02:31:05');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(1, 1),
(1, 2),
(1, 3),
(2, 1),
(2, 2),
(2, 3),
(3, 1),
(3, 2),
(3, 3),
(4, 1),
(4, 2),
(4, 3),
(5, 1),
(5, 2),
(5, 3),
(6, 1),
(6, 3),
(7, 1),
(7, 3),
(8, 1),
(8, 3),
(9, 1),
(9, 3),
(10, 1),
(10, 3),
(11, 1),
(12, 1),
(13, 1),
(14, 1),
(15, 1);

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ukms`
--

CREATE TABLE `ukms` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `vision` text DEFAULT NULL,
  `mission` text DEFAULT NULL,
  `category` enum('academic','sports','arts','religion','social','technology','entrepreneurship','other') NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `banner` varchar(255) DEFAULT NULL,
  `contact_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`contact_info`)),
  `meeting_schedule` text DEFAULT NULL,
  `meeting_location` varchar(255) DEFAULT NULL,
  `requirements` text DEFAULT NULL,
  `achievements` text DEFAULT NULL COMMENT 'Prestasi UKM',
  `organization_structure` varchar(255) DEFAULT NULL COMMENT 'Gambar struktur organisasi',
  `max_members` int(11) NOT NULL DEFAULT 100,
  `current_members` int(11) NOT NULL DEFAULT 0,
  `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
  `registration_status` enum('open','closed') NOT NULL DEFAULT 'open',
  `is_recruiting` tinyint(1) NOT NULL DEFAULT 1,
  `established_date` date DEFAULT NULL,
  `leader_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `ukms`
--

INSERT INTO `ukms` (`id`, `name`, `slug`, `description`, `vision`, `mission`, `category`, `logo`, `banner`, `contact_info`, `meeting_schedule`, `meeting_location`, `requirements`, `achievements`, `organization_structure`, `max_members`, `current_members`, `status`, `registration_status`, `is_recruiting`, `established_date`, `leader_id`, `created_at`, `updated_at`) VALUES
(4, 'Sistem informasi', 'sistem-informasi', 'UKM Sistem informasi', 'Menjadi UKM', 'Menampung Mahasiswa berbakat', 'academic', 'ukms/logos/JKaQxZbASUrxc5hXroD9538ebLnKUQKjCBHiDwo8.jpg', NULL, '\"[]\"', NULL, NULL, NULL, '-', 'ukms/organization_structures/9oxjvot1zAsi0AEDJIHPbN35keZJsOu6Owo3e56H.png', 252, 2, 'active', 'open', 1, NULL, 4, '2025-06-10 04:18:55', '2025-06-17 00:51:10'),
(5, 'UKM IMMA', 'ukm-imma', 'Imma ikatan muslim muslimah', 'manjadi ukm', 'imma', 'religion', 'ukms/logos/jfqdjyWHepxXwHzDVQfGEyyHSOmj11b2F5VLWvjA.jpg', NULL, '\"[]\"', NULL, NULL, NULL, 'adawdad', 'ukms/organization_structures/eUqGuhyOHnfC3JQyaHbPqI3dRIsSAmYoSJEMzSZk.png', 50, 0, 'active', 'open', 1, NULL, 13, '2025-06-17 00:20:17', '2025-06-17 00:31:12');

-- --------------------------------------------------------

--
-- Table structure for table `ukm_achievements`
--

CREATE TABLE `ukm_achievements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ukm_id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `type` enum('competition','award','certification','recognition','other') NOT NULL DEFAULT 'competition',
  `level` enum('local','regional','national','international') NOT NULL DEFAULT 'local',
  `organizer` varchar(255) DEFAULT NULL,
  `achievement_date` date NOT NULL,
  `year` year(4) NOT NULL,
  `certificate_file` varchar(255) DEFAULT NULL,
  `participants` text DEFAULT NULL,
  `position` int(11) DEFAULT NULL,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ukm_members`
--

CREATE TABLE `ukm_members` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `ukm_id` bigint(20) UNSIGNED NOT NULL,
  `role` enum('member','coordinator','vice_leader','leader') NOT NULL DEFAULT 'member',
  `status` enum('pending','active','inactive','alumni') NOT NULL DEFAULT 'pending',
  `previous_experience` text DEFAULT NULL,
  `skills_interests` text DEFAULT NULL,
  `reason_joining` text DEFAULT NULL,
  `preferred_division` varchar(255) DEFAULT NULL,
  `cv_file` varchar(255) DEFAULT NULL,
  `applied_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `rejected_by` bigint(20) UNSIGNED DEFAULT NULL,
  `joined_date` date DEFAULT NULL,
  `left_date` date DEFAULT NULL,
  `reason_for_leaving` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `ukm_members`
--

INSERT INTO `ukm_members` (`id`, `user_id`, `ukm_id`, `role`, `status`, `previous_experience`, `skills_interests`, `reason_joining`, `preferred_division`, `cv_file`, `applied_at`, `approved_at`, `rejected_at`, `rejection_reason`, `approved_by`, `rejected_by`, `joined_date`, `left_date`, `reason_for_leaving`, `notes`, `created_at`, `updated_at`) VALUES
(1, 3, 4, 'member', 'active', '--', '-', '-', 'marketing', 'ukm_registrations/cvs/J0YSXSxBGx8P4IFLIjYf3u98UsU9ZE3KTAKy3GM2.docx', '2025-06-11 22:38:31', '2025-06-11 22:39:01', NULL, NULL, 4, NULL, '2025-06-12', NULL, NULL, NULL, '2025-06-11 22:38:31', '2025-06-11 22:39:01'),
(2, 14, 4, 'member', 'active', 'jhhghf', 'yugkygyu', 'ygiug', 'other', 'ukm_registrations/cvs/e3YVzA4zBUL4B1XXQJV4fspDs2zH4khGhpiosSZa.docx', '2025-06-17 00:48:26', '2025-06-17 00:51:10', NULL, NULL, 4, NULL, '2025-06-17', NULL, NULL, NULL, '2025-06-17 00:48:26', '2025-06-17 00:51:10'),
(3, 11, 5, 'member', 'pending', 'w', 'w', 'w', 'other', 'ukm_registrations/cvs/MoKlJppZN2BqqDy5nNrZASvmcirqsOXkLzrgVUVW.pdf', '2025-06-19 23:18:59', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-19 23:18:59', '2025-06-19 23:18:59');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nim` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `faculty` varchar(255) DEFAULT NULL,
  `major` varchar(255) DEFAULT NULL,
  `batch` varchar(4) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive','graduated','pending','suspended') DEFAULT 'pending',
  `role` enum('admin','student','ketua_ukm') DEFAULT 'student',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `nim`, `name`, `email`, `email_verified_at`, `password`, `phone`, `gender`, `faculty`, `major`, `batch`, `bio`, `avatar`, `status`, `role`, `last_login_at`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'ADMIN001', 'Administrator', '<EMAIL>', '2025-06-10 02:20:28', '$2y$12$eTAIrV9kXgZ8e8.TJBhSdudssvCdANJ25eaaC/YdPAZqByTglbRcu', '081234567890', 'male', 'Administrasi', 'Sistem Informasi', '2024', NULL, NULL, 'active', 'admin', NULL, NULL, '2025-06-10 02:20:28', '2025-06-10 02:20:28'),
(2, 'ADMIN002', 'Super Administrator', '<EMAIL>', '2025-06-10 02:20:29', '$2y$12$EAzGDJOFeUDUIB8B8.0u3eRhFZOuyQF1z46mL/UCqFuO3kI5VDcTy', '081234567891', 'female', 'Administrasi', 'Manajemen', '2024', NULL, NULL, 'active', 'admin', NULL, NULL, '2025-06-10 02:20:29', '2025-06-10 02:20:29'),
(3, '1201214002', 'Aufa Hafiy Andhika', '<EMAIL>', '2025-06-10 02:20:29', '$2y$12$aIter3eSXQhcb1O0xC5.hOFbRjK/s62OeOGshJfMQP/0IDR9EPm6y', '081234567892', 'male', 'Fakultas Rekayasa Industri', 'Teknik Informatika', '2021', NULL, 'avatars/6BXiTVlqd1qq0JfeEREvOcxsbse2iDxRyKMtyKdX.jpg', 'active', 'student', NULL, 's2VN0TQW1xzcpJRvFzfqVoj0D0Z2b2ePCa4GscYVfgGDVVc60xO7Dmcff1AU', '2025-06-10 02:20:29', '2025-06-19 00:50:53'),
(4, '1201214005', 'Rehan Dwiandra', '<EMAIL>', '2025-06-10 02:20:29', '$2y$12$i.bdT3ByTu7ZZiomRO5N4.t5K9nfNWEoKTaXzYpwS6NUN0vVLUP7y', '081234567893', 'male', 'Fakultas Rekayasa Industri', 'Sistem Informasi', '2021', NULL, 'avatars/UKy7UIYc17tZrdwDvneQfb5GkrVyP0LPmRdnuZ0v.jpg', 'active', 'ketua_ukm', NULL, '1ING4rghfUtx0PTijWhxxP9WhhHVGsdYEgiyrRKBqneP0R3Iw86BSsCePem5', '2025-06-10 02:20:29', '2025-06-19 00:51:33'),
(11, '1201214006', 'Ryemius Margaretha Siregar', '<EMAIL>', NULL, '$2y$12$uT41iD05Nj3zldQNIuAZT.rm0lC1qiCgRjmEO5dpZEMoSnB3HXEJa', '2345244545', 'female', 'Fakultas Ekonomi dan Bisnis', 'sistem', '2021', NULL, 'avatars/8ykvSFD4HIpMTW5IQUdddzXMe5q68y9AgwtyxM77.jpg', 'active', 'student', NULL, NULL, '2025-06-10 02:47:12', '2025-06-19 00:54:06'),
(12, '1201214025', 'Mutiara Hani Demayanti', '<EMAIL>', NULL, '$2y$12$Vpq19S1PUxNrG60GDv9IyeodqOREFsKT9j0PyKSsPlQoOz5gC1YTq', '9090232902049', 'female', 'Fakultas Rekayasa Industri', 'Sistem informasi', '2021', NULL, NULL, 'active', 'student', NULL, NULL, '2025-06-16 23:13:06', '2025-06-16 23:48:33'),
(13, '1201228077', 'Dimas Adhi Nugraha', '<EMAIL>', NULL, '$2y$12$Y/B6f2V9jWJc/PYSZIXYhOy1pVh8nX7CCzqMRavF3xz4ZQo0vacx.', '081296516912', 'male', 'Fakultas Rekayasa Industri', 'Sistem Informasi', '2022', NULL, 'avatars/OY6iCHNUTyTu8A0whmeDDbI79IcHgnt84EbvxVbW.jpg', 'active', 'ketua_ukm', NULL, NULL, '2025-06-16 23:50:00', '2025-06-17 00:30:47'),
(14, '212012121221', 'mahasiswa', '<EMAIL>', NULL, '$2y$12$rfddWVxzTAE6SXaBvDdo2uQNi2NISQZeciGjrHL5OaniFFkAchrgm', '321319123', 'male', 'Fakultas Ekonomi dan Bisnis', 'tester', '2021', NULL, NULL, 'active', 'student', NULL, NULL, '2025-06-17 00:45:11', '2025-06-17 00:46:35');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `attendances`
--
ALTER TABLE `attendances`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `attendances_user_id_event_id_unique` (`user_id`,`event_id`),
  ADD KEY `attendances_event_id_foreign` (`event_id`),
  ADD KEY `attendances_recorded_by_foreign` (`recorded_by`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `certificates`
--
ALTER TABLE `certificates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `certificates_user_id_event_id_unique` (`user_id`,`event_id`),
  ADD UNIQUE KEY `certificates_certificate_number_unique` (`certificate_number`),
  ADD UNIQUE KEY `certificates_verification_code_unique` (`verification_code`),
  ADD KEY `certificates_event_id_foreign` (`event_id`),
  ADD KEY `certificates_issued_by_foreign` (`issued_by`);

--
-- Indexes for table `events`
--
ALTER TABLE `events`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `events_slug_unique` (`slug`),
  ADD KEY `events_ukm_id_foreign` (`ukm_id`),
  ADD KEY `events_approved_by_foreign` (`approved_by`),
  ADD KEY `events_rejected_by_foreign` (`rejected_by`),
  ADD KEY `events_cancelled_by_foreign` (`cancelled_by`);

--
-- Indexes for table `event_attendances`
--
ALTER TABLE `event_attendances`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `event_attendances_event_id_user_id_unique` (`event_id`,`user_id`),
  ADD KEY `event_attendances_user_id_foreign` (`user_id`),
  ADD KEY `event_attendances_event_registration_id_foreign` (`event_registration_id`),
  ADD KEY `event_attendances_verified_by_foreign` (`verified_by`),
  ADD KEY `event_attendances_event_id_status_index` (`event_id`,`status`),
  ADD KEY `event_attendances_verification_status_index` (`verification_status`);

--
-- Indexes for table `event_registrations`
--
ALTER TABLE `event_registrations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `event_registrations_user_id_event_id_unique` (`user_id`,`event_id`),
  ADD KEY `event_registrations_event_id_foreign` (`event_id`),
  ADD KEY `event_registrations_approved_by_foreign` (`approved_by`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_user_id_read_at_index` (`user_id`,`read_at`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `ukms`
--
ALTER TABLE `ukms`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `ukms_slug_unique` (`slug`),
  ADD KEY `ukms_leader_id_foreign` (`leader_id`);

--
-- Indexes for table `ukm_achievements`
--
ALTER TABLE `ukm_achievements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ukm_achievements_ukm_id_year_index` (`ukm_id`,`year`),
  ADD KEY `ukm_achievements_is_featured_achievement_date_index` (`is_featured`,`achievement_date`);

--
-- Indexes for table `ukm_members`
--
ALTER TABLE `ukm_members`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `ukm_members_user_id_ukm_id_unique` (`user_id`,`ukm_id`),
  ADD KEY `ukm_members_ukm_id_foreign` (`ukm_id`),
  ADD KEY `ukm_members_approved_by_foreign` (`approved_by`),
  ADD KEY `ukm_members_rejected_by_foreign` (`rejected_by`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_nim_unique` (`nim`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `attendances`
--
ALTER TABLE `attendances`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `certificates`
--
ALTER TABLE `certificates`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `events`
--
ALTER TABLE `events`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `event_attendances`
--
ALTER TABLE `event_attendances`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `event_registrations`
--
ALTER TABLE `event_registrations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `ukms`
--
ALTER TABLE `ukms`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `ukm_achievements`
--
ALTER TABLE `ukm_achievements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ukm_members`
--
ALTER TABLE `ukm_members`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `attendances`
--
ALTER TABLE `attendances`
  ADD CONSTRAINT `attendances_event_id_foreign` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `attendances_recorded_by_foreign` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `attendances_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `certificates`
--
ALTER TABLE `certificates`
  ADD CONSTRAINT `certificates_event_id_foreign` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `certificates_issued_by_foreign` FOREIGN KEY (`issued_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `certificates_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `events`
--
ALTER TABLE `events`
  ADD CONSTRAINT `events_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `events_cancelled_by_foreign` FOREIGN KEY (`cancelled_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `events_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `events_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukms` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `event_attendances`
--
ALTER TABLE `event_attendances`
  ADD CONSTRAINT `event_attendances_event_id_foreign` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `event_attendances_event_registration_id_foreign` FOREIGN KEY (`event_registration_id`) REFERENCES `event_registrations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `event_attendances_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `event_attendances_verified_by_foreign` FOREIGN KEY (`verified_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `event_registrations`
--
ALTER TABLE `event_registrations`
  ADD CONSTRAINT `event_registrations_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `event_registrations_event_id_foreign` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `event_registrations_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `ukms`
--
ALTER TABLE `ukms`
  ADD CONSTRAINT `ukms_leader_id_foreign` FOREIGN KEY (`leader_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `ukm_achievements`
--
ALTER TABLE `ukm_achievements`
  ADD CONSTRAINT `ukm_achievements_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukms` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `ukm_members`
--
ALTER TABLE `ukm_members`
  ADD CONSTRAINT `ukm_members_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `ukm_members_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `ukm_members_ukm_id_foreign` FOREIGN KEY (`ukm_id`) REFERENCES `ukms` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `ukm_members_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
