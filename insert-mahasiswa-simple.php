<?php
echo "=== CREATING MAHASISWA USERS (SIMPLE) ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Password hash for 'pass123123'
    $passwordHash = password_hash('pass123123', PASSWORD_DEFAULT);
    
    // List mahasiswa
    $mahasiswaList = [
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON>it Kurniawan',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>'
    ];
    
    $sql = "INSERT INTO users (name, email, nim, password, role, status, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, 'mahasiswa', 'active', NOW(), NOW(), NOW())";
    $stmt = $pdo->prepare($sql);
    
    $created = 0;
    $failed = 0;
    
    echo "Creating mahasiswa accounts...\n\n";
    
    foreach ($mahasiswaList as $index => $name) {
        try {
            // Generate email from name
            $email = strtolower(str_replace(' ', '.', $name)) . '@student.telkomuniversity.ac.id';
            
            // Generate NIM
            $nim = '*********' . str_pad($index + 1, 2, '0', STR_PAD_LEFT);
            
            // Check if user already exists
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $checkStmt->execute([$email]);
            if ($checkStmt->fetchColumn() > 0) {
                echo "   ⚠️  User already exists: {$name} ({$email})\n";
                continue;
            }
            
            // Insert user
            if ($stmt->execute([$name, $email, $nim, $passwordHash])) {
                echo "   ✅ Created: {$name}\n";
                echo "      📧 Email: {$email}\n";
                echo "      🆔 NIM: {$nim}\n";
                echo "      🔑 Password: pass123123\n\n";
                $created++;
            } else {
                echo "   ❌ Failed to create: {$name}\n\n";
                $failed++;
            }
            
        } catch (Exception $e) {
            echo "   ❌ Error creating {$name}: " . $e->getMessage() . "\n\n";
            $failed++;
        }
    }
    
    echo "=== RESULT ===\n";
    echo "✅ Successfully created: {$created} users\n";
    echo "❌ Failed: {$failed} users\n";
    
    // Show summary
    echo "\n📊 User Summary:\n";
    $result = $pdo->query("SELECT COUNT(*) as total FROM users");
    $totalUsers = $result->fetch(PDO::FETCH_ASSOC)['total'];
    
    $result = $pdo->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    echo "   Total Users: {$totalUsers}\n";
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   {$row['role']}: {$row['count']}\n";
    }
    
    echo "\n📋 Login Credentials:\n";
    echo "   Password for all mahasiswa: pass123123\n";
    echo "   Login URL: http://localhost:8000/login\n";
    
    echo "\n📱 Sample Login:\n";
    $result = $pdo->query("SELECT email FROM users WHERE role = 'mahasiswa' LIMIT 1");
    $sampleUser = $result->fetch(PDO::FETCH_ASSOC);
    if ($sampleUser) {
        echo "   Email: {$sampleUser['email']}\n";
        echo "   Password: pass123123\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== COMPLETE ===\n";
?>
