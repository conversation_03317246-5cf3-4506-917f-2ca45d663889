<?php
require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== CREATING MAHASISWA USERS ===\n\n";

use App\Models\User;
use Illuminate\Support\Facades\Hash;

// List mahasiswa
$mahasiswaList = [
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>'
];

$password = 'pass123123';
$created = 0;
$failed = 0;

echo "Creating mahasiswa accounts...\n\n";

foreach ($mahasiswaList as $index => $name) {
    try {
        // Generate email from name
        $email = strtolower(str_replace(' ', '.', $name)) . '@student.telkomuniversity.ac.id';
        
        // Generate NIM (example format: *********0 + index)
        $nim = '*********' . str_pad($index + 1, 2, '0', STR_PAD_LEFT);
        
        // Check if user already exists
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            echo "   ⚠️  User already exists: {$name} ({$email})\n";
            continue;
        }
        
        // Create user
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'nim' => $nim,
            'password' => Hash::make($password),
            'role' => 'mahasiswa',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        
        echo "   ✅ Created: {$name}\n";
        echo "      📧 Email: {$email}\n";
        echo "      🆔 NIM: {$nim}\n";
        echo "      🔑 Password: {$password}\n\n";
        
        $created++;
        
    } catch (Exception $e) {
        echo "   ❌ Failed to create {$name}: " . $e->getMessage() . "\n\n";
        $failed++;
    }
}

echo "=== RESULT ===\n";
echo "✅ Successfully created: {$created} users\n";
echo "❌ Failed: {$failed} users\n";

// Show summary
echo "\n📊 User Summary:\n";
$totalUsers = User::count();
$mahasiswaCount = User::where('role', 'mahasiswa')->count();
$adminCount = User::where('role', 'admin')->count();

echo "   Total Users: {$totalUsers}\n";
echo "   Mahasiswa: {$mahasiswaCount}\n";
echo "   Admin: {$adminCount}\n";

echo "\n📋 Login Credentials:\n";
echo "   Password for all mahasiswa: {$password}\n";
echo "   Login URL: http://localhost:8000/login\n";

echo "\n📱 Sample Login:\n";
if ($created > 0) {
    $sampleUser = User::where('role', 'mahasiswa')->first();
    if ($sampleUser) {
        echo "   Email: {$sampleUser->email}\n";
        echo "   Password: {$password}\n";
    }
}

echo "\n=== COMPLETE ===\n";
?>
