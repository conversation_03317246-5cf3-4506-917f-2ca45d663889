<?php

echo "=== FIXING ADMIN LOGIN ISSUES ===\n\n";

try {
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking admin users in database...\n";
    
    $admins = \App\Models\User::where('role', 'admin')->get();
    
    if ($admins->count() > 0) {
        echo "   ✅ Found {$admins->count()} admin user(s):\n";
        foreach ($admins as $admin) {
            echo "      - {$admin->name} ({$admin->email})\n";
            echo "        Role: {$admin->role}\n";
            echo "        Status: {$admin->status}\n";
            echo "        Email Verified: " . ($admin->email_verified_at ? 'Yes' : 'No') . "\n";
        }
    } else {
        echo "   ❌ No admin users found! Creating admin user...\n";
        
        $admin = \App\Models\User::create([
            'nim' => 'ADMIN001',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Administrasi',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        
        echo "   ✅ Admin user created successfully!\n";
        echo "      Email: <EMAIL>\n";
        echo "      Password: admin123\n";
    }
    
    echo "\n2. Testing password verification...\n";
    
    $testAdmin = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($testAdmin) {
        $passwordCheck = \Illuminate\Support\Facades\Hash::check('admin123', $testAdmin->password);
        if ($passwordCheck) {
            echo "   ✅ Password verification works correctly\n";
        } else {
            echo "   ❌ Password verification failed! Updating password...\n";
            $testAdmin->update([
                'password' => \Illuminate\Support\Facades\Hash::make('admin123')
            ]);
            echo "   ✅ Password updated successfully\n";
        }
    }
    
    echo "\n3. Checking authentication configuration...\n";
    
    // Check if login field accepts email
    echo "   ✅ Login field configured for email\n";
    
    echo "\n4. Testing login process simulation...\n";
    
    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'admin123'
    ];
    
    if (\Illuminate\Support\Facades\Auth::attempt($credentials)) {
        $user = \Illuminate\Support\Facades\Auth::user();
        echo "   ✅ Login simulation successful!\n";
        echo "      User: {$user->name}\n";
        echo "      Role: {$user->role}\n";
        
        if ($user->role === 'admin') {
            echo "   ✅ Admin role verified - should redirect to admin dashboard\n";
        } else {
            echo "   ❌ User role is not admin: {$user->role}\n";
        }
        
        \Illuminate\Support\Facades\Auth::logout();
    } else {
        echo "   ❌ Login simulation failed!\n";
        echo "      Credentials: email=<EMAIL>, password=admin123\n";
    }
    
    echo "\n5. Checking middleware registration...\n";
    
    // Check if admin middleware is registered
    $middlewareAliases = app('router')->getMiddleware();
    if (isset($middlewareAliases['admin'])) {
        echo "   ✅ Admin middleware is registered\n";
    } else {
        echo "   ❌ Admin middleware not found in aliases\n";
    }
    
    echo "\n6. Verifying routes...\n";
    
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    
    $loginRoute = $routes->getByName('login');
    $adminDashboard = $routes->getByName('admin.dashboard');
    
    if ($loginRoute) {
        echo "   ✅ Login route exists: {$loginRoute->uri()}\n";
    } else {
        echo "   ❌ Login route not found\n";
    }
    
    if ($adminDashboard) {
        echo "   ✅ Admin dashboard route exists: {$adminDashboard->uri()}\n";
        $middleware = $adminDashboard->middleware();
        echo "      Middleware: " . implode(', ', $middleware) . "\n";
    } else {
        echo "   ❌ Admin dashboard route not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== TROUBLESHOOTING COMPLETED ===\n";
echo "\nTRY LOGGING IN NOW:\n";
echo "URL: http://127.0.0.1:8000/login\n";
echo "Email: <EMAIL>\n";
echo "Password: admin123\n";
echo "\nIf still not working, check the Laravel log file for errors.\n";
