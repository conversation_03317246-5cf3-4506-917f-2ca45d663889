<?php

echo "=== TESTING KETUA UKM CONTROLLER ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing controller instantiation...\n";
    
    // Test if controller can be instantiated
    $controller = new \App\Http\Controllers\KetuaUkmController();
    echo "   ✅ KetuaUkmController instantiated successfully\n";
    
    echo "2. Testing User model methods...\n";
    
    // Test isKetuaUkm method
    $ketuaUkm = \App\Models\User::where('role', 'ketua_ukm')->first();
    if ($ketuaUkm) {
        echo "   Testing with user: {$ketuaUkm->name}\n";
        echo "   isKetuaUkm(): " . ($ketuaUkm->isKetuaUkm() ? 'true' : 'false') . "\n";
        echo "   ✅ isKetuaUkm method works\n";
    } else {
        echo "   ⚠️  No ketua UKM user found for testing\n";
    }
    
    echo "3. Testing routes...\n";
    
    $routes = [
        'ketua-ukm.dashboard',
        'ketua-ukm.ukm.edit',
        'ketua-ukm.ukm.update',
        'ketua-ukm.events',
        'ketua-ukm.events.create',
    ];
    
    foreach ($routes as $routeName) {
        try {
            $url = route($routeName);
            echo "   ✅ Route {$routeName}: {$url}\n";
        } catch (Exception $e) {
            echo "   ❌ Route {$routeName}: ERROR - " . $e->getMessage() . "\n";
        }
    }
    
    echo "4. Testing UKM relationship...\n";
    
    if ($ketuaUkm) {
        $ledUkms = $ketuaUkm->ledUkms;
        echo "   UKMs led by {$ketuaUkm->name}: " . $ledUkms->count() . "\n";
        
        if ($ledUkms->count() > 0) {
            $ukm = $ledUkms->first();
            echo "   First UKM: {$ukm->name}\n";
            echo "   UKM leader_id: {$ukm->leader_id}\n";
            echo "   ✅ UKM relationship works\n";
        } else {
            echo "   ⚠️  Ketua UKM not assigned to any UKM yet\n";
        }
    }
    
    echo "5. Testing middleware functionality...\n";
    
    // Test middleware logic
    if ($ketuaUkm) {
        $hasAccess = $ketuaUkm->isKetuaUkm();
        echo "   User {$ketuaUkm->name} has ketua UKM access: " . ($hasAccess ? 'YES' : 'NO') . "\n";
        
        if ($hasAccess) {
            echo "   ✅ Middleware should allow access\n";
        } else {
            echo "   ❌ Middleware should deny access\n";
        }
    }
    
    echo "\n=== TESTING COMPLETED ===\n";
    echo "✅ KetuaUkmController should now work without middleware errors!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
