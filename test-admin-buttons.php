<?php

echo "=== TESTING ADMIN ACTIVATE/SUSPEND BUTTONS ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Creating test users with different statuses...\n";
    
    // Create test users for button testing
    $testUsers = [
        [
            'nim' => 'BTN001',
            'name' => 'Test Pending Button',
            'email' => '<EMAIL>',
            'status' => 'pending',
        ],
        [
            'nim' => 'BTN002',
            'name' => 'Test Suspended Button',
            'email' => '<EMAIL>',
            'status' => 'suspended',
        ],
        [
            'nim' => 'BTN003',
            'name' => 'Test Active Button',
            'email' => '<EMAIL>',
            'status' => 'active',
        ],
        [
            'nim' => 'BTN004',
            'name' => 'Test Inactive Button',
            'email' => '<EMAIL>',
            'status' => 'inactive',
        ],
    ];
    
    foreach ($testUsers as $userData) {
        // Delete if exists
        $existingUser = \App\Models\User::where('email', $userData['email'])->first();
        if ($existingUser) {
            $existingUser->delete();
        }
        
        // Create new test user
        $user = \App\Models\User::create([
            'nim' => $userData['nim'],
            'name' => $userData['name'],
            'email' => $userData['email'],
            'password' => \Illuminate\Support\Facades\Hash::make('password123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Fakultas Informatika',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'student',
            'status' => $userData['status'],
        ]);
        
        echo "   ✅ Created: {$user->name} (Status: {$user->status})\n";
    }
    
    echo "\n2. Testing activate functionality...\n";
    
    // Test activate for pending user
    $pendingUser = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($pendingUser) {
        echo "   Testing activate for pending user...\n";
        $pendingUser->update(['status' => 'active']);
        $pendingUser->refresh();
        
        if ($pendingUser->status === 'active') {
            echo "      ✅ Pending user successfully activated\n";
        } else {
            echo "      ❌ Failed to activate pending user\n";
        }
    }
    
    // Test activate for suspended user
    $suspendedUser = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($suspendedUser) {
        echo "   Testing activate for suspended user...\n";
        $suspendedUser->update(['status' => 'active']);
        $suspendedUser->refresh();
        
        if ($suspendedUser->status === 'active') {
            echo "      ✅ Suspended user successfully activated\n";
        } else {
            echo "      ❌ Failed to activate suspended user\n";
        }
    }
    
    // Test activate for inactive user
    $inactiveUser = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($inactiveUser) {
        echo "   Testing activate for inactive user...\n";
        $inactiveUser->update(['status' => 'active']);
        $inactiveUser->refresh();
        
        if ($inactiveUser->status === 'active') {
            echo "      ✅ Inactive user successfully activated\n";
        } else {
            echo "      ❌ Failed to activate inactive user\n";
        }
    }
    
    echo "\n3. Testing suspend functionality...\n";
    
    // Test suspend for active user
    $activeUser = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($activeUser) {
        echo "   Testing suspend for active user...\n";
        $activeUser->update(['status' => 'suspended']);
        $activeUser->refresh();
        
        if ($activeUser->status === 'suspended') {
            echo "      ✅ Active user successfully suspended\n";
        } else {
            echo "      ❌ Failed to suspend active user\n";
        }
    }
    
    echo "\n4. Testing admin protection...\n";
    
    // Create admin user for testing
    $adminUser = \App\Models\User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'nim' => 'ADM001',
            'name' => 'Test Admin User',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Fakultas Informatika',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'admin',
            'status' => 'active',
        ]
    );
    
    echo "   ✅ Admin user created for testing\n";
    
    echo "\n5. Checking button display logic...\n";
    
    $allUsers = \App\Models\User::whereIn('email', [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ])->get();
    
    foreach ($allUsers as $user) {
        echo "   User: {$user->name} (Status: {$user->status})\n";
        
        if ($user->status === 'pending') {
            echo "      Should show: GREEN 'Aktifkan' button\n";
        } elseif ($user->status === 'suspended') {
            echo "      Should show: BLUE 'Aktifkan' button\n";
        } elseif ($user->status === 'inactive') {
            echo "      Should show: YELLOW 'Aktifkan' button\n";
        } elseif ($user->status === 'active') {
            echo "      Should show: RED 'Suspend' button\n";
        }
    }
    
    echo "\n6. Cleaning up test users...\n";
    
    $testEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];
    
    foreach ($testEmails as $email) {
        $user = \App\Models\User::where('email', $email)->first();
        if ($user) {
            $user->delete();
            echo "   ✅ Deleted: {$email}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== ADMIN BUTTONS TEST COMPLETED ===\n";
echo "\nBUTTON DISPLAY LOGIC:\n";
echo "🟢 PENDING users → GREEN 'Aktifkan' button\n";
echo "🔵 SUSPENDED users → BLUE 'Aktifkan' button\n";
echo "🟡 INACTIVE users → YELLOW 'Aktifkan' button\n";
echo "🔴 ACTIVE users → RED 'Suspend' button\n";
echo "🛡️ ADMIN users → No suspend button (protected)\n";
echo "\nROUTES:\n";
echo "- Activate: PATCH /admin/users/{id}/activate\n";
echo "- Suspend: PATCH /admin/users/{id}/suspend\n";
echo "\nLOCATION:\n";
echo "- Admin Panel → Kelola Mahasiswa\n";
echo "- Buttons in action column of user table\n";
echo "- Detail page only shows 'Reset Password'\n";
echo "\nFUNCTIONALITY:\n";
echo "✅ Activate changes status to 'active'\n";
echo "✅ Suspend changes status to 'suspended'\n";
echo "✅ Admin accounts protected from suspension\n";
echo "✅ Confirmation dialogs before action\n";
echo "✅ Success/error messages displayed\n";
