<?php

echo "=== READING LARAVEL LOG ===\n\n";

$logFile = 'storage/logs/laravel.log';

if (!file_exists($logFile)) {
    echo "❌ Log file not found: {$logFile}\n";
    echo "Creating log directory...\n";
    
    if (!is_dir('storage/logs')) {
        mkdir('storage/logs', 0755, true);
        echo "✅ Created storage/logs directory\n";
    }
    
    echo "Log file will be created when <PERSON><PERSON> writes first log entry.\n";
    exit;
}

echo "📋 READING LAST 50 LINES OF LARAVEL LOG:\n";
echo "File: {$logFile}\n";
echo "Size: " . number_format(filesize($logFile)) . " bytes\n\n";

// Read last 50 lines
$lines = file($logFile);
$totalLines = count($lines);
$startLine = max(0, $totalLines - 50);

echo "Showing lines " . ($startLine + 1) . " to {$totalLines}:\n";
echo str_repeat("=", 80) . "\n";

for ($i = $startLine; $i < $totalLines; $i++) {
    echo ($i + 1) . ": " . $lines[$i];
}

echo str_repeat("=", 80) . "\n";
echo "End of log\n\n";

echo "🎯 TO MONITOR LOG IN REAL-TIME:\n";
echo "Run this command in terminal: tail -f storage/logs/laravel.log\n";
echo "Or refresh this script after accessing ketua UKM pages\n";
