<?php

echo "=== DEBUGGING BUKBER ERROR ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking for 'bukber' event in database...\n";
    
    // Check if there's an event with slug 'bukber'
    $bukberEvent = \App\Models\Event::where('slug', 'bukber')->first();
    
    if ($bukberEvent) {
        echo "   ✅ Found event with slug 'bukber'\n";
        echo "   ID: {$bukberEvent->id}\n";
        echo "   Title: {$bukberEvent->title}\n";
        echo "   Slug: {$bukberEvent->slug}\n";
        echo "   Status: {$bukberEvent->status}\n";
        echo "   UKM: {$bukberEvent->ukm->name}\n";
        echo "   Created: {$bukberEvent->created_at}\n";
    } else {
        echo "   ❌ No event found with slug 'bukber'\n";
    }
    
    echo "2. Checking all events with similar names...\n";
    
    $similarEvents = \App\Models\Event::where('title', 'LIKE', '%bukber%')
                                     ->orWhere('title', 'LIKE', '%buka%')
                                     ->orWhere('title', 'LIKE', '%puasa%')
                                     ->get();
    
    if ($similarEvents->count() > 0) {
        echo "   Found {$similarEvents->count()} similar events:\n";
        foreach ($similarEvents as $event) {
            echo "   - ID: {$event->id}, Title: {$event->title}, Slug: {$event->slug}\n";
        }
    } else {
        echo "   No similar events found\n";
    }
    
    echo "3. Checking recent session data...\n";
    
    // Check session files for bukber references
    $sessionDir = storage_path('framework/sessions');
    $sessionFiles = glob($sessionDir . '/*');
    
    foreach ($sessionFiles as $sessionFile) {
        $content = file_get_contents($sessionFile);
        if (strpos($content, 'bukber') !== false) {
            echo "   Found 'bukber' in session: " . basename($sessionFile) . "\n";
            
            // Try to decode session data
            if (preg_match('/s:3:"url";s:\d+:"([^"]+)"/', $content, $matches)) {
                echo "   Last URL: {$matches[1]}\n";
            }
        }
    }
    
    echo "4. Checking routes that might cause this error...\n";
    
    // Check if there are any routes that might be causing issues
    $routes = [
        'events.show' => '/kegiatan/{event}',
        'events.certificate' => '/events/{event}/certificate',
        'ketua-ukm.events.show' => '/ketua-ukm/events/{event}',
        'ketua-ukm.events.attendances.index' => '/ketua-ukm/events/{event}/attendances',
    ];
    
    foreach ($routes as $routeName => $pattern) {
        try {
            if (\Illuminate\Support\Facades\Route::has($routeName)) {
                echo "   ✅ Route exists: {$routeName} -> {$pattern}\n";
            } else {
                echo "   ❌ Route missing: {$routeName}\n";
            }
        } catch (\Exception $e) {
            echo "   ⚠️  Route check failed: {$routeName} - {$e->getMessage()}\n";
        }
    }
    
    echo "5. Checking for certificate-related routes...\n";
    
    // Check if there's a certificate route that might be causing issues
    try {
        $certificateRoutes = \Illuminate\Support\Facades\Route::getRoutes()->getByName('events.certificate');
        if ($certificateRoutes) {
            echo "   ✅ Certificate route exists\n";
        } else {
            echo "   ❌ Certificate route not found\n";
        }
    } catch (\Exception $e) {
        echo "   ⚠️  Certificate route check failed: {$e->getMessage()}\n";
    }
    
    echo "6. Checking for any 404 or error pages...\n";
    
    // Check if there are any custom error pages
    $errorViews = [
        'errors.404',
        'errors.500',
        'errors.403',
    ];
    
    foreach ($errorViews as $errorView) {
        $viewPath = resource_path('views/' . str_replace('.', '/', $errorView) . '.blade.php');
        if (file_exists($viewPath)) {
            echo "   ✅ Error view exists: {$errorView}\n";
            
            // Check if it contains 'Bukber'
            $content = file_get_contents($viewPath);
            if (strpos($content, 'Bukber') !== false) {
                echo "   ⚠️  Found 'Bukber' in error view: {$errorView}\n";
            }
        } else {
            echo "   ❌ Error view missing: {$errorView}\n";
        }
    }
    
    echo "7. Checking Laravel logs for errors...\n";
    
    $logPath = storage_path('logs/laravel.log');
    if (file_exists($logPath)) {
        echo "   ✅ Laravel log exists\n";
        
        // Read last 50 lines of log
        $logLines = array_slice(file($logPath), -50);
        $bukberErrors = array_filter($logLines, function($line) {
            return strpos(strtolower($line), 'bukber') !== false;
        });
        
        if (!empty($bukberErrors)) {
            echo "   Found bukber-related errors in log:\n";
            foreach ($bukberErrors as $error) {
                echo "   " . trim($error) . "\n";
            }
        } else {
            echo "   No bukber-related errors in recent logs\n";
        }
    } else {
        echo "   ❌ Laravel log not found\n";
    }
    
    echo "8. Checking current user session...\n";
    
    // Check if there's a current user that might be causing issues
    try {
        $user = \Illuminate\Support\Facades\Auth::user();
        if ($user) {
            echo "   Current user: {$user->name} (ID: {$user->id})\n";
            echo "   Role: {$user->role}\n";
            echo "   Status: {$user->status}\n";
        } else {
            echo "   No authenticated user\n";
        }
    } catch (\Exception $e) {
        echo "   ⚠️  User check failed: {$e->getMessage()}\n";
    }
    
    echo "9. Possible causes and solutions...\n";
    
    echo "   Possible causes:\n";
    echo "   1. Event with slug 'bukber' exists but has issues\n";
    echo "   2. Route parameter mismatch (ID vs slug)\n";
    echo "   3. Missing or corrupted view file\n";
    echo "   4. Controller method error\n";
    echo "   5. Database relationship issue\n";
    echo "   6. Session data corruption\n";
    
    echo "   Recommended solutions:\n";
    echo "   1. Clear browser cache and cookies\n";
    echo "   2. Clear Laravel cache: php artisan cache:clear\n";
    echo "   3. Clear sessions: php artisan session:flush\n";
    echo "   4. Check event data integrity\n";
    echo "   5. Verify route definitions\n";
    echo "   6. Check controller methods\n";
    
    echo "10. Quick fixes to try...\n";
    
    echo "   Run these commands:\n";
    echo "   php artisan cache:clear\n";
    echo "   php artisan config:clear\n";
    echo "   php artisan route:clear\n";
    echo "   php artisan view:clear\n";
    echo "   php artisan session:flush\n";
    
    if ($bukberEvent) {
        echo "\n   Event-specific checks:\n";
        echo "   - Check if event has valid UKM relationship\n";
        echo "   - Check if event status is valid\n";
        echo "   - Check if event dates are valid\n";
        echo "   - Check if event has required fields\n";
        
        // Validate event data
        $issues = [];
        
        if (!$bukberEvent->ukm) {
            $issues[] = "Missing UKM relationship";
        }
        
        if (!in_array($bukberEvent->status, ['draft', 'published', 'ongoing', 'completed', 'cancelled'])) {
            $issues[] = "Invalid status: {$bukberEvent->status}";
        }
        
        if (!$bukberEvent->start_datetime) {
            $issues[] = "Missing start datetime";
        }
        
        if (!$bukberEvent->location) {
            $issues[] = "Missing location";
        }
        
        if (!empty($issues)) {
            echo "   ⚠️  Event data issues found:\n";
            foreach ($issues as $issue) {
                echo "     - {$issue}\n";
            }
        } else {
            echo "   ✅ Event data appears valid\n";
        }
    }
    
    echo "\n=== BUKBER ERROR DEBUGGING COMPLETED ===\n";
    
} catch (Exception $e) {
    echo "❌ Error during debugging: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
