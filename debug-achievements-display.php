<?php
/**
 * Debug achievements display in UKM show page
 */

echo "=== DEBUGGING ACHIEVEMENTS DISPLAY ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing IMMA UKM achievements...\n";
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    
    if (!$ukm) {
        echo "   ❌ IMMA UKM not found\n";
        exit;
    }
    
    echo "   ✅ Found IMMA UKM (ID: {$ukm->id})\n";
    
    // Load achievements like in controller
    $achievements = $ukm->achievements()->get();

    // Try different ways to set the relationship
    echo "   Testing setRelation...\n";
    $ukm->setRelation('achievements', $achievements);
    echo "   After setRelation - achievements is null: " . ($ukm->achievements === null ? 'Yes' : 'No') . "\n";

    // Try using load instead
    echo "   Testing load...\n";
    $ukm->load('achievements');
    echo "   After load - achievements is null: " . ($ukm->achievements === null ? 'Yes' : 'No') . "\n";
    
    echo "   ✅ Achievements loaded: " . $achievements->count() . "\n\n";
    
    echo "2. Testing achievement count logic from view...\n";
    
    // Simulate the view logic
    $achievementCount = 0;
    if ($ukm->achievements !== null) {
        if (is_array($ukm->achievements) || ($ukm->achievements instanceof \Countable)) {
            $achievementCount = count($ukm->achievements);
        } elseif (method_exists($ukm->achievements, 'count')) {
            $achievementCount = $ukm->achievements->count();
        }
    }
    
    echo "   📊 Achievement count from view logic: $achievementCount\n";
    echo "   📊 Direct achievements count: " . ($ukm->achievements ? $ukm->achievements->count() : 'NULL') . "\n";
    echo "   📊 Achievements is null: " . ($ukm->achievements === null ? 'Yes' : 'No') . "\n";
    echo "   📊 Achievements type: " . ($ukm->achievements ? get_class($ukm->achievements) : 'NULL') . "\n\n";
    
    echo "3. Testing individual achievements...\n";
    
    foreach ($achievements as $achievement) {
        echo "   📋 Achievement: {$achievement->title}\n";
        echo "      ID: {$achievement->id}\n";
        echo "      Level: {$achievement->level}\n";
        echo "      Level Text: " . ($achievement->level_text ?? 'N/A') . "\n";
        echo "      Type: {$achievement->type}\n";
        echo "      Type Text: " . ($achievement->type_text ?? 'N/A') . "\n";
        echo "      Date: {$achievement->achievement_date}\n";
        echo "      Position: " . ($achievement->position ?: 'N/A') . "\n";
        echo "      Participants: " . ($achievement->participants ?: 'N/A') . "\n";
        echo "      Description: " . ($achievement->description ?: 'N/A') . "\n";
        echo "      Organizer: " . ($achievement->organizer ?: 'N/A') . "\n";
        echo "      Is Featured: " . ($achievement->is_featured ? 'Yes' : 'No') . "\n";
        echo "      Certificate File: " . ($achievement->certificate_file ?: 'N/A') . "\n";
        echo "      ---\n";
    }
    
    echo "\n4. Testing view rendering with achievements...\n";
    
    try {
        // Test rendering the achievements section specifically
        $viewData = [
            'ukm' => $ukm,
            'achievements' => $achievements,
            'publishedEvents' => collect([]),
            'isMember' => false,
            'membershipStatus' => null
        ];
        
        echo "   Creating view with UKM data...\n";
        $view = view('ukms.show', $viewData);
        echo "   ✅ View created successfully\n";
        
        echo "   Rendering view...\n";
        $content = $view->render();
        echo "   ✅ View rendered successfully\n";
        echo "   📊 Content length: " . strlen($content) . " characters\n";
        
        // Check if achievements section is in the rendered content
        $hasAchievements = strpos($content, 'Prestasi UKM') !== false;
        $hasAchievementTitle = strpos($content, 'Lomba Tilawah') !== false;
        $hasAchievementBadge = strpos($content, 'Regional') !== false;
        
        echo "   📊 Contains 'Prestasi UKM': " . ($hasAchievements ? 'Yes' : 'No') . "\n";
        echo "   📊 Contains 'Lomba Tilawah': " . ($hasAchievementTitle ? 'Yes' : 'No') . "\n";
        echo "   📊 Contains 'Regional' badge: " . ($hasAchievementBadge ? 'Yes' : 'No') . "\n";
        
    } catch (\Exception $e) {
        echo "   ❌ View rendering error: " . $e->getMessage() . "\n";
        echo "   📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    }
    
    echo "\n5. Creating test HTML for achievements...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Achievements Display Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .achievement-card { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 10px 0; 
            background: #f9f9f9; 
        }
        .badge { 
            display: inline-block; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: bold; 
            margin-right: 5px; 
        }
        .badge-success { background: #d4edda; color: #155724; }
        .badge-info { background: #d1ecf1; color: #0c5460; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-secondary { background: #e2e3e5; color: #383d41; }
        .position-badge { 
            width: 40px; 
            height: 40px; 
            border-radius: 50%; 
            background: linear-gradient(135deg, #ffd700, #ffb347); 
            display: inline-flex; 
            align-items: center; 
            justify-content: center; 
            color: white; 
            font-weight: bold; 
            float: right; 
        }
    </style>
</head>
<body>
    <h1>IMMA UKM Achievements Test</h1>
    
    <p><strong>Achievement Count:</strong> $achievementCount</p>
    <p><strong>Direct Count:</strong> " . $achievements->count() . "</p>
    
    <h2>Achievements:</h2>";
    
    foreach ($achievements as $achievement) {
        $levelClass = $achievement->level == 'international' ? 'success' : 
                     ($achievement->level == 'national' ? 'info' : 
                     ($achievement->level == 'regional' ? 'warning' : 'secondary'));
        
        $typeClass = 'secondary';
        
        $testHtml .= "
    <div class='achievement-card'>
        <div style='overflow: hidden;'>
            " . ($achievement->position ? "<div class='position-badge'>#" . $achievement->position . "</div>" : "") . "
            <h3>{$achievement->title}</h3>
            <div style='margin: 10px 0;'>
                <span class='badge badge-{$levelClass}'>" . ucfirst($achievement->level) . "</span>
                <span class='badge badge-{$typeClass}'>" . ucfirst($achievement->type) . "</span>
                " . ($achievement->is_featured ? "<span class='badge badge-warning'>⭐ Unggulan</span>" : "") . "
            </div>
            <p><strong>Tanggal:</strong> " . date('d M Y', strtotime($achievement->achievement_date)) . "</p>
            " . ($achievement->organizer ? "<p><strong>Penyelenggara:</strong> {$achievement->organizer}</p>" : "") . "
            " . ($achievement->participants ? "<p><strong>Peserta:</strong> {$achievement->participants}</p>" : "") . "
            " . ($achievement->description ? "<p><strong>Deskripsi:</strong> {$achievement->description}</p>" : "") . "
        </div>
    </div>";
    }
    
    $testHtml .= "
    
    <h2>Test Links:</h2>
    <ul>
        <li><a href='http://127.0.0.1:8000/ukms/imma' target='_blank'>IMMA UKM Detail Page</a></li>
        <li><a href='http://127.0.0.1:8000/ukms' target='_blank'>All UKMs</a></li>
        <li><a href='http://127.0.0.1:8000/' target='_blank'>Homepage (should show achievements)</a></li>
    </ul>
</body>
</html>";
    
    file_put_contents(public_path('achievements-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/achievements-test.html\n";
    
    echo "\n=== ACHIEVEMENTS DEBUG COMPLETED ===\n";
    echo "🔗 Test page: http://127.0.0.1:8000/achievements-test.html\n";
    echo "🔗 IMMA UKM: http://127.0.0.1:8000/ukms/imma\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
