@echo off
echo ========================================
echo   MANUAL FIX FOR ROLE ENUM
echo ========================================
echo.

echo Please run this SQL command in your MySQL client:
echo.
echo ALTER TABLE users MODIFY COLUMN role ENUM('student', 'ketua_ukm', 'admin') DEFAULT 'student';
echo.
echo After running the SQL, press any key to test...
pause

echo.
echo Testing role assignment...
php -r "
require_once 'vendor/autoload.php';
\$app = require_once 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    \$user = \App\Models\User::where('role', 'student')->first();
    if (\$user) {
        echo 'Testing with user: ' . \$user->name . PHP_EOL;
        \$user->update(['role' => 'ketua_ukm']);
        \$user->refresh();
        echo 'New role: ' . \$user->role . PHP_EOL;
        
        if (\$user->role === 'ketua_ukm') {
            echo '✅ SUCCESS: Role assignment works!' . PHP_EOL;
            \$user->update(['role' => 'student']);
            echo 'Reverted to student' . PHP_EOL;
        } else {
            echo '❌ FAILED: Role assignment failed' . PHP_EOL;
        }
    } else {
        echo 'No test user found' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo ========================================
echo   MANUAL FIX COMPLETED
echo ========================================
pause
