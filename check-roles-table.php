<?php

echo "=== CHECKING ROLES TABLE STATUS ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Table 'roles' exists\n";
        
        // Check content
        $stmt = $pdo->query("SELECT * FROM roles");
        $roles = $stmt->fetchAll();
        
        echo "Found " . count($roles) . " roles:\n";
        foreach ($roles as $role) {
            echo "- {$role['name']} (guard: {$role['guard_name']})\n";
        }
    } else {
        echo "❌ Table 'roles' does NOT exist\n";
        echo "🔧 Creating roles table...\n";
        
        // Create roles table
        $pdo->exec("
            CREATE TABLE roles (
                id bigint unsigned NOT NULL AUTO_INCREMENT,
                name varchar(255) NOT NULL,
                guard_name varchar(255) NOT NULL,
                created_at timestamp NULL DEFAULT NULL,
                updated_at timestamp NULL DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY roles_name_guard_name_unique (name, guard_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        echo "✅ Roles table created\n";
        
        // Insert basic roles
        $pdo->exec("
            INSERT INTO roles (name, guard_name, created_at, updated_at) VALUES
            ('admin', 'web', NOW(), NOW()),
            ('student', 'web', NOW(), NOW()),
            ('ketua_ukm', 'web', NOW(), NOW())
        ");
        
        echo "✅ Basic roles inserted\n";
    }
    
    // Check other permission tables
    $permissionTables = ['permissions', 'model_has_roles', 'model_has_permissions', 'role_has_permissions'];
    
    echo "\n📋 CHECKING OTHER PERMISSION TABLES:\n";
    
    foreach ($permissionTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            echo "✅ Table '{$table}' exists with {$count} records\n";
        } else {
            echo "❌ Table '{$table}' does NOT exist\n";
        }
    }
    
    // Test the specific query that's failing
    echo "\n🔍 TESTING FAILING QUERY:\n";
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM roles WHERE name = ? AND guard_name = ? LIMIT 1");
        $stmt->execute(['student', 'web']);
        $role = $stmt->fetch();
        
        if ($role) {
            echo "✅ Query successful - Found student role: ID {$role['id']}\n";
        } else {
            echo "❌ Query successful but no student role found\n";
        }
    } catch (Exception $e) {
        echo "❌ Query failed: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
