<?php

echo "=== VERIFYING ROLES AND PERMISSIONS ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking Spatie roles...\n";
    
    $roles = \Spatie\Permission\Models\Role::all();
    echo "   Available roles:\n";
    foreach ($roles as $role) {
        echo "      - {$role->name} (guard: {$role->guard_name})\n";
    }
    
    echo "\n2. Checking database enum...\n";
    $result = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users LIKE 'role'");
    if (!empty($result)) {
        echo "   Database enum: " . $result[0]->Type . "\n";
    }
    
    echo "\n3. Testing role assignment...\n";
    
    // Find a test user
    $testUser = \App\Models\User::where('role', 'student')->first();
    if ($testUser) {
        echo "   Testing with user: {$testUser->name}\n";
        echo "   Current role: {$testUser->role}\n";
        echo "   Current Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . "\n";
        
        try {
            // Test assigning ketua_ukm role
            $testUser->assignRole('ketua_ukm');
            echo "   ✅ Successfully assigned ketua_ukm role via Spatie\n";
            
            // Update role column
            $testUser->update(['role' => 'ketua_ukm']);
            $testUser->refresh();
            
            echo "   Updated role column to: {$testUser->role}\n";
            echo "   Spatie roles after update: " . $testUser->roles->pluck('name')->join(', ') . "\n";
            echo "   isKetuaUkm() returns: " . ($testUser->isKetuaUkm() ? 'true' : 'false') . "\n";
            
            if ($testUser->role === 'ketua_ukm' && $testUser->hasRole('ketua_ukm')) {
                echo "   ✅ SUCCESS: Role assignment works perfectly!\n";
            } else {
                echo "   ❌ PARTIAL: Some issues detected\n";
            }
            
            // Revert back
            $testUser->update(['role' => 'student']);
            $testUser->syncRoles(['student']);
            echo "   Reverted back to student\n";
            
        } catch (Exception $e) {
            echo "   ❌ ERROR: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n4. Checking permissions...\n";
    
    $permissions = \Spatie\Permission\Models\Permission::all();
    echo "   Total permissions: " . $permissions->count() . "\n";
    
    $ketuaUkmRole = \Spatie\Permission\Models\Role::where('name', 'ketua_ukm')->first();
    if ($ketuaUkmRole) {
        $rolePermissions = $ketuaUkmRole->permissions;
        echo "   Ketua UKM permissions: " . $rolePermissions->pluck('name')->join(', ') . "\n";
    }
    
    echo "\n=== VERIFICATION COMPLETED ===\n";
    echo "✅ All roles and permissions are properly configured!\n";
    echo "You can now assign 'ketua_ukm' role without errors.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
