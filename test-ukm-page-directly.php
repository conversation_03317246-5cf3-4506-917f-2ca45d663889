<?php
/**
 * Test UKM page directly to catch method_exists error
 */

echo "=== TESTING UKM PAGE DIRECTLY ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make('Illuminate\Contracts\Console\Kernel');
    $kernel->bootstrap();
    
    echo "1. Testing UKM Controller index method directly...\n";
    
    // Create request
    $request = \Illuminate\Http\Request::create('/ukms', 'GET');
    
    // Create controller
    $controller = new \App\Http\Controllers\UkmController();
    
    echo "   Controller created successfully\n";
    
    // Call index method
    try {
        echo "   Calling index method...\n";
        $response = $controller->index($request);
        echo "   ✅ Index method executed successfully\n";
        echo "   Response type: " . get_class($response) . "\n";
        
        if ($response instanceof \Illuminate\View\View) {
            echo "   View name: " . $response->getName() . "\n";
            
            // Try to render the view
            echo "   Attempting to render view...\n";
            $content = $response->render();
            echo "   ✅ View rendered successfully\n";
            echo "   Content length: " . strlen($content) . " characters\n";
            
        }
        
    } catch (\Exception $e) {
        echo "   ❌ Index method error: " . $e->getMessage() . "\n";
        echo "   📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
        
        if (strpos($e->getMessage(), 'method_exists') !== false) {
            echo "   🔍 This is the method_exists error!\n";
            echo "   📋 Full stack trace:\n";
            echo $e->getTraceAsString() . "\n";
        }
    }
    
    echo "\n2. Testing UKM show method...\n";
    
    $ukm = \App\Models\Ukm::first();
    if ($ukm) {
        try {
            echo "   Testing show method for UKM: {$ukm->name}\n";
            $showRequest = \Illuminate\Http\Request::create("/ukms/{$ukm->slug}", 'GET');
            
            $response = $controller->show($ukm);
            echo "   ✅ Show method executed successfully\n";
            
            if ($response instanceof \Illuminate\View\View) {
                echo "   Attempting to render show view...\n";
                $content = $response->render();
                echo "   ✅ Show view rendered successfully\n";
            }
            
        } catch (\Exception $e) {
            echo "   ❌ Show method error: " . $e->getMessage() . "\n";
            echo "   📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
            
            if (strpos($e->getMessage(), 'method_exists') !== false) {
                echo "   🔍 This is the method_exists error in show!\n";
                echo "   📋 Full stack trace:\n";
                echo $e->getTraceAsString() . "\n";
            }
        }
    }
    
    echo "\n3. Testing view rendering with specific UKM data...\n";
    
    $ukms = \App\Models\Ukm::with(['leader'])->limit(3)->get();
    
    // Load achievements manually for each UKM
    foreach ($ukms as $ukm) {
        try {
            echo "   Processing UKM: {$ukm->name}\n";
            
            $achievements = $ukm->achievements()->get();
            $ukm->setRelation('achievements', $achievements);
            
            echo "     ✅ Achievements loaded: " . $achievements->count() . "\n";
            
            // Test accessing achievements in view context
            $achievementsCount = $ukm->achievements ? $ukm->achievements->count() : 0;
            echo "     ✅ Achievements count accessed: $achievementsCount\n";
            
        } catch (\Exception $e) {
            echo "     ❌ Error processing UKM: " . $e->getMessage() . "\n";
            
            if (strpos($e->getMessage(), 'method_exists') !== false) {
                echo "     🔍 method_exists error found!\n";
                echo "     📋 Details: " . $e->getFile() . ":" . $e->getLine() . "\n";
                echo "     📋 Stack trace:\n";
                echo $e->getTraceAsString() . "\n";
            }
        }
    }
    
    echo "\n4. Testing view compilation...\n";
    
    try {
        // Test compiling the UKM index view
        $viewData = [
            'ukms' => $ukms,
            'categories' => ['technology', 'sports', 'arts']
        ];
        
        echo "   Creating view with data...\n";
        $view = view('ukms.index', $viewData);
        echo "   ✅ View created\n";
        
        echo "   Rendering view...\n";
        $content = $view->render();
        echo "   ✅ View rendered successfully\n";
        echo "   Content length: " . strlen($content) . " characters\n";
        
    } catch (\Exception $e) {
        echo "   ❌ View rendering error: " . $e->getMessage() . "\n";
        echo "   📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
        
        if (strpos($e->getMessage(), 'method_exists') !== false) {
            echo "   🔍 method_exists error in view rendering!\n";
            echo "   📋 Stack trace:\n";
            echo $e->getTraceAsString() . "\n";
        }
    }
    
    echo "\n=== TEST COMPLETED ===\n";
    
} catch (\Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    echo "📋 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "📋 Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
