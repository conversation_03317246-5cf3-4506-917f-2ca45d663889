<?php

// Bootstrap Laravel
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING LARAVEL AUTHENTICATION ===\n\n";

try {
    // 1. Test database connection via Laravel
    echo "1. Testing Laravel database connection...\n";
    
    $users = \App\Models\User::all();
    echo "✅ Laravel DB connection works, found " . $users->count() . " users\n";
    
    foreach ($users as $user) {
        echo "- {$user->name} ({$user->email}) - Role: {$user->role}, Status: {$user->status}\n";
    }
    
    // 2. Test Auth::attempt directly
    echo "\n2. Testing Auth::attempt with known credentials...\n";
    
    $testCredentials = [
        ['email' => '<EMAIL>', 'password' => 'admin123'],
        ['email' => '<EMAIL>', 'password' => 'password'],
        ['nim' => 'ADMIN001', 'password' => 'password'],
        ['nim' => 'TEST001', 'password' => 'admin123'],
    ];
    
    foreach ($testCredentials as $creds) {
        $field = isset($creds['email']) ? 'email' : 'nim';
        $value = isset($creds['email']) ? $creds['email'] : $creds['nim'];
        
        echo "\nTesting: {$field} = {$value}, password = {$creds['password']}\n";
        
        if (\Illuminate\Support\Facades\Auth::attempt($creds)) {
            $user = \Illuminate\Support\Facades\Auth::user();
            echo "✅ Auth::attempt SUCCESS\n";
            echo "   User: {$user->name}\n";
            echo "   Role: {$user->role}\n";
            echo "   Status: {$user->status}\n";
            
            // Test status check
            if ($user->status === 'active') {
                echo "✅ Status check: PASS (active)\n";
            } else {
                echo "❌ Status check: FAIL (status: {$user->status})\n";
            }
            
            \Illuminate\Support\Facades\Auth::logout();
        } else {
            echo "❌ Auth::attempt FAILED\n";
            
            // Debug: Check if user exists
            $user = \App\Models\User::where($field, $value)->first();
            if ($user) {
                echo "   User exists in database\n";
                echo "   Stored password hash: " . substr($user->password, 0, 30) . "...\n";
                
                // Test password manually
                if (\Illuminate\Support\Facades\Hash::check($creds['password'], $user->password)) {
                    echo "   ✅ Password hash check: PASS\n";
                    echo "   🚨 Auth::attempt failed despite correct password!\n";
                } else {
                    echo "   ❌ Password hash check: FAIL\n";
                }
            } else {
                echo "   ❌ User not found in database\n";
            }
        }
    }
    
    // 3. Test LoginRequest logic simulation
    echo "\n3. Testing LoginRequest logic simulation...\n";
    
    $loginInput = '<EMAIL>';
    $passwordInput = 'admin123';
    
    echo "Simulating login with: {$loginInput} / {$passwordInput}\n";
    
    // Same logic as LoginRequest
    $field = filter_var($loginInput, FILTER_VALIDATE_EMAIL) ? 'email' : 'nim';
    echo "Field detected: {$field}\n";
    
    $credentials = [$field => $loginInput, 'password' => $passwordInput];
    
    if (\Illuminate\Support\Facades\Auth::attempt($credentials)) {
        $user = \Illuminate\Support\Facades\Auth::user();
        echo "✅ LoginRequest simulation: SUCCESS\n";
        echo "   User: {$user->name}\n";
        
        // Status checks (same as LoginRequest)
        if ($user->status === 'pending') {
            echo "❌ Would fail: Status is pending\n";
        } elseif ($user->status === 'suspended') {
            echo "❌ Would fail: Status is suspended\n";
        } elseif ($user->status === 'inactive') {
            echo "❌ Would fail: Status is inactive\n";
        } elseif ($user->status !== 'active') {
            echo "❌ Would fail: Status is not active ({$user->status})\n";
        } else {
            echo "✅ Would succeed: Status is active\n";
        }
        
        \Illuminate\Support\Facades\Auth::logout();
    } else {
        echo "❌ LoginRequest simulation: FAILED\n";
    }
    
    // 4. Check auth configuration
    echo "\n4. Checking auth configuration...\n";
    
    $authConfig = config('auth');
    echo "Default guard: " . $authConfig['defaults']['guard'] . "\n";
    echo "User provider: " . $authConfig['guards']['web']['provider'] . "\n";
    echo "User model: " . $authConfig['providers']['users']['model'] . "\n";
    
    // 5. Test User model directly
    echo "\n5. Testing User model...\n";
    
    $testUser = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($testUser) {
        echo "✅ User model works\n";
        echo "   Fillable fields: " . implode(', ', $testUser->getFillable()) . "\n";
        echo "   Hidden fields: " . implode(', ', $testUser->getHidden()) . "\n";
        
        // Test password verification
        if (\Illuminate\Support\Facades\Hash::check('admin123', $testUser->password)) {
            echo "✅ Password verification works\n";
        } else {
            echo "❌ Password verification failed\n";
        }
    } else {
        echo "❌ Test user not found\n";
    }
    
    echo "\n=== DIAGNOSIS COMPLETE ===\n";
    echo "If Auth::attempt works here but fails in browser, the issue is likely:\n";
    echo "1. Session/cookie issues\n";
    echo "2. CSRF token issues\n";
    echo "3. Middleware interference\n";
    echo "4. Rate limiting\n";
    echo "5. Form field name mismatch\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
