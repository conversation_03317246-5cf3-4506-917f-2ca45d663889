# 🚨 MANUAL FIX LOGIN ISSUE - STEP BY STEP

## 🔍 **MASALAH YANG TERIDENTIFIKASI:**
- Database connection issues
- Users table mungkin tidak ada atau kosong
- Migration belum berjalan dengan benar

## 🛠️ **SOLUSI STEP-BY-STEP:**

### **STEP 1: Pastikan MySQL/XAMPP Berjalan**

1. **Buka XAMPP Control Panel**
2. **Start Apache dan MySQL**
3. **Pastikan MySQL berjalan di port 3306**

### **STEP 2: Cek Database Exists**

1. **Buka phpMyAdmin:** `http://localhost/phpmyadmin`
2. **Cek apakah database `ukmwebL` ada**
3. **Jika tidak ada, buat database:**
   ```sql
   CREATE DATABASE ukmwebL;
   ```

### **STEP 3: Buat Table Users Manual**

1. **Pilih database `ukmwebL`**
2. **<PERSON><PERSON> tab SQL**
3. **Jalankan query ini:**

```sql
CREATE TABLE users (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    nim varchar(255) NOT NULL,
    name varchar(255) NOT NULL,
    email varchar(255) NOT NULL,
    email_verified_at timestamp NULL DEFAULT NULL,
    password varchar(255) NOT NULL,
    phone varchar(255) DEFAULT NULL,
    gender enum('male','female') NOT NULL,
    faculty varchar(255) NOT NULL,
    major varchar(255) NOT NULL,
    batch varchar(255) NOT NULL,
    role enum('admin','student','ketua_ukm') NOT NULL DEFAULT 'student',
    status enum('active','inactive','suspended','pending') NOT NULL DEFAULT 'pending',
    remember_token varchar(100) DEFAULT NULL,
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY users_email_unique (email),
    UNIQUE KEY users_nim_unique (nim)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### **STEP 4: Insert Admin User**

**Jalankan query ini di phpMyAdmin:**

```sql
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    'ADMIN001',
    'Administrator',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '081234567890',
    'male',
    'Administrasi',
    'Sistem Informasi',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
);
```

### **STEP 5: Insert Test Student**

```sql
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    '1301210001',
    'Test Student',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '081234567891',
    'male',
    'Informatika',
    'Sistem Informasi',
    '2021',
    'student',
    'active',
    NOW(),
    NOW(),
    NOW()
);
```

### **STEP 6: Verifikasi Data**

**Cek apakah user berhasil dibuat:**

```sql
SELECT id, nim, name, email, role, status, created_at 
FROM users 
ORDER BY id;
```

**Hasil yang diharapkan:**
```
1 | ADMIN001 | Administrator | <EMAIL> | admin | active
2 | 1301210001 | Test Student | <EMAIL> | student | active
```

### **STEP 7: Test Login**

1. **Buka:** `http://localhost:8000/login`
2. **Login Admin:**
   - Email: `<EMAIL>`
   - Password: `admin123`
3. **Login Student:**
   - Email: `<EMAIL>`
   - Password: `student123`

## 🔧 **JIKA MASIH TIDAK BISA LOGIN:**

### **Troubleshoot 1: Cek .env File**

Pastikan file `.env` memiliki setting yang benar:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ukmwebL
DB_USERNAME=root
DB_PASSWORD=
```

### **Troubleshoot 2: Clear Cache**

Jalankan di terminal:

```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

### **Troubleshoot 3: Cek Auth Configuration**

Pastikan file `config/auth.php` menggunakan model User yang benar:

```php
'providers' => [
    'users' => [
        'driver' => 'eloquent',
        'model' => App\Models\User::class,
    ],
],
```

### **Troubleshoot 4: Reset Password Hash**

Jika password tidak cocok, update dengan hash baru:

```sql
UPDATE users 
SET password = '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
WHERE email = '<EMAIL>';
```

### **Troubleshoot 5: Cek Laravel Log**

Cek file `storage/logs/laravel.log` untuk error messages.

## 🎯 **ALTERNATIVE: Buat User via Laravel Tinker**

Jika database sudah ada, coba via tinker:

```bash
php artisan tinker
```

Lalu jalankan:

```php
use App\Models\User;
use Illuminate\Support\Facades\Hash;

User::create([
    'nim' => 'ADMIN001',
    'name' => 'Administrator',
    'email' => '<EMAIL>',
    'password' => Hash::make('admin123'),
    'phone' => '081234567890',
    'gender' => 'male',
    'faculty' => 'Administrasi',
    'major' => 'Sistem Informasi',
    'batch' => '2024',
    'role' => 'admin',
    'status' => 'active',
    'email_verified_at' => now(),
]);
```

## 📋 **CHECKLIST FINAL:**

- [ ] MySQL/XAMPP running
- [ ] Database `ukmwebL` exists
- [ ] Table `users` exists
- [ ] Admin user inserted
- [ ] Password hash correct
- [ ] User status = 'active'
- [ ] .env database config correct
- [ ] Laravel cache cleared

## 🎉 **SETELAH BERHASIL:**

**Login Credentials:**
- **Admin:** <EMAIL> / admin123
- **Student:** <EMAIL> / student123

**URLs:**
- **Login:** http://localhost:8000/login
- **Admin Dashboard:** http://localhost:8000/admin/dashboard

---

**💡 TIP:** Jika semua cara di atas tidak berhasil, kemungkinan ada masalah dengan Laravel authentication middleware atau routing. Dalam kasus ini, kita perlu cek file `app/Http/Controllers/Auth/LoginController.php` dan middleware authentication.
