<?php
/**
 * Debug method_exists error in UKM pages
 */

echo "=== DEBUGGING METHOD_EXISTS ERROR ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing UKM model and relationships...\n";
    
    // Get a UKM to test
    $ukm = \App\Models\Ukm::first();
    
    if (!$ukm) {
        echo "   ❌ No UKM found in database\n";
        exit;
    }
    
    echo "   ✅ Found UKM: {$ukm->name}\n";
    echo "   📄 Slug: {$ukm->slug}\n\n";
    
    echo "2. Testing UKM relationships...\n";
    
    // Test leader relationship
    try {
        $leader = $ukm->leader;
        echo "   ✅ Leader relationship: " . ($leader ? $leader->name : "No leader") . "\n";
    } catch (Exception $e) {
        echo "   ❌ Leader relationship error: " . $e->getMessage() . "\n";
    }
    
    // Test activeMembers relationship
    try {
        $members = $ukm->activeMembers;
        echo "   ✅ Active members count: " . $members->count() . "\n";
    } catch (Exception $e) {
        echo "   ❌ Active members error: " . $e->getMessage() . "\n";
    }
    
    // Test achievements relationship
    try {
        $achievements = $ukm->achievements;
        if ($achievements) {
            echo "   ✅ Achievements count: " . $achievements->count() . "\n";
        } else {
            echo "   ⚠️ Achievements relationship returned null\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Achievements error: " . $e->getMessage() . "\n";
    }
    
    // Test publishedEvents relationship
    try {
        $events = $ukm->publishedEvents;
        if ($events) {
            echo "   ✅ Published events count: " . $events->count() . "\n";
        } else {
            echo "   ⚠️ Published events relationship returned null\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Published events error: " . $e->getMessage() . "\n";
    }
    
    echo "\n3. Testing UKM controller methods...\n";
    
    // Test UKM show method
    try {
        $controller = new \App\Http\Controllers\UkmController();
        
        if (method_exists($controller, 'show')) {
            echo "   ✅ UkmController::show method exists\n";
        } else {
            echo "   ❌ UkmController::show method not found\n";
        }
        
        if (method_exists($controller, 'index')) {
            echo "   ✅ UkmController::index method exists\n";
        } else {
            echo "   ❌ UkmController::index method not found\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Controller test error: " . $e->getMessage() . "\n";
    }
    
    echo "\n4. Testing User model and relationships...\n";
    
    // Test user model
    try {
        $user = \App\Models\User::first();
        if ($user) {
            echo "   ✅ Found user: {$user->name}\n";
            
            // Test user relationships that might cause issues
            try {
                $userUkms = $user->ukms;
                echo "   ✅ User UKMs count: " . $userUkms->count() . "\n";
            } catch (Exception $e) {
                echo "   ❌ User UKMs error: " . $e->getMessage() . "\n";
            }
            
        } else {
            echo "   ❌ No users found\n";
        }
    } catch (Exception $e) {
        echo "   ❌ User model error: " . $e->getMessage() . "\n";
    }
    
    echo "\n5. Testing specific UKM with slug 'imma'...\n";
    
    try {
        $immaUkm = \App\Models\Ukm::where('slug', 'imma')->first();
        if ($immaUkm) {
            echo "   ✅ Found IMMA UKM\n";
            
            // Test loading relationships
            $immaUkm->load(['leader', 'activeMembers', 'achievements', 'publishedEvents']);
            echo "   ✅ Relationships loaded successfully\n";
            
        } else {
            echo "   ❌ IMMA UKM not found\n";
        }
    } catch (Exception $e) {
        echo "   ❌ IMMA UKM test error: " . $e->getMessage() . "\n";
        echo "   📋 Stack trace: " . $e->getTraceAsString() . "\n";
    }
    
    echo "\n=== DEBUG COMPLETED ===\n";
    
} catch (\Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    echo "📋 Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
