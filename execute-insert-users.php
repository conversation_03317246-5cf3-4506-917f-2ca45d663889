<?php

echo "=== INSERTING 5 USERS (2 ADMIN + 3 MAHASISWA) ===\n\n";

try {
    // Database connection
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected\n\n";
    
    // Read and execute SQL file
    $sql = file_get_contents('insert-5-users.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty lines and comments
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
            
            // Show progress for INSERT statements
            if (stripos($statement, 'INSERT INTO users') !== false) {
                // Extract name from INSERT statement
                if (preg_match("/VALUES\s*\(\s*'[^']*',\s*'([^']*)',/i", $statement, $matches)) {
                    echo "✅ Inserted user: {$matches[1]}\n";
                }
            }
        } catch (Exception $e) {
            echo "❌ Error executing statement: " . $e->getMessage() . "\n";
            echo "Statement: " . substr($statement, 0, 100) . "...\n\n";
        }
    }
    
    echo "\n📊 VERIFICATION:\n";
    
    // Verify users were inserted
    $stmt = $pdo->query("SELECT id, nim, name, email, role, status FROM users ORDER BY role DESC, id");
    $users = $stmt->fetchAll();
    
    echo "Total users inserted: " . count($users) . "\n\n";
    
    foreach ($users as $user) {
        echo "- {$user['name']} ({$user['nim']}) - {$user['role']} - {$user['status']}\n";
    }
    
    // Count by role
    echo "\n📈 COUNT BY ROLE:\n";
    $stmt = $pdo->query("SELECT role, COUNT(*) as jumlah FROM users WHERE status = 'active' GROUP BY role");
    $counts = $stmt->fetchAll();
    
    foreach ($counts as $count) {
        echo "- {$count['role']}: {$count['jumlah']} users\n";
    }
    
    // Total active users
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE status = 'active'");
    $total = $stmt->fetchColumn();
    
    echo "\n🎯 TOTAL ANGGOTA AKTIF: {$total}\n";
    echo "\n✅ Homepage should now show: Anggota Aktif = {$total}\n";
    
    echo "\n🔑 LOGIN CREDENTIALS:\n";
    echo "Admin 1: <EMAIL> / password\n";
    echo "Admin 2: <EMAIL> / password\n";
    echo "Student 1: <EMAIL> / password\n";
    echo "Student 2: <EMAIL> / password\n";
    echo "Student 3: <EMAIL> / password\n";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
