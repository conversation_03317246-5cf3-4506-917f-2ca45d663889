-- Insert Ma<PERSON>wa Users
-- Password for all users: pass123123 (hashed with bcrypt)

INSERT INTO users (name, email, nim, password, role, status, email_verified_at, created_at, updated_at) VALUES

('<PERSON><PERSON>', '<EMAIL>', '12345678901', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('<PERSON>ye<PERSON><PERSON>', '<EMAIL>', '12345678902', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('<PERSON>', '<EMAIL>', '12345678903', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Najla Ramadina Sulistyowati', '<EMAIL>', '12345678904', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Nabilla Alyvia', '<EMAIL>', '12345678905', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Aras Agita Fasya', '<EMAIL>', '12345678906', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Aufa Hafiy Andhika', '<EMAIL>', '12345678907', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Rahadian Nungki Saputra', '<EMAIL>', '12345678908', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Adit Kurniawan', '<EMAIL>', '12345678909', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Mikel Austin', '<EMAIL>', '12345678910', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Antonius Valentino', '<EMAIL>', '12345678911', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Abraham Arif Mulia', '<EMAIL>', '12345678912', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Fathan Mubina', '<EMAIL>', '12345678913', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW()),

('Mutiara Hani Demayanti', '<EMAIL>', '12345678914', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mahasiswa', 'active', NOW(), NOW(), NOW());

-- Check the result
SELECT COUNT(*) as total_users FROM users;
SELECT role, COUNT(*) as count FROM users GROUP BY role;
