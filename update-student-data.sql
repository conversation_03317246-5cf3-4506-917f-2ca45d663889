-- Update Student Data with <PERSON>, Major, and Batch
-- This will fix the issue where students don't appear in admin dashboard

-- Update <PERSON><PERSON>andra
UPDATE users SET 
    faculty = 'Fakultas Teknik Elektro',
    major = 'Teknik Informatika',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Ryemius Mar<PERSON>areta Siregar
UPDATE users SET 
    faculty = 'Fakultas Teknik Elektro',
    major = 'Sistem Informasi',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Amanda Riski Agustian
UPDATE users SET 
    faculty = 'Fakultas Ekonomi dan <PERSON>',
    major = 'Akuntansi',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Najla <PERSON>yowati
UPDATE users SET 
    faculty = 'Fakultas Komunikasi dan <PERSON>',
    major = '<PERSON><PERSON>munik<PERSON>',
    batch = '2023'
WHERE email = 'najla.ramadina.su<PERSON><PERSON><PERSON>@student.telkomuniversity.ac.id' AND role = 'student';

-- Update Nabilla Alyvia
UPDATE users SET 
    faculty = 'Fakultas Teknik Elektro',
    major = 'Teknik Informatika',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Aras Agita Fasya
UPDATE users SET 
    faculty = 'Fakultas Teknik Elektro',
    major = 'Teknik Komputer',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Aufa Hafiy Andhika
UPDATE users SET 
    faculty = 'Fakultas Teknik Elektro',
    major = 'Sistem Informasi',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Rahadian Nungki Saputra
UPDATE users SET 
    faculty = 'Fakultas Ekonomi dan Bisnis',
    major = 'Manajemen',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Adit Kurniawan
UPDATE users SET 
    faculty = 'Fakultas Teknik Elektro',
    major = 'Teknik Informatika',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Mikel Austin
UPDATE users SET 
    faculty = 'Fakultas Komunikasi dan Bisnis',
    major = 'Digital Marketing',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Antonius Valentino
UPDATE users SET 
    faculty = 'Fakultas Teknik Elektro',
    major = 'Teknik Informatika',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Abraham Arif Mulia
UPDATE users SET 
    faculty = 'Fakultas Ekonomi dan Bisnis',
    major = 'Akuntansi',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Fathan Mubina
UPDATE users SET 
    faculty = 'Fakultas Teknik Elektro',
    major = 'Sistem Informasi',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Update Mutiara Hani Demayanti
UPDATE users SET 
    faculty = 'Fakultas Komunikasi dan Bisnis',
    major = 'Ilmu Komunikasi',
    batch = '2023'
WHERE email = '<EMAIL>' AND role = 'student';

-- Check the results
SELECT 'Total Students' as info, COUNT(*) as count FROM users WHERE role = 'student'
UNION ALL
SELECT 'Students with Complete Data' as info, COUNT(*) as count FROM users WHERE role = 'student' AND faculty IS NOT NULL AND major IS NOT NULL AND batch IS NOT NULL;

-- Show updated student data
SELECT name, email, faculty, major, batch FROM users WHERE role = 'student' AND faculty IS NOT NULL ORDER BY name;
