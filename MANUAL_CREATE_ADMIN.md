# 🔑 MANUAL ADMIN CREATION GUIDE

## 📋 **CARA MEMBUAT AKUN ADMIN SECARA MANUAL**

### **Method 1: Menggunakan Database Tool (Recommended)**

#### **1. Buka Database Tool (phpMyAdmin/MySQL Workbench/etc)**

#### **2. Jalankan Query SQL Berikut:**

```sql
INSERT INTO users (
    nim, 
    name, 
    email, 
    password, 
    phone, 
    gender, 
    faculty, 
    major, 
    batch, 
    role, 
    status, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    'ADMIN001',
    'Administrator',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '************',
    'male',
    'Administrasi',
    'Sistem Informasi',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    password = '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    status = 'active',
    updated_at = NOW();
```

**Password Hash di atas adalah untuk password: `admin123`**

### **Method 2: Menggunakan Laravel Tinker**

#### **1. Buka Terminal dan Jalankan:**
```bash
php artisan tinker
```

#### **2. Paste Code Berikut di Tinker:**
```php
use App\Models\User;
use Illuminate\Support\Facades\Hash;

$admin = User::updateOrCreate(
    ['email' => '<EMAIL>'],
    [
        'nim' => 'ADMIN001',
        'name' => 'Administrator',
        'email' => '<EMAIL>',
        'password' => Hash::make('admin123'),
        'phone' => '************',
        'gender' => 'male',
        'faculty' => 'Administrasi',
        'major' => 'Sistem Informasi',
        'batch' => '2024',
        'role' => 'admin',
        'status' => 'active',
        'email_verified_at' => now(),
    ]
);

echo "Admin created: " . $admin->name . " (" . $admin->email . ")";
```

#### **3. Ketik `exit` untuk keluar dari tinker**

### **Method 3: Menggunakan Artisan Command**

#### **1. Jalankan Command:**
```bash
php artisan admin:create
```

#### **2. Ikuti Instruksi yang Muncul**

### **Method 4: Menggunakan Seeder**

#### **1. Jalankan Seeder:**
```bash
php artisan db:seed --class=AdminUserSeeder
```

## 🔑 **LOGIN CREDENTIALS**

Setelah admin berhasil dibuat, gunakan kredensial berikut untuk login:

### **📧 Email:** `<EMAIL>`
### **🔒 Password:** `admin123`
### **👤 Role:** `admin`
### **✅ Status:** `active`

## 🌐 **LOGIN URL**

Akses halaman login di: **http://localhost:8000/login**

## ✅ **VERIFIKASI ADMIN BERHASIL DIBUAT**

### **1. Cek Database:**
```sql
SELECT id, nim, name, email, role, status, created_at 
FROM users 
WHERE email = '<EMAIL>';
```

### **2. Test Login:**
- Buka http://localhost:8000/login
- Masukkan email: `<EMAIL>`
- Masukkan password: `admin123`
- Klik Login

### **3. Cek Dashboard Admin:**
- Setelah login berhasil, Anda akan diarahkan ke dashboard admin
- URL dashboard: http://localhost:8000/admin/dashboard

## 🛠️ **TROUBLESHOOTING**

### **Jika Login Gagal:**

#### **1. Cek Status User:**
```sql
UPDATE users 
SET status = 'active' 
WHERE email = '<EMAIL>';
```

#### **2. Reset Password:**
```sql
UPDATE users 
SET password = '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
WHERE email = '<EMAIL>';
```

#### **3. Verify Email:**
```sql
UPDATE users 
SET email_verified_at = NOW() 
WHERE email = '<EMAIL>';
```

### **Jika Role Tidak Benar:**
```sql
UPDATE users 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

## 📝 **ADDITIONAL TEST ACCOUNTS**

Anda juga bisa membuat akun test lainnya:

### **Test Student:**
```sql
INSERT INTO users (nim, name, email, password, phone, gender, faculty, major, batch, role, status, email_verified_at, created_at, updated_at) 
VALUES ('**********', 'Test Student', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '************', 'male', 'Informatika', 'Sistem Informasi', '2021', 'student', 'active', NOW(), NOW(), NOW());
```
**Login:** `<EMAIL>` | **Password:** `student123`

### **Test Ketua UKM:**
```sql
INSERT INTO users (nim, name, email, password, phone, gender, faculty, major, batch, role, status, email_verified_at, created_at, updated_at) 
VALUES ('**********', 'Test Ketua UKM', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '************', 'female', 'Informatika', 'Teknik Informatika', '2021', 'ketua_ukm', 'active', NOW(), NOW(), NOW());
```
**Login:** `<EMAIL>` | **Password:** `ketua123`

## 🎯 **SUMMARY**

### **✅ Admin Account Details:**
- **NIM:** ADMIN001
- **Name:** Administrator  
- **Email:** <EMAIL>
- **Password:** admin123
- **Role:** admin
- **Status:** active
- **Phone:** ************

### **✅ Access URLs:**
- **Login:** http://localhost:8000/login
- **Admin Dashboard:** http://localhost:8000/admin/dashboard
- **Admin Events:** http://localhost:8000/admin/events
- **Admin UKMs:** http://localhost:8000/admin/ukms

---

**🎉 ADMIN ACCOUNT SIAP DIGUNAKAN!**

Pilih salah satu method di atas untuk membuat admin account, lalu gunakan kredensial yang diberikan untuk login ke sistem.
