<?php

echo "=== TESTING REGISTRATION PERIOD FIELDS ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing registration period fields in Event model...\n";
    
    // Check if fields exist in fillable
    $event = new \App\Models\Event();
    $fillable = $event->getFillable();
    
    $hasRegistrationStart = in_array('registration_start', $fillable);
    $hasRegistrationEnd = in_array('registration_end', $fillable);
    
    echo "   registration_start in fillable: " . ($hasRegistrationStart ? 'Yes' : 'No') . "\n";
    echo "   registration_end in fillable: " . ($hasRegistrationEnd ? 'Yes' : 'No') . "\n";
    
    if ($hasRegistrationStart && $hasRegistrationEnd) {
        echo "   ✅ Registration period fields are fillable\n";
    } else {
        echo "   ❌ Registration period fields missing from fillable\n";
    }
    
    echo "2. Testing event creation with registration period...\n";
    
    // Find a UKM for testing
    $ukm = \App\Models\Ukm::first();
    if (!$ukm) {
        echo "   ❌ No UKM found for testing\n";
        exit;
    }
    
    echo "   ✅ Using UKM: {$ukm->name}\n";
    
    // Create test event with registration period
    $eventData = [
        'ukm_id' => $ukm->id,
        'title' => 'Test Event with Registration Period',
        'slug' => 'test-event-registration-period',
        'description' => 'Test event for registration period functionality',
        'start_datetime' => now()->addDays(10),
        'end_datetime' => now()->addDays(10)->addHours(3),
        'registration_start' => now()->addDays(1), // Registration starts in 1 day
        'registration_end' => now()->addDays(8),   // Registration ends 2 days before event
        'location' => 'Test Location',
        'type' => 'workshop',
        'status' => 'published',
        'registration_open' => true,
    ];
    
    $testEvent = \App\Models\Event::create($eventData);
    
    echo "   ✅ Event created with ID: {$testEvent->id}\n";
    echo "   Event start: {$testEvent->start_datetime}\n";
    echo "   Registration start: {$testEvent->registration_start}\n";
    echo "   Registration end: {$testEvent->registration_end}\n";
    
    echo "3. Testing isRegistrationOpen() method with periods...\n";
    
    // Test different scenarios
    $scenarios = [
        'Before registration starts' => [
            'registration_start' => now()->addDays(1),
            'registration_end' => now()->addDays(5),
            'expected' => false
        ],
        'During registration period' => [
            'registration_start' => now()->subDays(1),
            'registration_end' => now()->addDays(5),
            'expected' => true
        ],
        'After registration ends' => [
            'registration_start' => now()->subDays(5),
            'registration_end' => now()->subDays(1),
            'expected' => false
        ],
        'No registration period set' => [
            'registration_start' => null,
            'registration_end' => null,
            'expected' => true
        ]
    ];
    
    foreach ($scenarios as $scenario => $data) {
        echo "   Testing scenario: {$scenario}\n";
        
        $testEvent->update([
            'registration_start' => $data['registration_start'],
            'registration_end' => $data['registration_end'],
            'start_datetime' => now()->addDays(10), // Event in future
            'status' => 'published',
            'registration_open' => true,
        ]);
        
        $testEvent->refresh();
        $isOpen = $testEvent->isRegistrationOpen();
        
        echo "     Registration open: " . ($isOpen ? 'Yes' : 'No') . "\n";
        echo "     Expected: " . ($data['expected'] ? 'Yes' : 'No') . "\n";
        
        if ($isOpen === $data['expected']) {
            echo "     ✅ Scenario passed\n";
        } else {
            echo "     ❌ Scenario failed\n";
        }
        echo "\n";
    }
    
    echo "4. Testing form validation logic...\n";
    
    // Test validation scenarios
    $validationTests = [
        'Valid period' => [
            'start_datetime' => now()->addDays(10),
            'registration_start' => now()->addDays(1),
            'registration_end' => now()->addDays(8),
            'should_pass' => true
        ],
        'Registration start after event start' => [
            'start_datetime' => now()->addDays(5),
            'registration_start' => now()->addDays(6),
            'registration_end' => now()->addDays(8),
            'should_pass' => false
        ],
        'Registration end after event start' => [
            'start_datetime' => now()->addDays(5),
            'registration_start' => now()->addDays(1),
            'registration_end' => now()->addDays(6),
            'should_pass' => false
        ],
        'Registration end before registration start' => [
            'start_datetime' => now()->addDays(10),
            'registration_start' => now()->addDays(5),
            'registration_end' => now()->addDays(3),
            'should_pass' => false
        ]
    ];
    
    foreach ($validationTests as $testName => $testData) {
        echo "   Testing: {$testName}\n";
        
        // Simulate validation rules
        $rules = [
            'start_datetime' => 'required|date',
            'registration_start' => 'nullable|date|before:start_datetime',
            'registration_end' => 'nullable|date|before:start_datetime|after:registration_start',
        ];
        
        $data = [
            'start_datetime' => $testData['start_datetime']->format('Y-m-d H:i:s'),
            'registration_start' => $testData['registration_start']?->format('Y-m-d H:i:s'),
            'registration_end' => $testData['registration_end']?->format('Y-m-d H:i:s'),
        ];
        
        try {
            $validator = \Illuminate\Support\Facades\Validator::make($data, $rules);
            $passes = $validator->passes();
            
            echo "     Validation result: " . ($passes ? 'Pass' : 'Fail') . "\n";
            echo "     Expected: " . ($testData['should_pass'] ? 'Pass' : 'Fail') . "\n";
            
            if ($passes === $testData['should_pass']) {
                echo "     ✅ Validation test passed\n";
            } else {
                echo "     ❌ Validation test failed\n";
                if (!$passes) {
                    echo "     Errors: " . implode(', ', $validator->errors()->all()) . "\n";
                }
            }
        } catch (Exception $e) {
            echo "     ❌ Validation error: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
    echo "5. Testing view form fields...\n";
    
    // Check if view files contain the new fields
    $createViewPath = 'resources/views/ketua-ukm/events/create.blade.php';
    $editViewPath = 'resources/views/ketua-ukm/events/edit.blade.php';
    
    if (file_exists($createViewPath)) {
        $createContent = file_get_contents($createViewPath);
        $hasRegistrationStartField = strpos($createContent, 'registration_start') !== false;
        $hasRegistrationEndField = strpos($createContent, 'registration_end') !== false;
        
        echo "   Create view has registration_start field: " . ($hasRegistrationStartField ? 'Yes' : 'No') . "\n";
        echo "   Create view has registration_end field: " . ($hasRegistrationEndField ? 'Yes' : 'No') . "\n";
        
        if ($hasRegistrationStartField && $hasRegistrationEndField) {
            echo "   ✅ Create view has registration period fields\n";
        } else {
            echo "   ❌ Create view missing registration period fields\n";
        }
    } else {
        echo "   ❌ Create view file not found\n";
    }
    
    if (file_exists($editViewPath)) {
        $editContent = file_get_contents($editViewPath);
        $hasRegistrationStartField = strpos($editContent, 'registration_start') !== false;
        $hasRegistrationEndField = strpos($editContent, 'registration_end') !== false;
        
        echo "   Edit view has registration_start field: " . ($hasRegistrationStartField ? 'Yes' : 'No') . "\n";
        echo "   Edit view has registration_end field: " . ($hasRegistrationEndField ? 'Yes' : 'No') . "\n";
        
        if ($hasRegistrationStartField && $hasRegistrationEndField) {
            echo "   ✅ Edit view has registration period fields\n";
        } else {
            echo "   ❌ Edit view missing registration period fields\n";
        }
    } else {
        echo "   ❌ Edit view file not found\n";
    }
    
    echo "6. Testing JavaScript validation...\n";
    
    // Check if JavaScript validation exists
    if (file_exists($createViewPath)) {
        $createContent = file_get_contents($createViewPath);
        $hasJSValidation = strpos($createContent, 'registrationStart') !== false && 
                          strpos($createContent, 'registrationEnd') !== false;
        
        echo "   Create view has JavaScript validation: " . ($hasJSValidation ? 'Yes' : 'No') . "\n";
        
        if ($hasJSValidation) {
            echo "   ✅ JavaScript validation implemented\n";
        } else {
            echo "   ❌ JavaScript validation missing\n";
        }
    }
    
    echo "7. Testing complete registration workflow...\n";
    
    // Test complete workflow
    $workflowEvent = \App\Models\Event::create([
        'ukm_id' => $ukm->id,
        'title' => 'Workflow Test Event',
        'slug' => 'workflow-test-event',
        'description' => 'Test event for complete workflow',
        'start_datetime' => now()->addDays(5),
        'end_datetime' => now()->addDays(5)->addHours(3),
        'registration_start' => now()->subHours(1), // Started 1 hour ago
        'registration_end' => now()->addDays(3),    // Ends in 3 days
        'location' => 'Test Location',
        'type' => 'workshop',
        'status' => 'published',
        'registration_open' => true,
    ]);
    
    echo "   Created workflow test event\n";
    echo "   Registration should be open: " . ($workflowEvent->isRegistrationOpen() ? 'Yes' : 'No') . "\n";
    
    // Test with student
    $student = \App\Models\User::where('role', 'student')->first();
    if ($student) {
        echo "   Testing with student: {$student->name}\n";
        
        // Check if student can register
        $canRegister = $workflowEvent->isRegistrationOpen();
        echo "   Student can register: " . ($canRegister ? 'Yes' : 'No') . "\n";
        
        if ($canRegister) {
            echo "   ✅ Registration workflow working correctly\n";
        } else {
            echo "   ❌ Registration workflow issue\n";
        }
    }
    
    echo "8. Cleanup test data...\n";
    
    // Clean up test events
    $testEvent->delete();
    $workflowEvent->delete();
    
    echo "   ✅ Test data cleaned up\n";
    
    echo "\n=== REGISTRATION PERIOD FIELDS TEST COMPLETED ===\n";
    echo "✅ Registration period fields functionality verified!\n";
    echo "\nKey Features Added:\n";
    echo "🔧 FIELD: registration_start - Tanggal mulai pendaftaran\n";
    echo "🔧 FIELD: registration_end - Tanggal berakhir pendaftaran\n";
    echo "🔧 VALIDATION: registration_start must be before event start\n";
    echo "🔧 VALIDATION: registration_end must be before event start and after registration_start\n";
    echo "🔧 LOGIC: isRegistrationOpen() considers registration period\n";
    echo "🔧 UI: Form fields added to create and edit views\n";
    echo "🔧 JS: Client-side validation for date relationships\n";
    echo "\nExpected Behavior:\n";
    echo "✅ Ketua UKM can set registration period when creating/editing events\n";
    echo "✅ Registration only open during specified period\n";
    echo "✅ Form validation prevents invalid date combinations\n";
    echo "✅ JavaScript provides real-time validation feedback\n";
    echo "✅ Default values suggested based on event dates\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
