<?php
echo "=== CHECKING UKM LOGOS ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check UKMs and their logos
    echo "📊 UKM Logo Status:\n";
    $result = $pdo->query("SELECT id, name, slug, logo FROM ukms ORDER BY name");
    $count = 0;
    $withLogo = 0;
    $withoutLogo = 0;
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $count++;
        $hasLogo = !empty($row['logo']);
        $status = $hasLogo ? '✅' : '❌';
        
        echo "   {$count}. {$status} {$row['name']}\n";
        echo "      📁 Slug: {$row['slug']}\n";
        
        if ($hasLogo) {
            echo "      🖼️  Logo: {$row['logo']}\n";
            echo "      🌐 URL: http://localhost:8000/storage/{$row['logo']}\n";
            
            // Check if file exists
            $filePath = "storage/app/public/{$row['logo']}";
            if (file_exists($filePath)) {
                echo "      📂 File: EXISTS\n";
            } else {
                echo "      📂 File: MISSING\n";
            }
            $withLogo++;
        } else {
            echo "      🖼️  Logo: NOT SET\n";
            $withoutLogo++;
        }
        echo "\n";
    }
    
    echo "📈 Summary:\n";
    echo "   Total UKMs: {$count}\n";
    echo "   With Logo: {$withLogo}\n";
    echo "   Without Logo: {$withoutLogo}\n";
    
    if ($withoutLogo > 0) {
        echo "\n📋 UKMs without logos:\n";
        $result = $pdo->query("SELECT name, slug FROM ukms WHERE logo IS NULL OR logo = '' ORDER BY name");
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "   • {$row['name']} ({$row['slug']})\n";
        }
        
        echo "\n🔧 To fix missing logos:\n";
        echo "1. Run: php update-ukm-logos-simple.php\n";
        echo "2. Or use SQL: add-ukm-logos.sql\n";
        echo "3. Upload actual logos via admin interface\n";
    }
    
    echo "\n🌐 Test URLs:\n";
    echo "   Admin UKMs: http://localhost:8000/admin/ukms\n";
    echo "   Public UKMs: http://localhost:8000/ukm\n";
    
    // Check placeholder logo files
    echo "\n📁 Placeholder Logo Files:\n";
    $logoDir = 'storage/app/public/ukms/logos';
    $placeholderLogos = [
        'badminton-logo.png',
        'dpm-logo.png',
        'esport-logo.png',
        'futsal-logo.png',
        'imma-logo.png',
        'mapala-logo.png',
        'pmk-logo.png',
        'seni-budaya-logo.png',
        'sistem-informasi-logo.png'
    ];
    
    foreach ($placeholderLogos as $logo) {
        $filePath = $logoDir . '/' . $logo;
        if (file_exists($filePath)) {
            $size = filesize($filePath);
            echo "   ✅ {$logo} ({$size} bytes)\n";
        } else {
            echo "   ❌ {$logo} (missing)\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== CHECK COMPLETE ===\n";
?>
