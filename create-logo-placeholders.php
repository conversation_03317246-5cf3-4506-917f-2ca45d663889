<?php
echo "=== CREATING LOGO PLACEHOLDERS ===\n\n";

try {
    // Create logos directory if it doesn't exist
    $logoDir = 'storage/app/public/ukms/logos';
    if (!file_exists($logoDir)) {
        mkdir($logoDir, 0755, true);
        echo "✅ Created logos directory: {$logoDir}\n";
    }
    
    // UKM logo mappings with colors
    $ukms = [
        'badminton' => ['name' => 'BADMINTON', 'color' => '#3B82F6'],
        'dpm' => ['name' => 'DPM', 'color' => '#1F2937'],
        'esport' => ['name' => 'ESPORT', 'color' => '#8B5CF6'],
        'futsal' => ['name' => 'FUTSAL', 'color' => '#10B981'],
        'imma' => ['name' => 'IMMA', 'color' => '#059669'],
        'mapala' => ['name' => 'MAPALA', 'color' => '#92400E'],
        'pmk' => ['name' => 'PMK', 'color' => '#DC2626'],
        'seni-budaya' => ['name' => 'SENI', 'color' => '#F59E0B'],
        'sistem-informasi' => ['name' => 'SI', 'color' => '#6366F1']
    ];
    
    echo "Creating placeholder logo files...\n\n";
    
    $created = 0;
    
    foreach ($ukms as $slug => $data) {
        $logoFilename = $slug . '-logo.png';
        $logoPath = $logoDir . '/' . $logoFilename;
        
        // Create a simple SVG logo and convert to PNG (placeholder)
        $svgContent = createSimpleLogo($data['name'], $data['color']);
        
        // For now, just create empty files as placeholders
        // In a real scenario, you would use an image library like GD or ImageMagick
        if (file_put_contents($logoPath, '')) {
            echo "   ✅ Created placeholder: {$logoFilename}\n";
            $created++;
        } else {
            echo "   ❌ Failed to create: {$logoFilename}\n";
        }
    }
    
    echo "\n=== RESULT ===\n";
    echo "✅ Created {$created} placeholder logo files\n";
    
    echo "\n📋 Next Steps:\n";
    echo "1. Replace placeholder files with actual UKM logos\n";
    echo "2. Run SQL script to update database: add-ukm-logos.sql\n";
    echo "3. Or run PHP script: php add-ukm-logos.php\n";
    echo "4. Check admin page: http://localhost:8000/admin/ukms\n";
    
    echo "\n📁 Logo Files Created:\n";
    foreach ($ukms as $slug => $data) {
        $logoFilename = $slug . '-logo.png';
        echo "   • {$logoFilename} ({$data['name']})\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

function createSimpleLogo($text, $color) {
    // Simple SVG template for logo
    return "
    <svg width='200' height='200' xmlns='http://www.w3.org/2000/svg'>
        <rect width='200' height='200' fill='{$color}' rx='20'/>
        <text x='100' y='110' font-family='Arial, sans-serif' font-size='24' font-weight='bold' 
              text-anchor='middle' fill='white'>{$text}</text>
    </svg>";
}

echo "\n=== PLACEHOLDER CREATION COMPLETE ===\n";
?>
