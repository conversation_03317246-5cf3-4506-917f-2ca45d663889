# 🔧 INSTRUKSI MEMPERBAIKI ROLE ENUM

## Masalah
Role `ketua_ukm` tidak bisa disimpan karena database enum hanya mendukung `student` dan `admin`.

## Solusi

### Langkah 1: Buka MySQL Client
Buka command prompt atau MySQL Workbench dan jalankan:

```sql
USE ukm_web_db;
```

### Langkah 2: Update Enum Role
Jalankan SQL berikut:

```sql
ALTER TABLE users MODIFY COLUMN role ENUM('student', 'ketua_ukm', 'admin') DEFAULT 'student';
```

### Langkah 3: Verifikasi Perubahan
Cek apakah enum sudah berubah:

```sql
SHOW COLUMNS FROM users LIKE 'role';
```

Hasilnya harus menunjukkan:
```
enum('student','ketua_ukm','admin')
```

### Langkah 4: Test Role Assignment
Setelah menjalankan SQL di atas, jalankan file batch ini:

```batch
fix-role-manual.bat
```

## Per<PERSON>ikan yang Sudah Dilakukan

1. ✅ **Controller Update**: Menambahkan `role` ke validation dan update data
2. ✅ **Session Refresh**: Menambahkan refresh session untuk user yang sedang login
3. ✅ **Cache Clearing**: Menambahkan clear cache untuk user data
4. ✅ **Middleware**: Menambahkan middleware untuk refresh user role

## Setelah SQL Dijalankan

Setelah menjalankan SQL di atas, masalah role persistence akan teratasi:

- ✅ Role `ketua_ukm` bisa disimpan
- ✅ Role tidak akan reset setelah refresh
- ✅ Perubahan role langsung terlihat

## Troubleshooting

Jika masih ada masalah:

1. **Clear Laravel Cache**:
   ```bash
   php artisan config:clear
   php artisan cache:clear
   php artisan view:clear
   ```

2. **Restart Web Server**

3. **Hard Refresh Browser** (Ctrl+F5)

## Verifikasi Final

1. Login sebagai admin
2. Edit role mahasiswa menjadi "Ketua UKM"
3. Simpan
4. Refresh halaman
5. Role harus tetap "Ketua UKM"
