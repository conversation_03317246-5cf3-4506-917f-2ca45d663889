<?php

echo "=== CHECKING GD EXTENSION AND TEMPLATE CERTIFICATE ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking GD Extension status...\n";
    
    // Check GD extension
    $hasGD = extension_loaded('gd');
    echo "   GD Extension: " . ($hasGD ? '✅ INSTALLED' : '❌ NOT INSTALLED') . "\n";
    
    if ($hasGD) {
        $gdInfo = gd_info();
        echo "   GD Version: " . $gdInfo['GD Version'] . "\n";
        echo "   JPEG Support: " . ($gdInfo['JPEG Support'] ? '✅ Yes' : '❌ No') . "\n";
        echo "   PNG Support: " . ($gdInfo['PNG Support'] ? '✅ Yes' : '❌ No') . "\n";
        echo "   GIF Support: " . (isset($gdInfo['GIF Support']) && $gdInfo['GIF Support'] ? '✅ Yes' : '❌ No') . "\n";
        echo "   FreeType Support: " . (isset($gdInfo['FreeType Support']) && $gdInfo['FreeType Support'] ? '✅ Yes' : '❌ No') . "\n";
    } else {
        echo "\n   ⚠️  GD Extension is not installed!\n";
        echo "   To enable GD extension:\n";
        echo "   1. Edit php.ini file\n";
        echo "   2. Find line: ;extension=gd\n";
        echo "   3. Remove semicolon: extension=gd\n";
        echo "   4. Restart Apache/web server\n";
        echo "   5. Run this script again\n\n";
    }
    
    echo "2. Checking PHP configuration...\n";
    
    $memoryLimit = ini_get('memory_limit');
    $maxExecutionTime = ini_get('max_execution_time');
    $uploadMaxFilesize = ini_get('upload_max_filesize');
    $postMaxSize = ini_get('post_max_size');
    
    echo "   Memory Limit: {$memoryLimit}\n";
    echo "   Max Execution Time: {$maxExecutionTime} seconds\n";
    echo "   Upload Max Filesize: {$uploadMaxFilesize}\n";
    echo "   Post Max Size: {$postMaxSize}\n";
    
    echo "3. Testing template certificate with GD...\n";
    
    // Find event with certificate template
    $event = \App\Models\Event::whereNotNull('certificate_template')->first();
    
    if (!$event) {
        echo "   ❌ No event with certificate template found\n";
        echo "   Please upload a certificate template first\n";
        exit;
    }
    
    echo "   ✅ Using event: {$event->title}\n";
    echo "   Template: {$event->certificate_template}\n";
    
    $templatePath = storage_path('app/public/' . $event->certificate_template);
    if (!file_exists($templatePath)) {
        echo "   ❌ Template file not found: {$templatePath}\n";
        exit;
    }
    
    echo "   ✅ Template file exists\n";
    
    $fileSize = filesize($templatePath);
    $mimeType = mime_content_type($templatePath);
    echo "   File size: " . round($fileSize / 1024, 2) . " KB\n";
    echo "   MIME type: {$mimeType}\n";
    
    // Test image processing with GD
    if ($hasGD) {
        echo "4. Testing GD image processing...\n";
        
        try {
            // Test creating image from file
            $imageResource = null;
            
            switch ($mimeType) {
                case 'image/jpeg':
                    $imageResource = imagecreatefromjpeg($templatePath);
                    break;
                case 'image/png':
                    $imageResource = imagecreatefrompng($templatePath);
                    break;
                case 'image/gif':
                    $imageResource = imagecreatefromgif($templatePath);
                    break;
                default:
                    echo "   ⚠️  Unsupported image type: {$mimeType}\n";
            }
            
            if ($imageResource) {
                $width = imagesx($imageResource);
                $height = imagesy($imageResource);
                echo "   ✅ Image loaded successfully\n";
                echo "   Image dimensions: {$width} x {$height} pixels\n";
                
                // Test base64 encoding
                ob_start();
                switch ($mimeType) {
                    case 'image/jpeg':
                        imagejpeg($imageResource);
                        break;
                    case 'image/png':
                        imagepng($imageResource);
                        break;
                    case 'image/gif':
                        imagegif($imageResource);
                        break;
                }
                $imageData = ob_get_contents();
                ob_end_clean();
                
                $base64 = base64_encode($imageData);
                echo "   ✅ Base64 encoding successful\n";
                echo "   Base64 length: " . strlen($base64) . " characters\n";
                
                imagedestroy($imageResource);
            } else {
                echo "   ❌ Failed to load image with GD\n";
            }
            
        } catch (\Exception $e) {
            echo "   ❌ GD image processing failed: " . $e->getMessage() . "\n";
        }
    }
    
    echo "5. Testing certificate generation with template...\n";
    
    // Find verified attendance
    $attendance = $event->attendances()
                       ->where('verification_status', 'verified')
                       ->where('status', 'present')
                       ->first();
    
    if (!$attendance) {
        echo "   Creating test verified attendance...\n";
        
        $student = \App\Models\User::where('role', 'student')->first();
        if (!$student) {
            echo "   ❌ No student found\n";
            exit;
        }
        
        // Create registration
        $registration = \App\Models\EventRegistration::firstOrCreate([
            'user_id' => $student->id,
            'event_id' => $event->id,
        ], [
            'status' => 'approved',
            'approved_at' => now(),
        ]);
        
        // Create verified attendance
        $attendance = \App\Models\EventAttendance::create([
            'event_id' => $event->id,
            'event_registration_id' => $registration->id,
            'user_id' => $student->id,
            'status' => 'present',
            'verification_status' => 'verified',
            'verified_at' => now(),
            'verified_by' => 1,
            'submitted_at' => now(),
        ]);
        
        echo "   ✅ Created verified attendance for {$student->name}\n";
    } else {
        echo "   ✅ Using existing verified attendance for {$attendance->user->name}\n";
    }
    
    echo "6. Testing certificate service...\n";
    
    $certificateService = app(\App\Services\CertificateService::class);
    
    try {
        // Clean up any existing certificate
        if ($attendance->certificate_file) {
            \Illuminate\Support\Facades\Storage::disk('public')->delete($attendance->certificate_file);
            $attendance->update(['certificate_file' => null, 'certificate_generated' => false]);
        }
        
        echo "   Generating certificate with template...\n";
        $filename = $certificateService->generateCertificate($attendance);
        
        echo "   ✅ Certificate generated successfully\n";
        echo "   Filename: {$filename}\n";
        
        // Check if file exists and size
        $certificateExists = \Illuminate\Support\Facades\Storage::disk('public')->exists($filename);
        echo "   Certificate file exists: " . ($certificateExists ? 'Yes' : 'No') . "\n";
        
        if ($certificateExists) {
            $fileSize = \Illuminate\Support\Facades\Storage::disk('public')->size($filename);
            echo "   Certificate size: " . round($fileSize / 1024, 2) . " KB\n";
            
            if ($fileSize > 20000) { // More than 20KB indicates template image included
                echo "   ✅ Certificate appears to include template image\n";
            } elseif ($fileSize > 5000) {
                echo "   ⚠️  Certificate has medium size (may or may not include template)\n";
            } else {
                echo "   ❌ Certificate is small (likely no template image)\n";
            }
        }
        
        // Test download
        echo "   Testing certificate download...\n";
        $downloadResponse = $certificateService->downloadCertificate($attendance);
        echo "   ✅ Certificate download response generated\n";
        
    } catch (\Exception $e) {
        echo "   ❌ Certificate generation failed: " . $e->getMessage() . "\n";
        echo "   Error details: " . $e->getTraceAsString() . "\n";
    }
    
    echo "7. Recommendations...\n";
    
    if (!$hasGD) {
        echo "   🔧 ENABLE GD EXTENSION:\n";
        echo "   1. Edit php.ini file\n";
        echo "   2. Find: ;extension=gd\n";
        echo "   3. Change to: extension=gd\n";
        echo "   4. Restart Apache\n";
        echo "   5. Verify with phpinfo()\n";
    } else {
        echo "   ✅ GD Extension is available\n";
        
        if (isset($fileSize) && $fileSize > 20000) {
            echo "   ✅ Template certificate generation working\n";
        } else {
            echo "   ⚠️  Template may not be displaying properly\n";
            echo "   💡 Try different image formats (JPG, PNG)\n";
            echo "   💡 Ensure image is not too large (< 5MB)\n";
            echo "   💡 Check image file permissions\n";
        }
    }
    
    echo "8. Template image requirements...\n";
    
    echo "   Recommended template specifications:\n";
    echo "   📐 Dimensions: 1754 x 1240 pixels (A4 landscape, 300 DPI)\n";
    echo "   📁 Format: JPG or PNG\n";
    echo "   📏 File size: 500KB - 5MB\n";
    echo "   🎨 Design: Leave space in center for participant name\n";
    echo "   🖼️  Quality: High resolution for printing\n";
    
    echo "9. Testing different DomPDF options...\n";
    
    if ($hasGD) {
        echo "   Recommended DomPDF options for template certificates:\n";
        echo "   - isHtml5ParserEnabled: true\n";
        echo "   - isPhpEnabled: false\n";
        echo "   - defaultFont: Arial or Times-Roman\n";
        echo "   - dpi: 300 (high quality)\n";
        echo "   - debugKeepTemp: false\n";
        echo "   - chroot: storage path for security\n";
    }
    
    echo "10. Cleanup...\n";
    
    // Clean up test files
    if (isset($filename) && $filename) {
        \Illuminate\Support\Facades\Storage::disk('public')->delete($filename);
        echo "   ✅ Cleaned up test certificate\n";
    }
    
    echo "\n=== GD EXTENSION AND TEMPLATE CHECK COMPLETED ===\n";
    
    if ($hasGD) {
        echo "✅ GD Extension is available!\n";
        echo "🎯 Template certificate generation should work\n";
        echo "📄 PDF should display template background images\n";
        echo "🎨 Professional certificates with UKM branding possible\n";
    } else {
        echo "❌ GD Extension is NOT available\n";
        echo "🔧 Please enable GD extension in php.ini\n";
        echo "⚠️  Template images will not display without GD\n";
        echo "💡 CSS-only certificates available as fallback\n";
    }
    
    echo "\nNext steps:\n";
    if (!$hasGD) {
        echo "1. Edit php.ini and enable GD extension\n";
        echo "2. Restart Apache/web server\n";
        echo "3. Run this script again to verify\n";
        echo "4. Test certificate generation\n";
    } else {
        echo "1. Upload high-quality certificate templates\n";
        echo "2. Test certificate generation\n";
        echo "3. Verify template images appear in PDF\n";
        echo "4. Adjust template positioning if needed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
