<?php
/**
 * Test script to verify registration form faculty and major options
 */

echo "=== Test Registration Form Faculty and Major Options ===\n\n";

// Expected faculty options
$expectedFaculties = [
    'Fakultas Teknik Elektro',
    'Fakultas Rekayasa Industri', 
    'Fakultas Informatika',
    'Fakultas Industri Kreatif'
];

// Expected major options per faculty
$expectedMajors = [
    'Fakultas Teknik Elektro' => [
        'Program Studi D3 Teknik Telekomunikasi',
        'Program Studi S1 Teknik Telekomunikasi'
    ],
    'Fakultas Informatika' => [
        'Program Studi S1 Teknologi Informasi'
    ],
    'Fakultas Rekayasa Industri' => [
        'Program Studi S1 Sistem Informasi'
    ],
    'Fakultas Industri Kreatif' => [
        'Program Studi S1 Desain Komunikasi Visual'
    ]
];

echo "Expected Faculties:\n";
foreach ($expectedFaculties as $faculty) {
    echo "- $faculty\n";
}

echo "\nExpected Majors per Faculty:\n";
foreach ($expectedMajors as $faculty => $majors) {
    echo "\n$faculty:\n";
    foreach ($majors as $major) {
        echo "  - $major\n";
    }
}

echo "\n=== Test completed ===\n";
echo "Please check the registration form at: http://127.0.0.1:8000/register\n";
echo "The faculty dropdown should only show the 4 faculties listed above.\n";
echo "The major dropdown should show the corresponding programs when a faculty is selected.\n";
