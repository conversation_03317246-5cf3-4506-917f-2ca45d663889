<?php

echo "=== SETUP FRESH DATABASE ===\n";

// Database connection details
$host = '127.0.0.1';
$username = 'root';
$password = '';
$newDbName = 'ukmwebv';

try {
    // 1. Connect to MySQL (without database)
    echo "1. Connecting to MySQL server...\n";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ MySQL connection: SUCCESS\n";
    
    // 2. Drop database if exists and create new one
    echo "2. Creating fresh database '$newDbName'...\n";
    
    try {
        $pdo->exec("DROP DATABASE IF EXISTS $newDbName");
        echo "   ✅ Old database dropped (if existed)\n";
        
        $pdo->exec("CREATE DATABASE $newDbName CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "   ✅ New database '$newDbName' created\n";
        
    } catch (Exception $e) {
        echo "   ❌ Database creation error: " . $e->getMessage() . "\n";
        exit;
    }
    
    // 3. Connect to the new database
    echo "3. Connecting to new database...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$newDbName", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ Connected to '$newDbName'\n";
    
    // 4. Create users table
    echo "4. Creating users table...\n";
    
    $createUsersTable = "
    CREATE TABLE users (
        id bigint unsigned NOT NULL AUTO_INCREMENT,
        nim varchar(255) NOT NULL,
        name varchar(255) NOT NULL,
        email varchar(255) NOT NULL,
        email_verified_at timestamp NULL DEFAULT NULL,
        password varchar(255) NOT NULL,
        phone varchar(255) DEFAULT NULL,
        gender enum('male','female') NOT NULL,
        faculty varchar(255) NOT NULL,
        major varchar(255) NOT NULL,
        batch varchar(255) NOT NULL,
        role enum('admin','student','ketua_ukm') NOT NULL DEFAULT 'student',
        status enum('active','inactive','suspended','pending') NOT NULL DEFAULT 'pending',
        remember_token varchar(100) DEFAULT NULL,
        created_at timestamp NULL DEFAULT NULL,
        updated_at timestamp NULL DEFAULT NULL,
        PRIMARY KEY (id),
        UNIQUE KEY users_email_unique (email),
        UNIQUE KEY users_nim_unique (nim)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createUsersTable);
    echo "   ✅ Users table created\n";
    
    // 5. Create other essential tables
    echo "5. Creating other essential tables...\n";
    
    // Sessions table
    $createSessionsTable = "
    CREATE TABLE sessions (
        id varchar(255) NOT NULL,
        user_id bigint unsigned DEFAULT NULL,
        ip_address varchar(45) DEFAULT NULL,
        user_agent text,
        payload longtext NOT NULL,
        last_activity int NOT NULL,
        PRIMARY KEY (id),
        KEY sessions_user_id_index (user_id),
        KEY sessions_last_activity_index (last_activity)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createSessionsTable);
    echo "   ✅ Sessions table created\n";
    
    // Cache table
    $createCacheTable = "
    CREATE TABLE cache (
        `key` varchar(255) NOT NULL,
        value mediumtext NOT NULL,
        expiration int NOT NULL,
        PRIMARY KEY (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createCacheTable);
    echo "   ✅ Cache table created\n";
    
    // Cache locks table
    $createCacheLocksTable = "
    CREATE TABLE cache_locks (
        `key` varchar(255) NOT NULL,
        owner varchar(255) NOT NULL,
        expiration int NOT NULL,
        PRIMARY KEY (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createCacheLocksTable);
    echo "   ✅ Cache locks table created\n";
    
    // Jobs table
    $createJobsTable = "
    CREATE TABLE jobs (
        id bigint unsigned NOT NULL AUTO_INCREMENT,
        queue varchar(255) NOT NULL,
        payload longtext NOT NULL,
        attempts tinyint unsigned NOT NULL,
        reserved_at int unsigned DEFAULT NULL,
        available_at int unsigned NOT NULL,
        created_at int unsigned NOT NULL,
        PRIMARY KEY (id),
        KEY jobs_queue_index (queue)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createJobsTable);
    echo "   ✅ Jobs table created\n";
    
    // Job batches table
    $createJobBatchesTable = "
    CREATE TABLE job_batches (
        id varchar(255) NOT NULL,
        name varchar(255) NOT NULL,
        total_jobs int NOT NULL,
        pending_jobs int NOT NULL,
        failed_jobs int NOT NULL,
        failed_job_ids longtext NOT NULL,
        options mediumtext,
        cancelled_at int DEFAULT NULL,
        created_at int NOT NULL,
        finished_at int DEFAULT NULL,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createJobBatchesTable);
    echo "   ✅ Job batches table created\n";
    
    // Failed jobs table
    $createFailedJobsTable = "
    CREATE TABLE failed_jobs (
        id bigint unsigned NOT NULL AUTO_INCREMENT,
        uuid varchar(255) NOT NULL,
        connection text NOT NULL,
        queue text NOT NULL,
        payload longtext NOT NULL,
        exception longtext NOT NULL,
        failed_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY failed_jobs_uuid_unique (uuid)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createFailedJobsTable);
    echo "   ✅ Failed jobs table created\n";
    
    // 6. Insert admin user
    echo "6. Creating admin user...\n";
    
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (
            nim, name, email, password, phone, gender, faculty, major, batch,
            role, status, email_verified_at, created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
        )
    ");
    
    $stmt->execute([
        'ADMIN001',
        'Administrator',
        '<EMAIL>',
        $hashedPassword,
        '081234567890',
        'male',
        'Administrasi',
        'Sistem Informasi',
        '2024',
        'admin',
        'active',
        date('Y-m-d H:i:s')
    ]);
    
    echo "   ✅ Admin user created\n";
    
    // 7. Insert test student
    echo "7. Creating test student...\n";
    
    $hashedPassword = password_hash('student123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (
            nim, name, email, password, phone, gender, faculty, major, batch,
            role, status, email_verified_at, created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
        )
    ");
    
    $stmt->execute([
        '1301210001',
        'Test Student',
        '<EMAIL>',
        $hashedPassword,
        '081234567891',
        'male',
        'Informatika',
        'Sistem Informasi',
        '2021',
        'student',
        'active',
        date('Y-m-d H:i:s')
    ]);
    
    echo "   ✅ Test student created\n";
    
    // 8. Insert test ketua UKM
    echo "8. Creating test ketua UKM...\n";
    
    $hashedPassword = password_hash('ketua123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (
            nim, name, email, password, phone, gender, faculty, major, batch,
            role, status, email_verified_at, created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
        )
    ");
    
    $stmt->execute([
        '1301210002',
        'Test Ketua UKM',
        '<EMAIL>',
        $hashedPassword,
        '081234567892',
        'female',
        'Informatika',
        'Teknik Informatika',
        '2021',
        'ketua_ukm',
        'active',
        date('Y-m-d H:i:s')
    ]);
    
    echo "   ✅ Test ketua UKM created\n";
    
    // 9. Verify users
    echo "9. Verifying created users...\n";
    
    $stmt = $pdo->query("SELECT id, nim, name, email, role, status FROM users ORDER BY id");
    while ($user = $stmt->fetch()) {
        echo "   - {$user['name']} ({$user['email']}) - {$user['role']} - {$user['status']}\n";
    }
    
    echo "\n=== DATABASE SETUP COMPLETED ===\n";
    echo "✅ Database '$newDbName' created successfully!\n";
    echo "✅ Essential tables created\n";
    echo "✅ Test users created\n";
    
    echo "\n🔑 LOGIN CREDENTIALS:\n";
    echo "📧 Admin: <EMAIL> | Password: admin123\n";
    echo "📧 Student: <EMAIL> | Password: student123\n";
    echo "📧 Ketua UKM: <EMAIL> | Password: ketua123\n";
    
    echo "\n🌐 LOGIN URL: http://localhost:8000/login\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
