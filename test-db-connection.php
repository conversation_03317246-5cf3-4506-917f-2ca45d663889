<?php

echo "=== DATABASE CONNECTION DIAGNOSTIC ===\n\n";

// Test 1: Basic PHP MySQL extension
echo "1. Testing PHP MySQL Extensions:\n";
echo "   - mysqli: " . (extension_loaded('mysqli') ? '✅ Available' : '❌ Not available') . "\n";
echo "   - pdo_mysql: " . (extension_loaded('pdo_mysql') ? '✅ Available' : '❌ Not available') . "\n";
echo "   - pdo: " . (extension_loaded('pdo') ? '✅ Available' : '❌ Not available') . "\n\n";

// Test 2: Database connection parameters
$configs = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'database' => 'ukmwebv',
    'username' => 'root',
    'password' => ''
];

echo "2. Testing Connection Parameters:\n";
foreach ($configs as $key => $value) {
    echo "   - {$key}: " . ($value ?: '(empty)') . "\n";
}
echo "\n";

// Test 3: Test connection with different methods
echo "3. Testing Database Connection:\n\n";

// Method 1: PDO
echo "   Method 1: PDO Connection\n";
try {
    $dsn = "mysql:host={$configs['host']};port={$configs['port']};charset=utf8mb4";
    $pdo = new PDO($dsn, $configs['username'], $configs['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ PDO connection to MySQL server: SUCCESS\n";
    
    // Test database exists
    try {
        $pdo->exec("USE {$configs['database']}");
        echo "   ✅ Database '{$configs['database']}' exists: SUCCESS\n";
        
        // Test users table
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "   ✅ Table 'users' exists: SUCCESS\n";
            
            // Check table structure
            $stmt = $pdo->query("DESCRIBE users");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "   ✅ Users table columns: " . implode(', ', $columns) . "\n";
            
            // Check user count
            $stmt = $pdo->query("SELECT COUNT(*) FROM users");
            $count = $stmt->fetchColumn();
            echo "   📊 Users count: {$count}\n";
            
        } else {
            echo "   ❌ Table 'users' does not exist\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Database '{$configs['database']}' error: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ PDO connection failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Method 2: MySQLi
echo "   Method 2: MySQLi Connection\n";
try {
    $mysqli = new mysqli($configs['host'], $configs['username'], $configs['password'], $configs['database'], $configs['port']);
    
    if ($mysqli->connect_error) {
        echo "   ❌ MySQLi connection failed: " . $mysqli->connect_error . "\n";
    } else {
        echo "   ✅ MySQLi connection: SUCCESS\n";
        
        // Test query
        $result = $mysqli->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "   📊 Users count via MySQLi: " . $row['count'] . "\n";
        } else {
            echo "   ❌ Query failed: " . $mysqli->error . "\n";
        }
        
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "   ❌ MySQLi error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Laravel database connection
echo "4. Testing Laravel Database Connection:\n";
try {
    // Simulate Laravel's database connection
    $dsn = "mysql:host={$configs['host']};port={$configs['port']};dbname={$configs['database']};charset=utf8mb4";
    $pdo = new PDO($dsn, $configs['username'], $configs['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    echo "   ✅ Laravel-style connection: SUCCESS\n";
    
    // Test authentication query (simulate Laravel Auth::attempt)
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? LIMIT 1");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "   ✅ Test user query: SUCCESS\n";
        echo "   📋 User found: {$user['name']} ({$user['email']})\n";
        echo "   📋 Status: {$user['status']}\n";
        echo "   📋 Role: {$user['role']}\n";
    } else {
        echo "   ❌ Test user not found\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Laravel-style connection failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Check MySQL server status
echo "5. MySQL Server Information:\n";
try {
    $pdo = new PDO("mysql:host={$configs['host']};port={$configs['port']}", $configs['username'], $configs['password']);
    
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetchColumn();
    echo "   📋 MySQL Version: {$version}\n";
    
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "   📋 Available Databases: " . implode(', ', $databases) . "\n";
    
    if (in_array($configs['database'], $databases)) {
        echo "   ✅ Target database '{$configs['database']}' exists\n";
    } else {
        echo "   ❌ Target database '{$configs['database']}' does not exist\n";
        echo "   🔧 Creating database...\n";
        $pdo->exec("CREATE DATABASE IF NOT EXISTS {$configs['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "   ✅ Database created\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ MySQL server info failed: " . $e->getMessage() . "\n";
}

echo "\n=== DIAGNOSTIC COMPLETE ===\n";
echo "If all tests pass, the database connection should work.\n";
echo "If any test fails, that's the issue to fix.\n";
