<?php

echo "=== FIXING ROLE COLUMN ISSUE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected\n\n";
    
    // 1. Check current users table structure
    echo "1. 📋 CHECKING USERS TABLE STRUCTURE...\n";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasRoleColumn = false;
    echo "   Current columns:\n";
    foreach ($columns as $column) {
        echo "   - {$column['Field']}: {$column['Type']}\n";
        if ($column['Field'] === 'role') {
            $hasRoleColumn = true;
        }
    }
    
    if (!$hasRoleColumn) {
        echo "\n   ❌ ROLE COLUMN MISSING!\n";
        echo "   🔧 Adding role column...\n";
        
        // Add role column
        $pdo->exec("ALTER TABLE users ADD COLUMN role ENUM('admin', 'student', 'ketua_ukm') DEFAULT 'student' AFTER email");
        echo "   ✅ Role column added successfully\n";
    } else {
        echo "\n   ✅ Role column exists\n";
    }
    
    // 2. Check if users have role values
    echo "\n2. 👥 CHECKING USER ROLES...\n";
    $stmt = $pdo->query("SELECT id, name, email, role FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $usersWithoutRole = 0;
    foreach ($users as $user) {
        if (empty($user['role']) || $user['role'] === null) {
            $usersWithoutRole++;
            echo "   ⚠️  User without role: {$user['email']}\n";
        } else {
            echo "   ✅ {$user['email']}: {$user['role']}\n";
        }
    }
    
    // 3. Fix users without roles
    if ($usersWithoutRole > 0) {
        echo "\n3. 🔧 FIXING USERS WITHOUT ROLES...\n";
        
        // Set admin role for admin emails
        $adminEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        foreach ($adminEmails as $email) {
            $stmt = $pdo->prepare("UPDATE users SET role = 'admin' WHERE email = ? AND (role IS NULL OR role = '')");
            $stmt->execute([$email]);
            if ($stmt->rowCount() > 0) {
                echo "   ✅ Set admin role for: $email\n";
            }
        }
        
        // Set ketua_ukm role for ketua emails
        $ketuaEmails = ['<EMAIL>', '<EMAIL>'];
        foreach ($ketuaEmails as $email) {
            $stmt = $pdo->prepare("UPDATE users SET role = 'ketua_ukm' WHERE email = ? AND (role IS NULL OR role = '')");
            $stmt->execute([$email]);
            if ($stmt->rowCount() > 0) {
                echo "   ✅ Set ketua_ukm role for: $email\n";
            }
        }
        
        // Set student role for remaining users
        $stmt = $pdo->prepare("UPDATE users SET role = 'student' WHERE role IS NULL OR role = ''");
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            echo "   ✅ Set student role for {$stmt->rowCount()} users\n";
        }
    }
    
    // 4. Verify final state
    echo "\n4. ✅ VERIFICATION...\n";
    $stmt = $pdo->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $roleCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   📊 Final role distribution:\n";
    foreach ($roleCounts as $roleCount) {
        echo "   - {$roleCount['role']}: {$roleCount['count']} users\n";
    }
    
    // 5. Test query that was failing
    echo "\n5. 🧪 TESTING PROBLEMATIC QUERY...\n";
    try {
        $stmt = $pdo->query("SELECT nim, name, email, role, status, created_at FROM users ORDER BY role, status");
        $testUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "   ✅ Query executed successfully! Found " . count($testUsers) . " users\n";
        
        echo "   📋 Sample users:\n";
        foreach (array_slice($testUsers, 0, 3) as $user) {
            echo "   - {$user['email']} ({$user['role']}) - {$user['status']}\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Query still failing: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎯 ROLE COLUMN FIX COMPLETE!\n";
    
} catch (PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
    
    echo "\n🔧 MANUAL FIX INSTRUCTIONS:\n";
    echo "1. Connect to MySQL: mysql -u root -p\n";
    echo "2. Use database: USE ukmwebv;\n";
    echo "3. Add role column: ALTER TABLE users ADD COLUMN role ENUM('admin', 'student', 'ketua_ukm') DEFAULT 'student' AFTER email;\n";
    echo "4. Update admin users: UPDATE users SET role = 'admin' WHERE email LIKE '%admin%';\n";
    echo "5. Update ketua users: UPDATE users SET role = 'ketua_ukm' WHERE email LIKE '%ketua%';\n";
    echo "6. Update remaining: UPDATE users SET role = 'student' WHERE role IS NULL;\n";
}
