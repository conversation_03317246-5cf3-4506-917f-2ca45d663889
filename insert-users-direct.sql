-- SOLUSI FINAL LOGIN - DIRECT SQL
-- Jalan<PERSON> di phpMyAdmin atau MySQL command line

-- 1. Hapus semua user yang ada
DELETE FROM users;

-- 2. Reset auto increment
ALTER TABLE users AUTO_INCREMENT = 1;

-- 3. Insert admin user dengan password yang pasti bekerja
INSERT INTO users (
    nim, 
    name, 
    email, 
    password, 
    phone, 
    gender, 
    faculty, 
    major, 
    batch, 
    role, 
    status, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    'ADMIN001',
    'Administrator',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '081234567890',
    'male',
    'Administrasi',
    'Sistem Informasi',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- 4. Insert admin user dengan password alternatif
INSERT INTO users (
    nim, 
    name, 
    email, 
    password, 
    phone, 
    gender, 
    faculty, 
    major, 
    batch, 
    role, 
    status, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    'ADMIN002',
    'Admin Backup',
    '<EMAIL>',
    '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm',
    '081234567891',
    'male',
    'Administrasi',
    'Sistem Informasi',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- 5. Insert student user
INSERT INTO users (
    nim, 
    name, 
    email, 
    password, 
    phone, 
    gender, 
    faculty, 
    major, 
    batch, 
    role, 
    status, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    '1301210001',
    'Test Student',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '081234567892',
    'male',
    'Informatika',
    'Sistem Informasi',
    '2021',
    'student',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- 6. Verifikasi data berhasil diinsert
SELECT 
    id, 
    nim, 
    name, 
    email, 
    role, 
    status, 
    created_at 
FROM users 
ORDER BY id;

-- 7. Test password hash (untuk debugging)
-- Password untuk semua user di atas adalah: password
-- Hash: $2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi
-- Hash alternatif: $2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm (password: secret)
