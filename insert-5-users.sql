-- INSERT 5 USERS (2 ADMIN + 3 MAHASISWA)
-- Untuk test homepage counter

-- Hapus user yang ada (jika ada)
DELETE FROM users;
ALTER TABLE users AUTO_INCREMENT = 1;

-- 1. ADMIN 1
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    'ADMIN001',
    'Administrator <PERSON><PERSON><PERSON>',
    '<EMAIL>',
    '$2y$10$E4CUV86GBmVOeXrXj0h5LO0Q4KWMOMTCXR4pucO2hU.M4mZsUM2/G',
    '081234567890',
    'male',
    'Administrasi',
    'Sistem Informasi',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- 2. ADMIN 2
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    'ADMIN002',
    'Administrator Ke<PERSON>',
    '<EMAIL>',
    '$2y$10$E4CUV86GBmVOeXrXj0h5LO0Q4KWMOMTCXR4pucO2hU.M4mZsUM2/G',
    '081234567891',
    'female',
    'Administrasi',
    'Manajemen',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- 3. MAHASISWA 1
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    '1301210001',
    'Ahmad Rizki Pratama',
    '<EMAIL>',
    '$2y$10$E4CUV86GBmVOeXrXj0h5LO0Q4KWMOMTCXR4pucO2hU.M4mZsUM2/G',
    '081234567892',
    'male',
    'Informatika',
    'Teknik Informatika',
    '2021',
    'student',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- 4. MAHASISWA 2
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    '1301210002',
    'Siti Nurhaliza',
    '<EMAIL>',
    '$2y$10$E4CUV86GBmVOeXrXj0h5LO0Q4KWMOMTCXR4pucO2hU.M4mZsUM2/G',
    '081234567893',
    'female',
    'Informatika',
    'Sistem Informasi',
    '2021',
    'student',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- 5. MAHASISWA 3
INSERT INTO users (
    nim, name, email, password, phone, gender, faculty, major, batch,
    role, status, email_verified_at, created_at, updated_at
) VALUES (
    '1301210003',
    'Budi Santoso',
    '<EMAIL>',
    '$2y$10$E4CUV86GBmVOeXrXj0h5LO0Q4KWMOMTCXR4pucO2hU.M4mZsUM2/G',
    '081234567894',
    'male',
    'Elektro',
    'Teknik Elektro',
    '2022',
    'student',
    'active',
    NOW(),
    NOW(),
    NOW()
);

-- Verifikasi data berhasil diinsert
SELECT 
    id, 
    nim, 
    name, 
    email, 
    role, 
    status, 
    created_at 
FROM users 
ORDER BY role DESC, id;

-- Hitung berdasarkan role
SELECT 
    role,
    COUNT(*) as jumlah
FROM users 
WHERE status = 'active'
GROUP BY role;

-- Total user aktif
SELECT COUNT(*) as total_anggota_aktif FROM users WHERE status = 'active';
