<?php

echo "=== TEST LOGIN LOGIC ===\n";

// Database connection
$host = '127.0.0.1';
$dbname = 'ukmwebv';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected\n";
    
    // Test credentials
    $testCredentials = [
        ['login' => '<EMAIL>', 'password' => 'admin123', 'type' => 'email'],
        ['login' => 'ADMIN001', 'password' => 'admin123', 'type' => 'nim'],
        ['login' => '<EMAIL>', 'password' => 'student123', 'type' => 'email'],
        ['login' => '1301210001', 'password' => 'student123', 'type' => 'nim'],
    ];
    
    echo "\n1. Testing login logic simulation...\n";
    
    foreach ($testCredentials as $cred) {
        echo "\n--- Testing {$cred['type']}: {$cred['login']} ---\n";
        
        // Determine field (same logic as LoginRequest)
        $field = filter_var($cred['login'], FILTER_VALIDATE_EMAIL) ? 'email' : 'nim';
        echo "Field detected: {$field}\n";
        
        // Find user
        $stmt = $pdo->prepare("SELECT * FROM users WHERE {$field} = ?");
        $stmt->execute([$cred['login']]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "✅ User found: {$user['name']} ({$user['email']})\n";
            echo "Status: {$user['status']}\n";
            echo "Role: {$user['role']}\n";
            
            // Test password
            if (password_verify($cred['password'], $user['password'])) {
                echo "✅ Password: CORRECT\n";
                
                // Check status
                if ($user['status'] === 'active') {
                    echo "✅ Status: ACTIVE - Login should work\n";
                } else {
                    echo "❌ Status: {$user['status']} - Login will be blocked\n";
                }
            } else {
                echo "❌ Password: INCORRECT\n";
                echo "Hash in DB: " . substr($user['password'], 0, 30) . "...\n";
                
                // Update password
                $newHash = password_hash($cred['password'], PASSWORD_DEFAULT);
                $updateStmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                $updateStmt->execute([$newHash, $user['id']]);
                echo "🔧 Password updated\n";
            }
        } else {
            echo "❌ User not found for {$field}: {$cred['login']}\n";
        }
    }
    
    echo "\n2. Ensuring users exist with correct data...\n";
    
    // Delete all users and recreate
    $pdo->exec("DELETE FROM users");
    echo "🗑️  All users deleted\n";
    
    // Create admin
    $adminHash = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (
            nim, name, email, password, phone, gender, faculty, major, batch,
            role, status, email_verified_at, created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
        )
    ");
    
    $stmt->execute([
        'ADMIN001',
        'Administrator',
        '<EMAIL>',
        $adminHash,
        '081234567890',
        'male',
        'Administrasi',
        'Sistem Informasi',
        '2024',
        'admin',
        'active',
        date('Y-m-d H:i:s')
    ]);
    echo "✅ Admin created\n";
    
    // Create student
    $studentHash = password_hash('student123', PASSWORD_DEFAULT);
    $stmt->execute([
        '1301210001',
        'Test Student',
        '<EMAIL>',
        $studentHash,
        '081234567891',
        'male',
        'Informatika',
        'Sistem Informasi',
        '2021',
        'student',
        'active',
        date('Y-m-d H:i:s')
    ]);
    echo "✅ Student created\n";
    
    echo "\n3. Final verification with fresh data...\n";
    
    foreach ($testCredentials as $cred) {
        echo "\n--- Final test {$cred['type']}: {$cred['login']} ---\n";
        
        $field = filter_var($cred['login'], FILTER_VALIDATE_EMAIL) ? 'email' : 'nim';
        $stmt = $pdo->prepare("SELECT * FROM users WHERE {$field} = ?");
        $stmt->execute([$cred['login']]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($cred['password'], $user['password']) && $user['status'] === 'active') {
            echo "✅ LOGIN READY: {$cred['login']} / {$cred['password']}\n";
        } else {
            echo "❌ LOGIN FAILED\n";
            if (!$user) echo "   - User not found\n";
            if ($user && !password_verify($cred['password'], $user['password'])) echo "   - Password incorrect\n";
            if ($user && $user['status'] !== 'active') echo "   - Status not active: {$user['status']}\n";
        }
    }
    
    echo "\n4. Database summary...\n";
    $stmt = $pdo->query("SELECT id, nim, name, email, role, status FROM users ORDER BY id");
    while ($user = $stmt->fetch()) {
        echo "   {$user['id']}. {$user['name']} ({$user['email']}) - NIM: {$user['nim']} - {$user['role']} - {$user['status']}\n";
    }
    
    echo "\n=== LOGIN TEST COMPLETED ===\n";
    echo "🎯 Try these login combinations:\n";
    echo "\n📧 ADMIN (Email):\n";
    echo "   Login: <EMAIL>\n";
    echo "   Password: admin123\n";
    echo "\n🆔 ADMIN (NIM):\n";
    echo "   Login: ADMIN001\n";
    echo "   Password: admin123\n";
    echo "\n📧 STUDENT (Email):\n";
    echo "   Login: <EMAIL>\n";
    echo "   Password: student123\n";
    echo "\n🆔 STUDENT (NIM):\n";
    echo "   Login: 1301210001\n";
    echo "   Password: student123\n";
    echo "\n🌐 URL: http://localhost:8000/login\n";
    echo "\n💡 NOTE: Form field is 'login' not 'email' - it accepts both email and NIM\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
