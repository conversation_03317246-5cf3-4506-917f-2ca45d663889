<?php

echo "=== CHECKING UKM LOGO IMPLEMENTATION ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Check if Telkom logo exists
    $telkomLogoPath = public_path('storage/Telkom.png');
    $destinationPath = public_path('storage/ukms/logos/telkom-logo.png');
    
    echo "Source Telkom.png: " . (file_exists($telkomLogoPath) ? "✅ EXISTS" : "❌ NOT FOUND") . "\n";
    echo "Destination telkom-logo.png: " . (file_exists($destinationPath) ? "✅ EXISTS" : "❌ NOT FOUND") . "\n";
    
    // Find UKMs that might need Telkom logo
    $ukms = \App\Models\Ukm::all();
    echo "\n=== ALL UKMs ===\n";
    foreach($ukms as $ukm) {
        echo "ID: {$ukm->id} | Name: {$ukm->name} | Logo: " . ($ukm->logo ?: 'None') . "\n";
    }
    
    // Check for Sistem Informasi UKM specifically
    $siUkm = \App\Models\Ukm::where('name', 'like', '%sistem informasi%')->first();
    if($siUkm) {
        echo "\n=== SISTEM INFORMASI UKM ===\n";
        echo "Found: {$siUkm->name}\n";
        echo "Current Logo: " . ($siUkm->logo ?: 'None') . "\n";
        
        if(!$siUkm->logo || $siUkm->logo !== 'ukms/logos/telkom-logo.png') {
            echo "Updating with Telkom logo...\n";
            $siUkm->update(['logo' => 'ukms/logos/telkom-logo.png']);
            echo "✅ Updated successfully!\n";
        } else {
            echo "✅ Already has Telkom logo!\n";
        }
    } else {
        echo "\n❌ No Sistem Informasi UKM found\n";
    }
    
    echo "\n=== VERIFICATION COMPLETE ===\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
