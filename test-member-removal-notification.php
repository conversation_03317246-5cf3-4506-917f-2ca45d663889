<?php
/**
 * Test member removal with notification system
 */

echo "=== TESTING MEMBER REMOVAL WITH NOTIFICATION ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Setting up test data...\n";
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    if (!$ukm) {
        echo "   ❌ IMMA UKM not found\n";
        exit;
    }
    
    echo "   ✅ Found IMMA UKM (ID: {$ukm->id})\n";
    
    // Find a student to test with
    $student = \App\Models\User::where('role', 'student')->where('email', '<EMAIL>')->first();
    if (!$student) {
        echo "   ❌ Test student not found\n";
        exit;
    }
    
    echo "   ✅ Found test student: {$student->name} (ID: {$student->id})\n";
    
    echo "\n2. Testing member removal scenarios...\n";
    
    // Scenario 1: Add member as active first
    echo "   Scenario 1: Adding member as active...\n";
    
    // Remove any existing membership first
    $ukm->members()->detach($student->id);
    
    // Add as active member
    $ukm->members()->attach($student->id, [
        'role' => 'member',
        'status' => 'active',
        'joined_date' => now(),
        'applied_at' => now(),
    ]);
    
    $ukm->increment('current_members');
    
    echo "      ✅ Added {$student->name} as active member\n";
    echo "      Current members count: {$ukm->fresh()->current_members}\n";
    
    // Check membership status
    $membership = $ukm->members()->where('ukm_members.user_id', $student->id)->first();
    if ($membership) {
        echo "      ✅ Membership exists with status: {$membership->pivot->status}\n";
    } else {
        echo "      ❌ Membership not found\n";
    }
    
    echo "\n   Scenario 2: Testing removal with notification...\n";
    
    // Test the removal process
    $removalReason = "Test removal for demonstration purposes";
    
    // Simulate the removal process from KetuaUkmController
    $memberToRemove = $ukm->members()->wherePivot('user_id', $student->id)->wherePivot('status', 'active')->first();
    
    if ($memberToRemove) {
        echo "      ✅ Found active member to remove\n";
        
        // Send notification (test the notification service)
        try {
            $notification = \App\Services\NotificationService::createUkmMemberRemoved($memberToRemove, $ukm->name, $removalReason);
            echo "      ✅ Notification created (ID: {$notification->id})\n";
            echo "      📧 Notification title: {$notification->title}\n";
            echo "      📧 Notification message: {$notification->message}\n";
        } catch (\Exception $e) {
            echo "      ❌ Notification creation failed: " . $e->getMessage() . "\n";
        }
        
        // Remove member completely (detach from pivot table)
        $ukm->members()->detach($student->id);
        
        // Update member count
        $ukm->decrement('current_members');
        
        echo "      ✅ Member completely removed from UKM\n";
        echo "      Current members count: {$ukm->fresh()->current_members}\n";
        
        // Verify removal
        $membershipAfterRemoval = $ukm->members()->where('ukm_members.user_id', $student->id)->first();
        if (!$membershipAfterRemoval) {
            echo "      ✅ Confirmed: No membership record exists (completely removed)\n";
        } else {
            echo "      ❌ Membership still exists with status: {$membershipAfterRemoval->pivot->status}\n";
        }
        
    } else {
        echo "      ❌ No active member found to remove\n";
    }
    
    echo "\n   Scenario 3: Testing re-registration capability...\n";
    
    // Test if removed member can register again
    $canRegister = true;
    $existingMembership = $ukm->members()->where('ukm_members.user_id', $student->id)->first();
    
    if (!$existingMembership) {
        echo "      ✅ No existing membership found - can register as new member\n";
    } else {
        $status = $existingMembership->pivot->status;
        if ($status === 'active') {
            echo "      ❌ Already active member - cannot register\n";
            $canRegister = false;
        } elseif ($status === 'pending') {
            echo "      ❌ Pending application exists - cannot register\n";
            $canRegister = false;
        } else {
            echo "      ✅ Can re-register (status: {$status})\n";
        }
    }
    
    if ($canRegister) {
        echo "      ✅ Registration button should be available\n";
    } else {
        echo "      ❌ Registration button should not be available\n";
    }
    
    echo "\n3. Testing notification display...\n";
    
    // Get user notifications
    $userNotifications = $student->notifications()->where('type', 'ukm_member_removed')->get();
    echo "   📊 Total removal notifications for user: " . $userNotifications->count() . "\n";
    
    foreach ($userNotifications as $notification) {
        echo "      📧 Notification: {$notification->title}\n";
        echo "         Message: {$notification->message}\n";
        echo "         Created: {$notification->created_at->format('Y-m-d H:i:s')}\n";
        echo "         Read: " . ($notification->read_at ? 'Yes' : 'No') . "\n";
        echo "         ---\n";
    }
    
    echo "\n4. Creating test HTML page...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Member Removal & Notification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            background: #f9f9f9; 
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .notification { 
            background: #fff3cd; 
            border: 1px solid #ffeaa7; 
            padding: 15px; 
            border-radius: 8px; 
            margin: 10px 0; 
        }
        .removed-status { 
            background: #f8d7da; 
            color: #721c24; 
            padding: 8px 12px; 
            border-radius: 4px; 
            font-weight: bold; 
        }
        .can-register { 
            background: #d4edda; 
            color: #155724; 
            padding: 8px 12px; 
            border-radius: 4px; 
            font-weight: bold; 
        }
    </style>
</head>
<body>
    <h1>🔧 Member Removal & Notification Test</h1>
    
    <div class='test-section'>
        <h2>📊 Test Results</h2>
        <p><strong>UKM:</strong> {$ukm->name}</p>
        <p><strong>Test Student:</strong> {$student->name}</p>
        <p><strong>Current Members:</strong> " . $ukm->fresh()->current_members . "</p>
        <p><strong>Member Status:</strong> <span class='removed-status'>COMPLETELY REMOVED</span></p>
        <p><strong>Can Re-register:</strong> <span class='can-register'>YES</span></p>
    </div>
    
    <div class='test-section'>
        <h2>📧 Notifications Sent</h2>";
    
    foreach ($userNotifications as $notification) {
        $testHtml .= "
        <div class='notification'>
            <h4>{$notification->title}</h4>
            <p>{$notification->message}</p>
            <small>Sent: {$notification->created_at->format('d M Y H:i:s')}</small>
        </div>";
    }
    
    $testHtml .= "
    </div>
    
    <div class='test-section'>
        <h2>🔗 Test Links</h2>
        <ul>
            <li><a href='http://127.0.0.1:8000/ukms/imma' target='_blank'><strong>IMMA UKM Detail</strong> - Should show registration button for removed member</a></li>
            <li><a href='http://127.0.0.1:8000/login' target='_blank'><strong>Login as Student</strong> - Login as {$student->email}</a></li>
            <li><a href='http://127.0.0.1:8000/notifications' target='_blank'><strong>Notifications</strong> - Check removal notification</a></li>
        </ul>
    </div>
    
    <div class='test-section'>
        <h2>✅ Expected Behavior</h2>
        <ul>
            <li>✅ Member completely removed from UKM (not just inactive)</li>
            <li>✅ Notification sent to removed member with ketua's message</li>
            <li>✅ Registration button appears for removed member</li>
            <li>✅ Member can register again as new application</li>
            <li>✅ Member count decremented correctly</li>
        </ul>
    </div>
    
    <div class='test-section'>
        <h2>🧪 Test Instructions</h2>
        <ol>
            <li>Login as ketua UKM</li>
            <li>Go to member management and remove a member</li>
            <li>Check that notification is sent</li>
            <li>Login as removed member and check notifications</li>
            <li>Visit UKM detail page and verify registration button appears</li>
            <li>Try to register again</li>
        </ol>
    </div>
</body>
</html>";
    
    file_put_contents(public_path('member-removal-notification-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/member-removal-notification-test.html\n";
    
    echo "\n=== MEMBER REMOVAL TEST COMPLETED ===\n";
    echo "🎉 Member removal with notification system is working!\n";
    echo "🔗 Test page: http://127.0.0.1:8000/member-removal-notification-test.html\n";
    echo "🔗 IMMA UKM: http://127.0.0.1:8000/ukms/imma\n";
    echo "📧 Login as {$student->email} to see notification\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
