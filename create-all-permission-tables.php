<?php

echo "=== CREATING ALL PERMISSION TABLES ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // 1. Create permissions table
    echo "1. Creating permissions table...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS permissions (
                id bigint unsigned NOT NULL AUTO_INCREMENT,
                name varchar(255) NOT NULL,
                guard_name varchar(255) NOT NULL,
                created_at timestamp NULL DEFAULT NULL,
                updated_at timestamp NULL DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY permissions_name_guard_name_unique (name, guard_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ Permissions table created\n";
    } catch (Exception $e) {
        echo "❌ Error creating permissions table: " . $e->getMessage() . "\n";
    }
    
    // 2. Create model_has_permissions table
    echo "\n2. Creating model_has_permissions table...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS model_has_permissions (
                permission_id bigint unsigned NOT NULL,
                model_type varchar(255) NOT NULL,
                model_id bigint unsigned NOT NULL,
                PRIMARY KEY (permission_id, model_id, model_type),
                KEY model_has_permissions_model_id_model_type_index (model_id, model_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ Model_has_permissions table created\n";
    } catch (Exception $e) {
        echo "❌ Error creating model_has_permissions table: " . $e->getMessage() . "\n";
    }
    
    // 3. Create model_has_roles table
    echo "\n3. Creating model_has_roles table...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS model_has_roles (
                role_id bigint unsigned NOT NULL,
                model_type varchar(255) NOT NULL,
                model_id bigint unsigned NOT NULL,
                PRIMARY KEY (role_id, model_id, model_type),
                KEY model_has_roles_model_id_model_type_index (model_id, model_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ Model_has_roles table created\n";
    } catch (Exception $e) {
        echo "❌ Error creating model_has_roles table: " . $e->getMessage() . "\n";
    }
    
    // 4. Create role_has_permissions table
    echo "\n4. Creating role_has_permissions table...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS role_has_permissions (
                permission_id bigint unsigned NOT NULL,
                role_id bigint unsigned NOT NULL,
                PRIMARY KEY (permission_id, role_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ Role_has_permissions table created\n";
    } catch (Exception $e) {
        echo "❌ Error creating role_has_permissions table: " . $e->getMessage() . "\n";
    }
    
    // 5. Insert basic permissions
    echo "\n5. Inserting basic permissions...\n";
    try {
        $pdo->exec("
            INSERT IGNORE INTO permissions (name, guard_name, created_at, updated_at) VALUES
            ('manage users', 'web', NOW(), NOW()),
            ('manage ukms', 'web', NOW(), NOW()),
            ('manage events', 'web', NOW(), NOW()),
            ('view dashboard', 'web', NOW(), NOW()),
            ('edit profile', 'web', NOW(), NOW())
        ");
        echo "✅ Basic permissions inserted\n";
    } catch (Exception $e) {
        echo "❌ Error inserting permissions: " . $e->getMessage() . "\n";
    }
    
    // 6. Assign roles to existing users
    echo "\n6. Assigning roles to existing users...\n";
    try {
        $pdo->exec("
            INSERT IGNORE INTO model_has_roles (role_id, model_type, model_id)
            SELECT r.id, 'App\\\\Models\\\\User', u.id
            FROM roles r, users u
            WHERE r.name = u.role AND r.guard_name = 'web'
        ");
        echo "✅ Roles assigned to users\n";
    } catch (Exception $e) {
        echo "❌ Error assigning roles: " . $e->getMessage() . "\n";
    }
    
    // 7. Add foreign key constraints (if possible)
    echo "\n7. Adding foreign key constraints...\n";
    try {
        // Add FK for model_has_permissions
        $pdo->exec("
            ALTER TABLE model_has_permissions 
            ADD CONSTRAINT model_has_permissions_permission_id_foreign 
            FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE
        ");
        echo "✅ FK added to model_has_permissions\n";
    } catch (Exception $e) {
        echo "⚠️  FK constraint for model_has_permissions: " . $e->getMessage() . "\n";
    }
    
    try {
        // Add FK for model_has_roles
        $pdo->exec("
            ALTER TABLE model_has_roles 
            ADD CONSTRAINT model_has_roles_role_id_foreign 
            FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE
        ");
        echo "✅ FK added to model_has_roles\n";
    } catch (Exception $e) {
        echo "⚠️  FK constraint for model_has_roles: " . $e->getMessage() . "\n";
    }
    
    // 8. Verification
    echo "\n📊 FINAL VERIFICATION:\n";
    
    $tables = ['roles', 'permissions', 'model_has_roles', 'model_has_permissions', 'role_has_permissions'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            echo "✅ Table '{$table}': {$count} records\n";
        } catch (Exception $e) {
            echo "❌ Table '{$table}': Error - " . $e->getMessage() . "\n";
        }
    }
    
    // Test the failing query again
    echo "\n🔍 TESTING STUDENT ROLE QUERY:\n";
    try {
        $stmt = $pdo->prepare("SELECT * FROM roles WHERE name = ? AND guard_name = ? LIMIT 1");
        $stmt->execute(['student', 'web']);
        $role = $stmt->fetch();
        
        if ($role) {
            echo "✅ Student role found: ID {$role['id']}, Name: {$role['name']}\n";
        } else {
            echo "❌ Student role not found\n";
        }
    } catch (Exception $e) {
        echo "❌ Query failed: " . $e->getMessage() . "\n";
    }
    
    // Show user role assignments
    echo "\n👥 USER ROLE ASSIGNMENTS:\n";
    try {
        $stmt = $pdo->query("
            SELECT u.name, u.email, u.role as user_role, r.name as assigned_role
            FROM users u
            LEFT JOIN model_has_roles mhr ON mhr.model_id = u.id AND mhr.model_type = 'App\\\\Models\\\\User'
            LEFT JOIN roles r ON r.id = mhr.role_id
            ORDER BY u.id
        ");
        
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            $assignedRole = $user['assigned_role'] ?: 'None';
            echo "- {$user['name']} ({$user['email']}) - Role: {$user['user_role']}, Assigned: {$assignedRole}\n";
        }
    } catch (Exception $e) {
        echo "❌ Error showing assignments: " . $e->getMessage() . "\n";
    }
    
    echo "\n✅ Permission system setup complete!\n";
    echo "🎯 Admin edit user should now work without errors.\n";
    
} catch (Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
}
