<?php

// Simple script to create admin user
echo "Creating admin user...\n";

try {
    // Include Laravel bootstrap
    require_once __DIR__ . '/vendor/autoload.php';
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

    // Check if admin exists
    $admin = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if ($admin) {
        echo "Admin user already exists. Updating password...\n";
        $admin->update([
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        echo "Admin user updated successfully!\n";
    } else {
        echo "Creating new admin user...\n";
        \App\Models\User::create([
            'nim' => 'ADMIN001',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Administrasi',
            'major' => 'Sistem Informasi',
            'batch' => '2024',
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        echo "Admin user created successfully!\n";
    }
    
    echo "\nLogin credentials:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
    echo "\nLogin URL: http://127.0.0.1:8000/login\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
