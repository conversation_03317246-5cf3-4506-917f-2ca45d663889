-- Insert Mahasiswa Users for phpMyAdmin
-- Password for all users: pass123123

-- First, let's check if users already exist and delete duplicates if needed
DELETE FROM users WHERE email LIKE '%@student.telkomuniversity.ac.id';

-- Insert new mahasiswa users (using 'student' role)
INSERT INTO users (name, email, nim, password, role, status, email_verified_at, created_at, updated_at) VALUES

('<PERSON><PERSON>', '<EMAIL>', '12345678901', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Ryemius Marghareta Siregar', '<EMAIL>', '12345678902', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Amanda Riski Agustian', '<EMAIL>', '12345678903', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Najla Ramadina Sulistyowati', '<EMAIL>', '12345678904', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Nabilla Alyvia', '<EMAIL>', '12345678905', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Aras Agita Fasya', '<EMAIL>', '12345678906', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Aufa Hafiy Andhika', '<EMAIL>', '12345678907', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Rahadian Nungki Saputra', '<EMAIL>', '12345678908', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Adit Kurniawan', '<EMAIL>', '12345678909', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Mikel Austin', '<EMAIL>', '12345678910', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Antonius Valentino', '<EMAIL>', '12345678911', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Abraham Arif Mulia', '<EMAIL>', '12345678912', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Fathan Mubina', '<EMAIL>', '12345678913', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW()),

('Mutiara Hani Demayanti', '<EMAIL>', '12345678914', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'student', 'active', NOW(), NOW(), NOW());

-- Check the result
SELECT 'Total Users' as info, COUNT(*) as count FROM users
UNION ALL
SELECT 'Student Users' as info, COUNT(*) as count FROM users WHERE role = 'student'
UNION ALL
SELECT 'Admin Users' as info, COUNT(*) as count FROM users WHERE role = 'admin';

-- Show created student users
SELECT name, email, nim, role, status FROM users WHERE role = 'student' ORDER BY name;
