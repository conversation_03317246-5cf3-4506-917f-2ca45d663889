<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faded Image Card Examples</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        /* Style 1: Horizontal Fade */
        .card-horizontal-fade {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card-horizontal-fade:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .image-container-horizontal {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .image-container-horizontal img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-container-horizontal::before,
        .image-container-horizontal::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 50px;
            pointer-events: none;
            z-index: 2;
        }

        .image-container-horizontal::before {
            left: 0;
            background: linear-gradient(to right, rgba(255,255,255,0.8), transparent);
        }

        .image-container-horizontal::after {
            right: 0;
            background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
        }

        /* Style 2: Gradient Overlay Fade */
        .card-gradient-fade {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .card-gradient-fade:hover {
            transform: scale(1.02);
        }

        .image-container-gradient {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .image-container-gradient img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-container-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                rgba(0,0,0,0.3) 0%,
                transparent 20%,
                transparent 80%,
                rgba(0,0,0,0.3) 100%
            );
            z-index: 2;
        }

        /* Style 3: Blur Fade Effect */
        .card-blur-fade {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
        }

        .image-container-blur {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .image-container-blur img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-container-blur::before,
        .image-container-blur::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 60px;
            backdrop-filter: blur(3px);
            z-index: 2;
        }

        .image-container-blur::before {
            left: 0;
            background: linear-gradient(to right, rgba(255,255,255,0.6), transparent);
        }

        .image-container-blur::after {
            right: 0;
            background: linear-gradient(to left, rgba(255,255,255,0.6), transparent);
        }

        /* Style 4: Color Fade */
        .card-color-fade {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .image-container-color {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .image-container-color img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-container-color::before,
        .image-container-color::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 80px;
            z-index: 2;
        }

        .image-container-color::before {
            left: 0;
            background: linear-gradient(to right, #667eea, transparent);
        }

        .image-container-color::after {
            right: 0;
            background: linear-gradient(to left, #764ba2, transparent);
        }

        /* Card Content */
        .card-content {
            padding: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .card-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .card-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.2s ease;
        }

        .card-button:hover {
            transform: translateY(-2px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Faded Image Card Examples</h1>
        
        <div class="grid">
            <!-- Style 1: Horizontal White Fade -->
            <div class="card-horizontal-fade">
                <div class="image-container-horizontal">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Mountain Landscape">
                </div>
                <div class="card-content">
                    <h3 class="card-title">Horizontal White Fade</h3>
                    <p class="card-description">Efek fade putih di kiri dan kanan gambar untuk memberikan kesan soft dan elegant.</p>
                    <button class="card-button">Learn More</button>
                </div>
            </div>

            <!-- Style 2: Gradient Overlay Fade -->
            <div class="card-gradient-fade">
                <div class="image-container-gradient">
                    <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Forest Lake">
                </div>
                <div class="card-content">
                    <h3 class="card-title">Gradient Overlay Fade</h3>
                    <p class="card-description">Menggunakan gradient overlay gelap di kedua sisi untuk efek dramatic dan modern.</p>
                    <button class="card-button">Explore</button>
                </div>
            </div>

            <!-- Style 3: Blur Fade Effect -->
            <div class="card-blur-fade">
                <div class="image-container-blur">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Ocean Sunset">
                </div>
                <div class="card-content">
                    <h3 class="card-title">Blur Fade Effect</h3>
                    <p class="card-description">Kombinasi blur dan fade untuk memberikan depth dan fokus pada bagian tengah gambar.</p>
                    <button class="card-button">Discover</button>
                </div>
            </div>

            <!-- Style 4: Color Fade -->
            <div class="card-color-fade">
                <div class="image-container-color">
                    <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Forest Path">
                </div>
                <div class="card-content">
                    <h3 class="card-title">Color Fade Effect</h3>
                    <p class="card-description">Fade dengan warna gradient yang matching dengan tema untuk konsistensi visual.</p>
                    <button class="card-button">View More</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
