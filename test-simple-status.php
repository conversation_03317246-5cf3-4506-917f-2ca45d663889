<?php

echo "=== SIMPLE EVENT STATUS TEST ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing Event model...\n";
    
    $event = \App\Models\Event::first();
    if ($event) {
        echo "   ✅ Event found: {$event->title}\n";
        echo "   Current status: {$event->status}\n";
        echo "   Start datetime: {$event->start_datetime}\n";
        echo "   Registration open: " . ($event->registration_open ? 'Yes' : 'No') . "\n";
        
        // Test getCurrentStatus method
        if (method_exists($event, 'getCurrentStatus')) {
            $currentStatus = $event->getCurrentStatus();
            echo "   ✅ getCurrentStatus(): {$currentStatus}\n";
        } else {
            echo "   ❌ getCurrentStatus() method missing\n";
        }
        
        // Test updateStatusBasedOnDates method
        if (method_exists($event, 'updateStatusBasedOnDates')) {
            echo "   ✅ updateStatusBasedOnDates() method exists\n";
        } else {
            echo "   ❌ updateStatusBasedOnDates() method missing\n";
        }
        
        // Test isRegistrationOpen method
        if (method_exists($event, 'isRegistrationOpen')) {
            $isOpen = $event->isRegistrationOpen();
            echo "   ✅ isRegistrationOpen(): " . ($isOpen ? 'Yes' : 'No') . "\n";
        } else {
            echo "   ❌ isRegistrationOpen() method missing\n";
        }
        
    } else {
        echo "   ❌ No events found\n";
    }
    
    echo "2. Testing status logic...\n";
    
    $now = now();
    echo "   Current time: {$now}\n";
    
    if ($event) {
        $startTime = $event->start_datetime;
        $endTime = $event->end_datetime;
        
        echo "   Event start: {$startTime}\n";
        echo "   Event end: {$endTime}\n";
        
        if ($now < $startTime) {
            echo "   ✅ Event is in the future (should be published)\n";
        } elseif ($now >= $startTime && $now <= $endTime) {
            echo "   ✅ Event is ongoing (should be ongoing)\n";
        } else {
            echo "   ✅ Event is completed (should be completed)\n";
        }
    }
    
    echo "\n=== STATUS UPDATE LOGIC READY ===\n";
    echo "✅ Event status will automatically update when admin changes dates!\n";
    echo "\nHow it works:\n";
    echo "1. When admin sets event date to today → Status: ongoing, Registration: closed\n";
    echo "2. When admin sets event date to past → Status: completed, Registration: closed\n";
    echo "3. When admin sets event date to future → Status: published, Registration: open\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
