<?php
echo "=== CHECKING UKMS TABLE STRUCTURE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check table structure
    echo "📋 UKMS Table Structure:\n";
    $result = $pdo->query("DESCRIBE ukms");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $null = $row['Null'] == 'YES' ? 'NULL' : 'NOT NULL';
        $default = $row['Default'] ? "DEFAULT '{$row['Default']}'" : '';
        echo "   • {$row['Field']} - {$row['Type']} {$null} {$default}\n";
    }
    
    echo "\n📊 Current UKMs count: ";
    $result = $pdo->query("SELECT COUNT(*) as count FROM ukms");
    $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "{$count}\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== CHECK COMPLETE ===\n";
?>
