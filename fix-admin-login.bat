@echo off
echo ========================================
echo   FIXING ADMIN LOGIN ISSUES
echo ========================================
echo.

echo [1/6] Clearing all caches...
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

echo.
echo [2/6] Creating/updating admin user...
php create-admin-user.php

echo.
echo [3/6] Running database migrations...
php artisan migrate --force

echo.
echo [4/6] Checking routes...
php artisan route:list | findstr admin

echo.
echo [5/6] Testing login system...
php test-unified-login.php

echo.
echo [6/6] Starting server...
echo.
echo ========================================
echo   ADMIN LOGIN FIXED!
echo ========================================
echo.
echo LOGIN INFORMATION:
echo URL: http://127.0.0.1:8000/login
echo Email: <EMAIL>
echo Password: admin123
echo.
echo TROUBLESHOOTING STEPS COMPLETED:
echo ✓ Caches cleared
echo ✓ Admin user created/updated
echo ✓ Database migrated
echo ✓ Routes verified
echo ✓ Login system tested
echo.
echo Starting Laravel server...
echo.

php artisan serve --host=127.0.0.1 --port=8000
