<?php

echo "=== TESTING ROUTES ===\n\n";

// Test URLs that should work
$testUrls = [
    '/ketua-ukm/dashboard',
    '/ketua-ukm/pending-members',
    '/ketua-ukm/pending-members/1/details',
    '/ketua-ukm/pending-members/1/approve',
    '/ketua-ukm/pending-members/1/reject',
    '/ketua-ukm/members',
];

foreach ($testUrls as $url) {
    echo "Testing URL: {$url}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost:8000{$url}");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200) {
        echo "✅ OK (200)\n";
    } elseif ($httpCode == 302) {
        echo "🔄 Redirect (302) - probably needs auth\n";
    } elseif ($httpCode == 404) {
        echo "❌ Not Found (404)\n";
    } else {
        echo "⚠️  HTTP {$httpCode}\n";
    }
    
    echo "---\n";
}

echo "\n🎯 NEXT STEPS:\n";
echo "1. Make sure Laravel server is running: php artisan serve\n";
echo "2. Login as ketua UKM: <EMAIL>\n";
echo "3. Test the pending members page\n";
