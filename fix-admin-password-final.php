<?php

echo "=== FIXING ADMIN PASSWORD TO MATCH LOGIN FORM ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // 1. Update admin password to match what's shown on login form
    echo "1. Updating admin password to 'admin123'...\n";
    
    $newPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
    $stmt->execute([$newPassword, '<EMAIL>']);
    
    echo "✅ Admin password updated\n";
    
    // 2. Verify the password works
    echo "2. Verifying password...\n";
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        echo "✅ Password verification: SUCCESS\n";
        echo "   Email: {$admin['email']}\n";
        echo "   Name: {$admin['name']}\n";
        echo "   Role: {$admin['role']}\n";
        echo "   Status: {$admin['status']}\n";
    } else {
        echo "❌ Password verification: FAILED\n";
    }
    
    // 3. Also update the test admin
    echo "\n3. Updating test admin password...\n";
    
    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
    $stmt->execute([$newPassword, '<EMAIL>']);
    
    echo "✅ Test admin password updated\n";
    
    // 4. Create additional test users with consistent passwords
    echo "\n4. Creating additional test users...\n";
    
    // Delete existing test users first
    $pdo->exec("DELETE FROM users WHERE nim IN ('STUDENT001', 'STUDENT002', 'STUDENT003')");
    
    $studentPassword = password_hash('student123', PASSWORD_DEFAULT);
    
    $students = [
        ['STUDENT001', 'Ahmad Rizki', '<EMAIL>'],
        ['STUDENT002', 'Siti Nurhaliza', '<EMAIL>'],
        ['STUDENT003', 'Budi Santoso', '<EMAIL>']
    ];
    
    foreach ($students as $student) {
        $stmt = $pdo->prepare("INSERT INTO users (nim, name, email, password, phone, gender, faculty, major, batch, status, role, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())");
        
        $stmt->execute([
            $student[0],
            $student[1],
            $student[2],
            $studentPassword,
            '081234567890',
            'male',
            'Informatika',
            'Teknik Informatika',
            '2021',
            'active',
            'student'
        ]);
        
        echo "✅ Created student: {$student[1]}\n";
    }
    
    // 5. Final verification
    echo "\n5. Final user count...\n";
    
    $stmt = $pdo->query("SELECT role, COUNT(*) as count FROM users WHERE status = 'active' GROUP BY role");
    $counts = $stmt->fetchAll();
    
    $totalActive = 0;
    foreach ($counts as $count) {
        echo "- {$count['role']}: {$count['count']} users\n";
        $totalActive += $count['count'];
    }
    
    echo "\n🎯 TOTAL ACTIVE USERS: {$totalActive}\n";
    echo "   (This should appear on homepage as 'Anggota Aktif')\n";
    
    echo "\n=== FINAL LOGIN CREDENTIALS ===\n";
    echo "🔑 ADMIN LOGIN:\n";
    echo "   Email: <EMAIL>\n";
    echo "   Password: admin123\n";
    echo "\n🔑 TEST ADMIN LOGIN:\n";
    echo "   Email: <EMAIL>\n";
    echo "   Password: admin123\n";
    echo "\n🔑 STUDENT LOGIN:\n";
    echo "   Email: <EMAIL>\n";
    echo "   Password: student123\n";
    echo "\n🌐 LOGIN URL: http://localhost:8000/login\n";
    echo "\n✅ Passwords now match what's displayed on login form!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
