<?php
/**
 * Complete test for re-registration functionality
 */

echo "=== COMPLETE RE-REGISTRATION TEST ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Setting up test environment...\n";
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    $student = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if (!$ukm || !$student) {
        echo "   ❌ UKM or student not found\n";
        exit;
    }
    
    echo "   ✅ UKM: {$ukm->name} (ID: {$ukm->id})\n";
    echo "   ✅ Student: {$student->name} (ID: {$student->id})\n";
    echo "   📊 UKM Status: {$ukm->status}\n";
    echo "   📊 Registration Status: " . ($ukm->registration_status ?? 'open') . "\n";
    echo "   📊 Members: {$ukm->current_members}/{$ukm->max_members}\n";
    
    echo "\n2. Testing complete removal and re-registration workflow...\n";
    
    // Step 1: Clean slate
    $ukm->members()->detach($student->id);
    echo "   ✅ Step 1: Cleaned any existing membership\n";
    
    // Step 2: Add as active member
    $ukm->members()->attach($student->id, [
        'role' => 'member',
        'status' => 'active',
        'joined_date' => now(),
        'applied_at' => now(),
    ]);
    $ukm->increment('current_members');
    echo "   ✅ Step 2: Added as active member\n";
    
    // Step 3: Remove member completely (simulate ketua removing)
    $ukm->members()->detach($student->id);
    $ukm->decrement('current_members');
    echo "   ✅ Step 3: Removed member completely\n";
    
    // Step 4: Test controller logic
    echo "\n3. Testing UkmController show method logic...\n";
    
    // Simulate the controller logic
    $membership = $ukm->members()->where('ukm_members.user_id', $student->id)->first();
    $membershipStatus = null;
    
    if ($membership) {
        $membershipStatus = $membership->pivot->status;
        echo "   📊 Membership found with status: {$membershipStatus}\n";
    } else {
        echo "   📊 No membership found (completely removed) ✅\n";
    }
    
    echo "   📊 membershipStatus variable: " . ($membershipStatus ?: 'null') . "\n";
    
    // Test view conditions
    echo "\n4. Testing view button conditions...\n";
    
    $conditions = [
        'membershipStatus === active' => ($membershipStatus === 'active'),
        'membershipStatus === pending' => ($membershipStatus === 'pending'),
        'ukm->status === active' => ($ukm->status === 'active'),
        'registration_status === open' => (($ukm->registration_status ?? 'open') === 'open'),
        'current_members < max_members' => ($ukm->current_members < $ukm->max_members),
        '!membershipStatus' => (!$membershipStatus),
        'membershipStatus === inactive' => ($membershipStatus === 'inactive'),
        'membershipStatus === alumni' => ($membershipStatus === 'alumni'),
    ];
    
    foreach ($conditions as $condition => $result) {
        echo "   📋 {$condition}: " . ($result ? 'TRUE ✅' : 'FALSE ❌') . "\n";
    }
    
    // Final button logic
    $showButton = false;
    $buttonText = '';
    
    if ($membershipStatus === 'active') {
        $buttonText = 'Sudah Bergabung (green badge)';
    } elseif ($membershipStatus === 'pending') {
        $buttonText = 'Menunggu Approval (yellow badge)';
    } elseif ($ukm->status === 'active' && ($ukm->registration_status ?? 'open') === 'open' && $ukm->current_members < $ukm->max_members) {
        if (!$membershipStatus || $membershipStatus === 'inactive' || $membershipStatus === 'alumni') {
            $showButton = true;
            $buttonText = ($membershipStatus === 'inactive' || $membershipStatus === 'alumni') ? 'Daftar Ulang' : 'Daftar Keanggotaan';
        }
    }
    
    echo "\n   🎯 RESULT:\n";
    echo "      Show button: " . ($showButton ? 'YES ✅' : 'NO ❌') . "\n";
    echo "      Button text: {$buttonText}\n";
    
    echo "\n5. Testing registration form access...\n";
    
    // Test registration form logic
    $canAccessForm = true;
    $blockReason = '';
    
    if ($student->role !== 'student') {
        $canAccessForm = false;
        $blockReason = 'Not a student';
    } elseif ($ukm->status !== 'active' || ($ukm->registration_status ?? 'open') !== 'open') {
        $canAccessForm = false;
        $blockReason = 'UKM registration closed';
    } elseif ($ukm->current_members >= $ukm->max_members) {
        $canAccessForm = false;
        $blockReason = 'UKM at capacity';
    } else {
        $existingMembership = $ukm->members()->where('ukm_members.user_id', $student->id)->first();
        if ($existingMembership) {
            $status = $existingMembership->pivot->status;
            if ($status === 'active') {
                $canAccessForm = false;
                $blockReason = 'Already active member';
            } elseif ($status === 'pending') {
                $canAccessForm = false;
                $blockReason = 'Pending application exists';
            }
        }
    }
    
    echo "   📋 Can access registration form: " . ($canAccessForm ? 'YES ✅' : 'NO ❌') . "\n";
    if (!$canAccessForm) {
        echo "   📋 Block reason: {$blockReason}\n";
    }
    
    echo "\n6. Creating comprehensive test page...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>🔄 Re-registration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { 
            margin: 20px 0; 
            padding: 25px; 
            border-radius: 12px; 
            background: white; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            text-align: center; 
            padding: 40px; 
            border-radius: 12px; 
            margin-bottom: 30px; 
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .workflow-step { 
            display: flex; 
            align-items: center; 
            margin: 15px 0; 
            padding: 15px; 
            background: #e3f2fd; 
            border-radius: 8px; 
        }
        .step-number { 
            background: #2196f3; 
            color: white; 
            width: 30px; 
            height: 30px; 
            border-radius: 50%; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            margin-right: 15px; 
            font-weight: bold; 
        }
        .test-link { 
            display: inline-block; 
            background: #007bff; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 6px; 
            margin: 10px 10px 10px 0; 
            transition: background 0.3s; 
        }
        .test-link:hover { background: #0056b3; color: white; text-decoration: none; }
        .test-link.success { background: #28a745; }
        .test-link.success:hover { background: #1e7e34; }
        .condition-list { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 15px 0; 
            font-family: monospace; 
        }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🔄 Re-registration Test</h1>
            <p>Testing removed member re-registration capability</p>
        </div>
        
        <div class='test-section'>
            <h2>📊 Test Results</h2>
            <div class='condition-list'>
                <strong>Current Status:</strong><br>
                ✅ Member completely removed: YES<br>
                ✅ Membership status: NULL<br>
                ✅ Should show button: " . ($showButton ? 'YES' : 'NO') . "<br>
                ✅ Button text: {$buttonText}<br>
                ✅ Can access form: " . ($canAccessForm ? 'YES' : 'NO') . "<br>
            </div>
        </div>
        
        <div class='test-section'>
            <h2>🔄 Re-registration Workflow</h2>
            <div class='workflow-step'>
                <div class='step-number'>1</div>
                <div>
                    <strong>Member gets removed by ketua</strong><br>
                    <small>Complete removal from pivot table (detach)</small>
                </div>
            </div>
            <div class='workflow-step'>
                <div class='step-number'>2</div>
                <div>
                    <strong>Member visits UKM detail page</strong><br>
                    <small>Should see 'Daftar Keanggotaan' button</small>
                </div>
            </div>
            <div class='workflow-step'>
                <div class='step-number'>3</div>
                <div>
                    <strong>Member clicks registration button</strong><br>
                    <small>Should access registration form</small>
                </div>
            </div>
            <div class='workflow-step'>
                <div class='step-number'>4</div>
                <div>
                    <strong>Member submits application</strong><br>
                    <small>Creates new pending membership</small>
                </div>
            </div>
        </div>
        
        <div class='test-section'>
            <h2>🧪 Manual Test Instructions</h2>
            <ol>
                <li><strong>Login as removed member:</strong> {$student->email}</li>
                <li><strong>Visit UKM detail page:</strong> Should see registration button</li>
                <li><strong>Click registration button:</strong> Should access form</li>
                <li><strong>Fill and submit form:</strong> Should create pending application</li>
                <li><strong>Check status:</strong> Should show 'Menunggu Approval'</li>
            </ol>
        </div>
        
        <div class='test-section'>
            <h2>🔗 Test Links</h2>
            <a href='http://127.0.0.1:8000/login' class='test-link' target='_blank'>
                🔐 Login Page
            </a>
            <a href='http://127.0.0.1:8000/ukms/imma' class='test-link success' target='_blank'>
                🏛️ IMMA UKM Detail
            </a>
            <a href='http://127.0.0.1:8000/ukms/imma/register' class='test-link' target='_blank'>
                📝 Registration Form
            </a>
        </div>
        
        <div class='test-section'>
            <h2>✅ Expected Behavior</h2>
            <ul>
                <li>✅ Removed member sees 'Daftar Keanggotaan' button</li>
                <li>✅ Registration form is accessible</li>
                <li>✅ Form submission creates new pending application</li>
                <li>✅ Status changes to 'Menunggu Approval'</li>
                <li>✅ Ketua can approve/reject the new application</li>
            </ul>
        </div>
        
        <div class='test-section'>
            <h2>🔍 Debug Information</h2>
            <div class='condition-list'>
                <strong>View Logic Conditions:</strong><br>";
    
    foreach ($conditions as $condition => $result) {
        $testHtml .= "                {$condition}: " . ($result ? 'TRUE' : 'FALSE') . "<br>";
    }
    
    $testHtml .= "
            </div>
        </div>
    </div>
</body>
</html>";
    
    file_put_contents(public_path('reregistration-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/reregistration-test.html\n";
    
    echo "\n=== RE-REGISTRATION TEST COMPLETED ===\n";
    echo "🎯 Summary:\n";
    echo "   - Logic should work: " . ($showButton && $canAccessForm ? 'YES ✅' : 'NO ❌') . "\n";
    echo "   - Button should show: " . ($showButton ? 'YES ✅' : 'NO ❌') . "\n";
    echo "   - Form accessible: " . ($canAccessForm ? 'YES ✅' : 'NO ❌') . "\n";
    echo "\n🔗 Test manually: http://127.0.0.1:8000/reregistration-test.html\n";
    echo "🔗 UKM page: http://127.0.0.1:8000/ukms/imma\n";
    echo "📧 Login as: {$student->email}\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
