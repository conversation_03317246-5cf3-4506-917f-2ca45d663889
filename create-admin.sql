-- Create admin user directly in database
INSERT INTO users (
    nim, 
    name, 
    email, 
    password, 
    phone, 
    gender, 
    faculty, 
    major, 
    batch, 
    role, 
    status, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    'ADMIN001',
    'Administrator',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    '081234567890',
    'male',
    'Administrasi',
    'Sistem Informasi',
    '2024',
    'admin',
    'active',
    NOW(),
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    password = '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    status = 'active',
    updated_at = NOW();

-- Create test student user
INSERT INTO users (
    nim, 
    name, 
    email, 
    password, 
    phone, 
    gender, 
    faculty, 
    major, 
    batch, 
    role, 
    status, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    '1301210001',
    'Test Student',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: student123
    '081234567891',
    'male',
    'Informatika',
    'Sistem Informasi',
    '2021',
    'student',
    'active',
    NOW(),
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    password = '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    status = 'active',
    updated_at = NOW();

-- Create test ketua UKM user
INSERT INTO users (
    nim, 
    name, 
    email, 
    password, 
    phone, 
    gender, 
    faculty, 
    major, 
    batch, 
    role, 
    status, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    '1301210002',
    'Test Ketua UKM',
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: ketua123
    '081234567892',
    'female',
    'Informatika',
    'Teknik Informatika',
    '2021',
    'ketua_ukm',
    'active',
    NOW(),
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    password = '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    status = 'active',
    updated_at = NOW();

-- Display created users
SELECT 
    id,
    nim,
    name,
    email,
    role,
    status,
    created_at
FROM users 
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY role, name;
