<?php

echo "=== TESTING EVENT REGISTRATION LOGIC FIX ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing event registration logic...\n";
    
    // Find an event for testing
    $event = \App\Models\Event::where('status', 'published')
                             ->where('registration_open', true)
                             ->first();
    
    if (!$event) {
        // Create a test event
        $ukm = \App\Models\Ukm::first();
        if (!$ukm) {
            echo "   ❌ No UKM found for testing\n";
            exit;
        }
        
        $event = \App\Models\Event::create([
            'ukm_id' => $ukm->id,
            'title' => 'Test Event Registration Logic',
            'slug' => 'test-event-registration-logic',
            'description' => 'Test event for registration logic',
            'start_datetime' => now()->addDays(7),
            'end_datetime' => now()->addDays(7)->addHours(3),
            'location' => 'Test Location',
            'status' => 'published',
            'registration_open' => true,
            'max_participants' => 50,
        ]);
        echo "   ✅ Created test event: {$event->title}\n";
    } else {
        echo "   ✅ Using existing event: {$event->title}\n";
    }
    
    echo "2. Testing with student user...\n";
    
    // Find a student for testing
    $student = \App\Models\User::where('role', 'student')->first();
    if (!$student) {
        echo "   ❌ No student found for testing\n";
        exit;
    }
    
    echo "   ✅ Testing with student: {$student->name}\n";
    
    // Clean up any existing registration for this test
    \App\Models\EventRegistration::where('event_id', $event->id)
                                ->where('user_id', $student->id)
                                ->delete();
    
    echo "3. Testing first registration attempt...\n";
    
    // Test registration logic
    $existingRegistration = $event->registrations()->where('user_id', $student->id)->first();
    echo "   Existing registration: " . ($existingRegistration ? 'Yes' : 'No') . "\n";
    
    if ($existingRegistration && $existingRegistration->status === 'approved') {
        echo "   ❌ Should not show error 'Anda sudah terdaftar'\n";
    } else {
        echo "   ✅ Should allow registration\n";
    }
    
    // Simulate registration creation
    $registration = \App\Models\EventRegistration::create([
        'user_id' => $student->id,
        'event_id' => $event->id,
        'status' => 'approved', // Auto-approved
        'motivation' => 'Test motivation for registration logic',
        'availability_form' => ['full_attendance' => 'yes'],
        'registration_notes' => 'Test registration',
        'additional_data' => [],
        'payment_status' => 'verified',
        'approved_at' => now(),
    ]);
    
    echo "   ✅ Registration created with status: {$registration->status}\n";
    
    echo "4. Testing registration status check...\n";
    
    // Test the show method logic
    $userRegistration = $event->registrations()
                             ->where('user_id', $student->id)
                             ->first();
    
    $isRegistered = false;
    if ($userRegistration && $userRegistration->status === 'approved') {
        $isRegistered = true;
    }
    
    echo "   User registration found: " . ($userRegistration ? 'Yes' : 'No') . "\n";
    echo "   Registration status: " . ($userRegistration ? $userRegistration->status : 'N/A') . "\n";
    echo "   Is registered (approved): " . ($isRegistered ? 'Yes' : 'No') . "\n";
    
    $canRegister = $event->isRegistrationOpen() && !$isRegistered;
    echo "   Can register: " . ($canRegister ? 'Yes' : 'No') . "\n";
    
    if ($isRegistered) {
        echo "   ✅ Should show 'Anda sudah terdaftar' (info message, not error)\n";
        echo "   ✅ Should not show registration button\n";
    } else {
        echo "   ✅ Should show registration button\n";
    }
    
    echo "5. Testing second registration attempt (should be blocked)...\n";
    
    // Test what happens if user tries to register again
    $existingRegistration = $event->registrations()->where('user_id', $student->id)->first();
    
    if ($existingRegistration && $existingRegistration->status === 'approved') {
        echo "   ✅ Should show info message: 'Anda sudah terdaftar untuk event ini'\n";
        echo "   ✅ Should NOT show error message\n";
        echo "   ✅ Should redirect to event page with info (not error)\n";
    } else {
        echo "   ❌ Logic error: should detect existing approved registration\n";
    }
    
    echo "6. Testing registration with pending status (edge case)...\n";
    
    // Create another student for testing pending status
    $student2 = \App\Models\User::where('role', 'student')->skip(1)->first();
    if ($student2) {
        echo "   ✅ Testing with second student: {$student2->name}\n";
        
        // Clean up
        \App\Models\EventRegistration::where('event_id', $event->id)
                                    ->where('user_id', $student2->id)
                                    ->delete();
        
        // Create pending registration
        $pendingRegistration = \App\Models\EventRegistration::create([
            'user_id' => $student2->id,
            'event_id' => $event->id,
            'status' => 'pending', // Not approved yet
            'motivation' => 'Test pending registration',
            'availability_form' => ['full_attendance' => 'yes'],
            'registration_notes' => 'Test pending',
            'additional_data' => [],
            'payment_status' => 'pending',
        ]);
        
        echo "   Created pending registration with status: {$pendingRegistration->status}\n";
        
        // Test logic with pending registration
        $existingReg = $event->registrations()->where('user_id', $student2->id)->first();
        $shouldAllowRegistration = !($existingReg && $existingReg->status === 'approved');
        
        echo "   Should allow re-registration: " . ($shouldAllowRegistration ? 'Yes' : 'No') . "\n";
        
        if ($shouldAllowRegistration) {
            echo "   ✅ Pending registration can be updated to approved\n";
            
            // Simulate updating pending to approved
            $pendingRegistration->update([
                'status' => 'approved',
                'approved_at' => now(),
            ]);
            
            echo "   ✅ Registration updated to approved status\n";
        }
        
        // Clean up
        $pendingRegistration->delete();
    }
    
    echo "7. Testing message types...\n";
    
    echo "   Expected messages:\n";
    echo "   ✅ Success: 'Pendaftaran berhasil! Anda telah terdaftar untuk kegiatan ini.'\n";
    echo "   ✅ Info (already registered): 'Anda sudah terdaftar untuk event ini.'\n";
    echo "   ❌ Error (should not appear): 'Anda sudah terdaftar untuk event ini.'\n";
    
    echo "8. Testing participant count update...\n";
    
    $participantCount = $event->registrations()->where('status', 'approved')->count();
    echo "   Current approved registrations: {$participantCount}\n";
    
    // Test updateParticipantCount method
    $event->updateParticipantCount();
    $event->refresh();
    
    echo "   Updated participant count: {$event->current_participants}\n";
    
    if ($event->current_participants === $participantCount) {
        echo "   ✅ Participant count updated correctly\n";
    } else {
        echo "   ⚠️  Participant count mismatch\n";
    }
    
    echo "9. Testing complete registration workflow...\n";
    
    echo "   Workflow simulation:\n";
    echo "   1. Student visits event page → Sees 'Daftar Sekarang' button\n";
    echo "   2. Student clicks register → Goes to registration form\n";
    echo "   3. Student fills form and submits → Registration created with status 'approved'\n";
    echo "   4. Student redirected to event page → Sees success message\n";
    echo "   5. Student visits event page again → Sees 'Anda sudah terdaftar' (info)\n";
    echo "   6. Registration button hidden → Cannot register again\n";
    
    // Test the complete workflow
    $testStudent = \App\Models\User::where('role', 'student')->skip(2)->first();
    if ($testStudent) {
        echo "   Testing complete workflow with: {$testStudent->name}\n";
        
        // Clean up
        \App\Models\EventRegistration::where('event_id', $event->id)
                                    ->where('user_id', $testStudent->id)
                                    ->delete();
        
        // Step 1: Check if can register
        $canRegister = $event->isRegistrationOpen() && 
                      !$event->registrations()
                             ->where('user_id', $testStudent->id)
                             ->where('status', 'approved')
                             ->exists();
        
        echo "   Step 1 - Can register: " . ($canRegister ? 'Yes' : 'No') . " ✅\n";
        
        // Step 3: Create registration (simulate form submission)
        $newRegistration = \App\Models\EventRegistration::create([
            'user_id' => $testStudent->id,
            'event_id' => $event->id,
            'status' => 'approved',
            'motivation' => 'Complete workflow test',
            'availability_form' => ['full_attendance' => 'yes'],
            'approved_at' => now(),
        ]);
        
        echo "   Step 3 - Registration created: approved ✅\n";
        
        // Step 5: Check status after registration
        $isNowRegistered = $event->registrations()
                                ->where('user_id', $testStudent->id)
                                ->where('status', 'approved')
                                ->exists();
        
        echo "   Step 5 - Is now registered: " . ($isNowRegistered ? 'Yes' : 'No') . " ✅\n";
        
        // Step 6: Check if can register again
        $canRegisterAgain = $event->isRegistrationOpen() && !$isNowRegistered;
        
        echo "   Step 6 - Can register again: " . ($canRegisterAgain ? 'Yes' : 'No') . " ✅\n";
        
        // Clean up
        $newRegistration->delete();
    }
    
    echo "10. Cleanup test data...\n";
    
    // Clean up test data
    $registration->delete();
    
    if ($event->title === 'Test Event Registration Logic') {
        $event->delete();
        echo "   ✅ Test event cleaned up\n";
    }
    
    echo "\n=== EVENT REGISTRATION LOGIC FIX TEST COMPLETED ===\n";
    echo "✅ Event registration logic fix verified!\n";
    echo "\nKey Fixes Applied:\n";
    echo "🔧 ISSUE: 'Anda sudah terdaftar' error appears during registration\n";
    echo "🔧 SOLUTION: Only check for approved registrations, not all registrations\n";
    echo "🔧 ISSUE: Cannot handle pending registrations properly\n";
    echo "🔧 SOLUTION: Allow updating pending registrations to approved\n";
    echo "🔧 ISSUE: Wrong message type (error instead of info)\n";
    echo "🔧 SOLUTION: Use 'info' message for already registered users\n";
    echo "\nExpected Behavior:\n";
    echo "✅ First registration → Success message: 'Pendaftaran berhasil!'\n";
    echo "✅ Already registered → Info message: 'Anda sudah terdaftar'\n";
    echo "✅ Pending registration → Can be updated to approved\n";
    echo "✅ Registration button → Hidden after successful registration\n";
    echo "✅ Participant count → Updated automatically\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
