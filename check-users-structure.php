<?php
echo "=== CHECKING USERS TABLE STRUCTURE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check table structure
    echo "📋 USERS Table Structure:\n";
    $result = $pdo->query("DESCRIBE users");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $null = $row['Null'] == 'YES' ? 'NULL' : 'NOT NULL';
        $default = $row['Default'] ? "DEFAULT '{$row['Default']}'" : '';
        echo "   • {$row['Field']} - {$row['Type']} {$null} {$default}\n";
    }
    
    echo "\n📊 Current Users:\n";
    $result = $pdo->query("SELECT id, name, email, role, status FROM users ORDER BY role, name");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $role = $row['role'] ?: 'NULL';
        echo "   • {$row['name']} ({$row['email']}) - Role: {$role}, Status: {$row['status']}\n";
    }
    
    echo "\n📈 Role Statistics:\n";
    $result = $pdo->query("SELECT COALESCE(role, 'NULL') as role, COUNT(*) as count FROM users GROUP BY role");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   • {$row['role']}: {$row['count']} users\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== CHECK COMPLETE ===\n";
?>
