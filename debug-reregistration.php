<?php
/**
 * Debug re-registration issue for removed members
 */

echo "=== DEBUGGING RE-REGISTRATION ISSUE ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Setting up test scenario...\n";
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    $student = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if (!$ukm || !$student) {
        echo "   ❌ UKM or student not found\n";
        exit;
    }
    
    echo "   ✅ UKM: {$ukm->name}\n";
    echo "   ✅ Student: {$student->name}\n";
    
    echo "\n2. Simulating member removal...\n";
    
    // First, make sure student is an active member
    $ukm->members()->detach($student->id);
    $ukm->members()->attach($student->id, [
        'role' => 'member',
        'status' => 'active',
        'joined_date' => now(),
        'applied_at' => now(),
    ]);
    
    echo "   ✅ Added student as active member\n";
    
    // Now remove the member (simulate ketua removing member)
    $ukm->members()->detach($student->id);
    
    echo "   ✅ Removed student from UKM (detached completely)\n";
    
    echo "\n3. Testing registration logic...\n";
    
    // Test the logic from UkmController show method
    $membershipStatus = null;
    $membership = $ukm->members()->where('ukm_members.user_id', $student->id)->first();
    
    if ($membership) {
        $membershipStatus = $membership->pivot->status;
        echo "   📊 Membership exists with status: {$membershipStatus}\n";
    } else {
        echo "   📊 No membership record found (completely removed)\n";
    }
    
    echo "   📊 Membership status variable: " . ($membershipStatus ?: 'null') . "\n";
    
    // Test registration form access logic
    $existingMembership = $ukm->members()->where('ukm_members.user_id', $student->id)->first();
    
    echo "\n4. Testing registration form access...\n";
    
    if ($existingMembership) {
        $status = $existingMembership->pivot->status;
        echo "   📊 Existing membership status: {$status}\n";
        
        if ($status === 'active') {
            echo "   ❌ Should redirect: Already active member\n";
        } elseif ($status === 'pending') {
            echo "   ❌ Should redirect: Pending application\n";
        } else {
            echo "   ✅ Should allow: Can re-register (status: {$status})\n";
        }
    } else {
        echo "   ✅ Should allow: No existing membership (completely removed)\n";
    }
    
    echo "\n5. Testing view button logic...\n";
    
    // Test the view logic for showing registration button
    $showRegisterButton = false;
    $buttonText = '';
    
    if ($membershipStatus === 'active') {
        echo "   Button: 'Sudah Bergabung' (green badge)\n";
    } elseif ($membershipStatus === 'pending') {
        echo "   Button: 'Menunggu Approval' (yellow badge)\n";
    } elseif ($ukm->status === 'active' && $ukm->registration_status === 'open' && $ukm->current_members < $ukm->max_members) {
        if (!$membershipStatus || $membershipStatus === 'inactive' || $membershipStatus === 'alumni') {
            $showRegisterButton = true;
            $buttonText = ($membershipStatus === 'inactive' || $membershipStatus === 'alumni') ? 'Daftar Ulang' : 'Daftar Keanggotaan';
            echo "   ✅ Button: '{$buttonText}' (blue button)\n";
        } else {
            echo "   ❌ No button shown (unexpected status: " . ($membershipStatus ?: 'null') . ")\n";
        }
    } else {
        echo "   ❌ No button: UKM conditions not met\n";
        echo "      UKM status: {$ukm->status}\n";
        echo "      Registration status: " . ($ukm->registration_status ?? 'open') . "\n";
        echo "      Members: {$ukm->current_members}/{$ukm->max_members}\n";
    }
    
    echo "\n6. Identifying the issue...\n";
    
    if (!$showRegisterButton && !$membershipStatus) {
        echo "   🔍 ISSUE FOUND: Logic doesn't handle null membership status properly\n";
        echo "   🔧 SOLUTION: Update view logic to show button when membershipStatus is null\n";
    } else {
        echo "   ✅ Logic should work correctly\n";
    }
    
    echo "\n7. Testing route access...\n";
    
    // Test if registration form route is accessible
    try {
        $request = \Illuminate\Http\Request::create("/ukms/{$ukm->slug}/register", 'GET');
        $request->setUserResolver(function() use ($student) {
            return $student;
        });
        
        // Simulate the registration form method
        $controller = new \App\Http\Controllers\UkmController();
        
        echo "   📋 Testing registration form access...\n";
        echo "   📋 UKM: {$ukm->name}\n";
        echo "   📋 Student: {$student->name}\n";
        echo "   📋 Existing membership: " . ($existingMembership ? 'Yes' : 'No') . "\n";
        
    } catch (\Exception $e) {
        echo "   ❌ Route test error: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== DEBUG COMPLETED ===\n";
    echo "📋 Summary:\n";
    echo "   - Member completely removed: " . (!$existingMembership ? 'Yes ✅' : 'No ❌') . "\n";
    echo "   - Membership status: " . ($membershipStatus ?: 'null') . "\n";
    echo "   - Should show register button: " . ($showRegisterButton ? 'Yes ✅' : 'No ❌') . "\n";
    echo "   - Button text: " . ($buttonText ?: 'None') . "\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
