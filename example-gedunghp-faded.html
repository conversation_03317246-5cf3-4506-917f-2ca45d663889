<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gedunghp.png Faded Examples</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom fade effects for Gedunghp.png */
        .fade-10-percent::before,
        .fade-10-percent::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 10%;
            pointer-events: none;
            z-index: 10;
        }

        .fade-10-percent::before {
            left: 0;
            background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0.8), transparent);
        }

        .fade-10-percent::after {
            right: 0;
            background: linear-gradient(to left, rgba(255,255,255,1), rgba(255,255,255,0.8), transparent);
        }

        .fade-15-percent::before,
        .fade-15-percent::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 15%;
            pointer-events: none;
            z-index: 10;
        }

        .fade-15-percent::before {
            left: 0;
            background: linear-gradient(to right, rgba(255,255,255,0.95), rgba(255,255,255,0.7), transparent);
        }

        .fade-15-percent::after {
            right: 0;
            background: linear-gradient(to left, rgba(255,255,255,0.95), rgba(255,255,255,0.7), transparent);
        }

        .fade-soft::before,
        .fade-soft::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 10%;
            pointer-events: none;
            z-index: 10;
        }

        .fade-soft::before {
            left: 0;
            background: linear-gradient(to right, rgba(255,255,255,0.6), rgba(255,255,255,0.3), transparent);
        }

        .fade-soft::after {
            right: 0;
            background: linear-gradient(to left, rgba(255,255,255,0.6), rgba(255,255,255,0.3), transparent);
        }

        .fade-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                rgba(255,255,255,0.8) 0%,
                transparent 10%,
                transparent 90%,
                rgba(255,255,255,0.8) 100%
            );
            z-index: 10;
        }

        .fade-blue-theme::before,
        .fade-blue-theme::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 10%;
            pointer-events: none;
            z-index: 10;
        }

        .fade-blue-theme::before {
            left: 0;
            background: linear-gradient(to right, rgba(59, 130, 246, 0.3), rgba(59, 130, 246, 0.1), transparent);
        }

        .fade-blue-theme::after {
            right: 0;
            background: linear-gradient(to left, rgba(59, 130, 246, 0.3), rgba(59, 130, 246, 0.1), transparent);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4">
        <h1 class="text-4xl font-bold text-center text-gray-800 mb-8">🏢 Gedunghp.png dengan Efek Faded</h1>
        
        <!-- Grid Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Original Implementation (10% Fade) -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">✅ Implementasi Saat Ini (10% Fade)</h3>
                </div>
                <div class="relative overflow-hidden">
                    <img src="http://127.0.0.1:8000/storage/Gedunghp.png" 
                         alt="Gedung HP" 
                         class="w-full h-auto object-cover">
                    
                    <!-- Left Fade (10% width) -->
                    <div class="absolute top-0 left-0 bottom-0 w-[10%] bg-gradient-to-r from-white via-white/80 to-transparent pointer-events-none z-10"></div>
                    
                    <!-- Right Fade (10% width) -->
                    <div class="absolute top-0 right-0 bottom-0 w-[10%] bg-gradient-to-l from-white via-white/80 to-transparent pointer-events-none z-10"></div>
                </div>
                <div class="p-4">
                    <p class="text-sm text-gray-600">Fade putih 10% dari kiri dan kanan dengan opacity bertingkat untuk efek natural.</p>
                </div>
            </div>

            <!-- Alternative 1: CSS Class Implementation -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🎨 CSS Class Version</h3>
                </div>
                <div class="relative overflow-hidden fade-10-percent">
                    <img src="http://127.0.0.1:8000/storage/Gedunghp.png" 
                         alt="Gedung HP" 
                         class="w-full h-auto object-cover">
                </div>
                <div class="p-4">
                    <p class="text-sm text-gray-600">Menggunakan CSS class <code class="bg-gray-100 px-2 py-1 rounded">.fade-10-percent</code> untuk reusability.</p>
                </div>
            </div>

            <!-- Alternative 2: Wider Fade (15%) -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">📏 Fade Lebih Lebar (15%)</h3>
                </div>
                <div class="relative overflow-hidden fade-15-percent">
                    <img src="http://127.0.0.1:8000/storage/Gedunghp.png" 
                         alt="Gedung HP" 
                         class="w-full h-auto object-cover">
                </div>
                <div class="p-4">
                    <p class="text-sm text-gray-600">Fade 15% untuk efek yang lebih dramatis dan fokus lebih ke tengah gambar.</p>
                </div>
            </div>

            <!-- Alternative 3: Soft Fade -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🌟 Soft Fade</h3>
                </div>
                <div class="relative overflow-hidden fade-soft">
                    <img src="http://127.0.0.1:8000/storage/Gedunghp.png" 
                         alt="Gedung HP" 
                         class="w-full h-auto object-cover">
                </div>
                <div class="p-4">
                    <p class="text-sm text-gray-600">Fade dengan opacity lebih rendah untuk efek yang lebih subtle dan natural.</p>
                </div>
            </div>

            <!-- Alternative 4: Single Gradient Overlay -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🌈 Gradient Overlay</h3>
                </div>
                <div class="relative overflow-hidden fade-gradient">
                    <img src="http://127.0.0.1:8000/storage/Gedunghp.png" 
                         alt="Gedung HP" 
                         class="w-full h-auto object-cover">
                </div>
                <div class="p-4">
                    <p class="text-sm text-gray-600">Single gradient overlay yang mencakup seluruh gambar dengan fade di kedua sisi.</p>
                </div>
            </div>

            <!-- Alternative 5: Blue Theme Fade -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">💙 Blue Theme Fade</h3>
                </div>
                <div class="relative overflow-hidden fade-blue-theme">
                    <img src="http://127.0.0.1:8000/storage/Gedunghp.png" 
                         alt="Gedung HP" 
                         class="w-full h-auto object-cover">
                </div>
                <div class="p-4">
                    <p class="text-sm text-gray-600">Fade dengan warna biru yang matching dengan tema Telkom untuk konsistensi brand.</p>
                </div>
            </div>
        </div>

        <!-- Implementation Guide -->
        <div class="mt-12 bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">📝 Panduan Implementasi</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">🎯 Implementasi Saat Ini</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <pre class="text-sm text-gray-700 overflow-x-auto"><code>&lt;div class="relative overflow-hidden rounded-lg"&gt;
    &lt;img src="Gedunghp.png" class="w-full h-auto object-cover"&gt;
    
    &lt;!-- Left Fade (10% width) --&gt;
    &lt;div class="absolute top-0 left-0 bottom-0 w-[10%] 
                bg-gradient-to-r from-white via-white/80 
                to-transparent pointer-events-none z-10"&gt;&lt;/div&gt;
    
    &lt;!-- Right Fade (10% width) --&gt;
    &lt;div class="absolute top-0 right-0 bottom-0 w-[10%] 
                bg-gradient-to-l from-white via-white/80 
                to-transparent pointer-events-none z-10"&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">⚙️ CSS Class Alternative</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <pre class="text-sm text-gray-700 overflow-x-auto"><code>/* CSS */
.fade-10-percent::before,
.fade-10-percent::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 10%;
    pointer-events: none;
    z-index: 10;
}

.fade-10-percent::before {
    left: 0;
    background: linear-gradient(to right, 
        rgba(255,255,255,1), 
        rgba(255,255,255,0.8), 
        transparent);
}

.fade-10-percent::after {
    right: 0;
    background: linear-gradient(to left, 
        rgba(255,255,255,1), 
        rgba(255,255,255,0.8), 
        transparent);
}

/* HTML */
&lt;div class="relative overflow-hidden fade-10-percent"&gt;
    &lt;img src="Gedunghp.png" class="w-full h-auto"&gt;
&lt;/div&gt;</code></pre>
                    </div>
                </div>
            </div>

            <div class="mt-8">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">💡 Tips Optimasi</h3>
                <ul class="space-y-2 text-gray-600">
                    <li>• <strong>Persentase Fade:</strong> 10% memberikan efek natural, 15% lebih dramatis</li>
                    <li>• <strong>Opacity Gradient:</strong> Gunakan <code class="bg-gray-100 px-2 py-1 rounded">via-white/80</code> untuk transisi yang smooth</li>
                    <li>• <strong>Z-index:</strong> Pastikan fade overlay berada di atas gambar dengan <code class="bg-gray-100 px-2 py-1 rounded">z-10</code></li>
                    <li>• <strong>Pointer Events:</strong> Gunakan <code class="bg-gray-100 px-2 py-1 rounded">pointer-events-none</code> agar fade tidak menghalangi interaksi</li>
                    <li>• <strong>Responsive:</strong> Gunakan <code class="bg-gray-100 px-2 py-1 rounded">w-[10%]</code> untuk persentase yang konsisten di semua ukuran</li>
                </ul>
            </div>
        </div>

        <!-- Test with Different Backgrounds -->
        <div class="mt-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg p-8 text-white">
            <h2 class="text-2xl font-bold mb-6">🧪 Test dengan Background Berbeda</h2>
            <div class="bg-white/10 rounded-lg p-6">
                <div class="relative overflow-hidden rounded-lg">
                    <img src="http://127.0.0.1:8000/storage/Gedunghp.png" 
                         alt="Gedung HP" 
                         class="w-full h-auto object-cover">
                    
                    <!-- Adjusted fade for colored background -->
                    <div class="absolute top-0 left-0 bottom-0 w-[10%] bg-gradient-to-r from-blue-500 via-blue-500/60 to-transparent pointer-events-none z-10"></div>
                    <div class="absolute top-0 right-0 bottom-0 w-[10%] bg-gradient-to-l from-purple-600 via-purple-600/60 to-transparent pointer-events-none z-10"></div>
                </div>
                <p class="text-center mt-4 text-blue-100">Fade disesuaikan dengan warna background untuk integrasi yang seamless</p>
            </div>
        </div>
    </div>
</body>
</html>
