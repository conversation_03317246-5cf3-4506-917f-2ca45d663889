<?php

echo "=== UPLOADING TELKOM LOGO TO UKM ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Find UKM Sistem Informasi
    $ukm = \App\Models\Ukm::where('name', 'like', '%sistem informasi%')->first();
    
    if (!$ukm) {
        echo "❌ UKM Sistem Informasi not found\n";
        exit;
    }
    
    echo "✅ Found UKM: {$ukm->name}\n";
    echo "   Current logo: " . ($ukm->logo ?: 'None') . "\n";
    
    // Check if Telkom.png exists in public/storage
    $sourcePath = public_path('storage/Telkom.png');
    if (!file_exists($sourcePath)) {
        echo "❌ Telkom.png not found at: {$sourcePath}\n";
        exit;
    }
    
    echo "✅ Source logo found: {$sourcePath}\n";
    echo "   File size: " . round(filesize($sourcePath) / 1024, 2) . " KB\n";
    
    // Create destination directory if not exists
    $logoDir = storage_path('app/public/ukms/logos');
    if (!is_dir($logoDir)) {
        mkdir($logoDir, 0755, true);
        echo "✅ Created directory: {$logoDir}\n";
    }
    
    // Copy logo to proper location
    $logoFilename = 'telkom-logo.png';
    $logoPath = 'ukms/logos/' . $logoFilename;
    $destinationPath = storage_path('app/public/' . $logoPath);
    
    if (copy($sourcePath, $destinationPath)) {
        echo "✅ Logo copied to: {$destinationPath}\n";
        
        // Update UKM with logo path
        $ukm->update(['logo' => $logoPath]);
        echo "✅ UKM updated with logo path: {$logoPath}\n";
        
        // Verify the update
        $ukm->refresh();
        echo "✅ Verification - UKM logo: {$ukm->logo}\n";
        
        // Check if file is accessible via web
        $webPath = asset('storage/' . $logoPath);
        echo "✅ Web URL: {$webPath}\n";
        
    } else {
        echo "❌ Failed to copy logo file\n";
    }
    
    echo "\n=== LOGO UPLOAD COMPLETE ===\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
