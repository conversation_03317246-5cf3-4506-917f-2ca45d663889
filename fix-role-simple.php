<?php

// Simple script to fix role enum
echo "Fixing role enum for ketua_ukm support...\n";

// Database configuration
$host = 'localhost';
$dbname = 'ukm_web_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database\n";
    
    // Check current enum
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Current enum: " . $result['Type'] . "\n";
    
    // Update enum to include ketua_ukm
    $sql = "ALTER TABLE users MODIFY COLUMN role ENUM('student', 'ketua_ukm', 'admin') DEFAULT 'student'";
    $pdo->exec($sql);
    
    echo "✅ Updated role enum successfully!\n";
    
    // Verify the change
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "New enum: " . $result['Type'] . "\n";
    
    // Test by updating a user
    $stmt = $pdo->query("SELECT id, name, role FROM users WHERE role = 'student' LIMIT 1");
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "Testing with user: " . $user['name'] . " (current role: " . $user['role'] . ")\n";
        
        // Update to ketua_ukm
        $updateStmt = $pdo->prepare("UPDATE users SET role = 'ketua_ukm' WHERE id = ?");
        $updateStmt->execute([$user['id']]);
        
        // Check if it worked
        $checkStmt = $pdo->prepare("SELECT role FROM users WHERE id = ?");
        $checkStmt->execute([$user['id']]);
        $newRole = $checkStmt->fetchColumn();
        
        echo "After update: " . $newRole . "\n";
        
        if ($newRole === 'ketua_ukm') {
            echo "✅ SUCCESS: Role assignment works!\n";
            
            // Revert back
            $revertStmt = $pdo->prepare("UPDATE users SET role = 'student' WHERE id = ?");
            $revertStmt->execute([$user['id']]);
            echo "Reverted back to student\n";
        } else {
            echo "❌ FAILED: Role assignment failed\n";
        }
    }
    
    echo "\n=== FIX COMPLETED ===\n";
    echo "You can now assign 'ketua_ukm' role to users!\n";
    
} catch (PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
