@echo off
echo ========================================
echo   FINAL REGISTER SUCCESS ROUTE FIX
echo ========================================
echo.

echo [1/4] Clearing all caches completely...
php artisan route:clear
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan optimize:clear

echo.
echo [2/4] Testing route availability...
php -r "
require_once 'vendor/autoload.php';
\$app = require_once 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo 'Testing routes...' . PHP_EOL;

// Test main route
try {
    \$url1 = route('register.success');
    echo 'SUCCESS: Main route works: ' . \$url1 . PHP_EOL;
} catch (Exception \$e) {
    echo 'ERROR: Main route failed: ' . \$e->getMessage() . PHP_EOL;
}

// Test backup route
try {
    \$url2 = route('register.success.backup');
    echo 'SUCCESS: Backup route works: ' . \$url2 . PHP_EOL;
} catch (Exception \$e) {
    echo 'ERROR: Backup route failed: ' . \$e->getMessage() . PHP_EOL;
}

// Test direct URL access
echo 'Direct URL test: http://127.0.0.1:8000/register/success' . PHP_EOL;
"

echo.
echo [3/4] Checking files exist...
echo Checking auth.php...
if exist routes\auth.php (
    echo ✓ routes/auth.php exists
) else (
    echo ✗ routes/auth.php missing
)

echo Checking register-success view...
if exist resources\views\auth\register-success.blade.php (
    echo ✓ register-success.blade.php exists
) else (
    echo ✗ register-success.blade.php missing
)

echo.
echo [4/4] Starting server...
echo.
echo ========================================
echo   ROUTE FIX COMPLETED
echo ========================================
echo.
echo ROUTE CONFIGURATION:
echo 1. Main route: register.success (from auth.php)
echo 2. Backup route: register.success.backup (from web.php)
echo 3. Controller has fallback logic
echo.
echo REGISTRATION FLOW:
echo 1. User goes to /register
echo 2. Fills form and submits
echo 3. Redirects to /register/success
echo 4. Shows success page with:
echo    - Congratulations message
echo    - Status: Waiting for admin approval
echo    - WhatsApp: 081382640946
echo    - Admin email: <EMAIL>
echo.
echo ADMIN APPROVAL:
echo 1. Admin login: <EMAIL> / admin123
echo 2. Go to Admin Panel → Kelola Mahasiswa
echo 3. Find user with status "Menunggu Persetujuan"
echo 4. Edit user → Change status to "Aktif"
echo 5. User can now login
echo.
echo TEST URLS:
echo - Registration: http://127.0.0.1:8000/register
echo - Success page: http://127.0.0.1:8000/register/success
echo - Admin panel: http://127.0.0.1:8000/admin
echo.
echo Starting Laravel server...
echo.

php artisan serve --host=127.0.0.1 --port=8000
