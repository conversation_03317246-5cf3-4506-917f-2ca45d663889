<?php

echo "=== TESTING STUDENT UKM STATUS TRACKING FEATURE ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing UkmController myApplications method...\n";
    
    // Test controller instantiation
    $controller = new \App\Http\Controllers\UkmController();
    echo "   ✅ UkmController instantiated\n";
    
    // Test if method exists
    if (method_exists($controller, 'myApplications')) {
        echo "   ✅ Method myApplications exists\n";
    } else {
        echo "   ❌ Method myApplications missing\n";
    }
    
    echo "2. Testing route...\n";
    
    try {
        $url = route('ukms.my-applications');
        echo "   ✅ Route ukms.my-applications: {$url}\n";
    } catch (Exception $e) {
        echo "   ❌ Route ukms.my-applications: ERROR - " . $e->getMessage() . "\n";
    }
    
    echo "3. Testing view file...\n";
    
    $viewPath = resource_path('views/ukms/my-applications.blade.php');
    if (file_exists($viewPath)) {
        echo "   ✅ View ukms.my-applications: EXISTS\n";
        
        // Check if view contains key elements
        $viewContent = file_get_contents($viewPath);
        $checks = [
            'Status Pendaftaran UKM' => 'Page title',
            'activeApplications' => 'Active applications variable',
            'pendingApplications' => 'Pending applications variable',
            'rejectedApplications' => 'Rejected applications variable',
            'bg-green-50' => 'Active status styling',
            'bg-yellow-50' => 'Pending status styling',
            'bg-red-50' => 'Rejected status styling',
            'Daftar Ulang' => 'Re-apply functionality',
        ];
        
        foreach ($checks as $search => $description) {
            if (strpos($viewContent, $search) !== false) {
                echo "   ✅ {$description}: FOUND\n";
            } else {
                echo "   ❌ {$description}: MISSING\n";
            }
        }
    } else {
        echo "   ❌ View ukms.my-applications: MISSING\n";
    }
    
    echo "4. Testing User model relationships...\n";
    
    // Test User model methods
    $user = \App\Models\User::where('role', 'student')->first();
    if ($user) {
        echo "   Found student user: {$user->name}\n";
        
        // Test ukms relationship
        if (method_exists($user, 'ukms')) {
            echo "   ✅ User ukms() relationship exists\n";
            
            // Test the relationship
            try {
                $ukms = $user->ukms()->get();
                echo "   ✅ User UKM memberships: {$ukms->count()} found\n";
            } catch (Exception $e) {
                echo "   ❌ Error accessing UKM memberships: " . $e->getMessage() . "\n";
            }
        } else {
            echo "   ❌ User ukms() relationship missing\n";
        }
        
        // Test activeUkms relationship
        if (method_exists($user, 'activeUkms')) {
            echo "   ✅ User activeUkms() relationship exists\n";
            
            try {
                $activeUkms = $user->activeUkms()->get();
                echo "   ✅ User active UKMs: {$activeUkms->count()} found\n";
            } catch (Exception $e) {
                echo "   ❌ Error accessing active UKMs: " . $e->getMessage() . "\n";
            }
        } else {
            echo "   ❌ User activeUkms() relationship missing\n";
        }
    } else {
        echo "   ⚠️  No student user found for testing\n";
    }
    
    echo "5. Testing UKM model relationships...\n";
    
    $ukm = \App\Models\Ukm::first();
    if ($ukm) {
        echo "   Found UKM: {$ukm->name}\n";
        
        // Test members relationship
        if (method_exists($ukm, 'members')) {
            echo "   ✅ UKM members() relationship exists\n";
            
            try {
                $members = $ukm->members()->get();
                echo "   ✅ UKM members: {$members->count()} found\n";
                
                // Test pivot data
                if ($members->count() > 0) {
                    $member = $members->first();
                    $pivotFields = ['status', 'applied_at', 'approved_at', 'rejected_at'];
                    foreach ($pivotFields as $field) {
                        if (isset($member->pivot->$field)) {
                            echo "   ✅ Pivot field {$field}: EXISTS\n";
                        } else {
                            echo "   ⚠️  Pivot field {$field}: NOT SET\n";
                        }
                    }
                }
            } catch (Exception $e) {
                echo "   ❌ Error accessing UKM members: " . $e->getMessage() . "\n";
            }
        } else {
            echo "   ❌ UKM members() relationship missing\n";
        }
    } else {
        echo "   ⚠️  No UKM found for testing\n";
    }
    
    echo "6. Testing navigation integration...\n";
    
    // Check if navigation has been updated
    $layoutPath = resource_path('views/layouts/app.blade.php');
    if (file_exists($layoutPath)) {
        $layoutContent = file_get_contents($layoutPath);
        if (strpos($layoutContent, 'ukms.my-applications') !== false) {
            echo "   ✅ Navigation link added to layout\n";
        } else {
            echo "   ❌ Navigation link missing from layout\n";
        }
        
        if (strpos($layoutContent, 'Status UKM') !== false) {
            echo "   ✅ Navigation text 'Status UKM' found\n";
        } else {
            echo "   ❌ Navigation text 'Status UKM' missing\n";
        }
    }
    
    // Check if dashboard has been updated
    $dashboardPath = resource_path('views/dashboard.blade.php');
    if (file_exists($dashboardPath)) {
        $dashboardContent = file_get_contents($dashboardPath);
        if (strpos($dashboardContent, 'ukms.my-applications') !== false) {
            echo "   ✅ Dashboard link added\n";
        } else {
            echo "   ❌ Dashboard link missing\n";
        }
        
        if (strpos($dashboardContent, 'Pantau status pendaftaran UKM') !== false) {
            echo "   ✅ Dashboard description found\n";
        } else {
            echo "   ❌ Dashboard description missing\n";
        }
    }
    
    echo "\n=== TESTING COMPLETED ===\n";
    echo "✅ Student UKM Status Tracking Feature Ready!\n";
    echo "\nFeature Summary:\n";
    echo "🎯 STUDENT UKM STATUS TRACKING:\n";
    echo "  ✅ View all UKM applications and memberships\n";
    echo "  ✅ Track application status (Active, Pending, Rejected, Inactive)\n";
    echo "  ✅ See application dates and rejection reasons\n";
    echo "  ✅ Quick stats dashboard with status counts\n";
    echo "  ✅ Re-apply functionality for rejected applications\n";
    echo "  ✅ Navigation integration in main menu and dashboard\n";
    echo "  ✅ Responsive design with status-based color coding\n";
    echo "\nAccess:\n";
    echo "  📱 Navigation: Status UKM (for students only)\n";
    echo "  🏠 Dashboard: Quick Actions → Status UKM\n";
    echo "  🔗 Direct URL: /my-ukm-applications\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
