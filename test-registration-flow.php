<?php

echo "=== TESTING REGISTRATION FLOW ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing route registration...\n";
    
    // Clear route cache first
    \Illuminate\Support\Facades\Artisan::call('route:clear');
    echo "   ✅ Route cache cleared\n";
    
    // Get fresh routes
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    
    // Check register.success route
    $registerSuccessRoute = $routes->getByName('register.success');
    
    if ($registerSuccessRoute) {
        echo "   ✅ register.success route found\n";
        echo "   URI: /{$registerSuccessRoute->uri()}\n";
        echo "   Controller: {$registerSuccessRoute->getActionName()}\n";
    } else {
        echo "   ❌ register.success route NOT FOUND\n";
        echo "   Available routes:\n";
        
        foreach ($routes->getRoutes() as $route) {
            if (str_contains($route->uri(), 'register')) {
                echo "      - {$route->getName()}: {$route->uri()}\n";
            }
        }
    }
    
    echo "\n2. Testing controller method...\n";
    
    try {
        $controller = new \App\Http\Controllers\Auth\RegisteredUserController();
        
        if (method_exists($controller, 'success')) {
            echo "   ✅ RegisteredUserController::success() method exists\n";
            
            // Test method call
            $response = $controller->success();
            echo "   ✅ Method callable, returns: " . get_class($response) . "\n";
            
        } else {
            echo "   ❌ RegisteredUserController::success() method missing\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Controller test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n3. Testing view existence...\n";
    
    $viewPath = resource_path('views/auth/register-success.blade.php');
    
    if (file_exists($viewPath)) {
        echo "   ✅ View file exists: {$viewPath}\n";
        
        // Check view content
        $content = file_get_contents($viewPath);
        if (str_contains($content, '081382640946')) {
            echo "   ✅ WhatsApp number found in view\n";
        } else {
            echo "   ❌ WhatsApp number missing in view\n";
        }
        
        if (str_contains($content, '<EMAIL>')) {
            echo "   ✅ Admin email found in view\n";
        } else {
            echo "   ❌ Admin email missing in view\n";
        }
        
    } else {
        echo "   ❌ View file missing: {$viewPath}\n";
    }
    
    echo "\n4. Testing route generation...\n";
    
    try {
        $url = route('register.success');
        echo "   ✅ Route URL generated successfully: {$url}\n";
    } catch (Exception $e) {
        echo "   ❌ Route generation failed: " . $e->getMessage() . "\n";
        
        // Try to manually register the route
        echo "   Attempting to manually register route...\n";
        
        \Illuminate\Support\Facades\Route::get('/register/success', [
            \App\Http\Controllers\Auth\RegisteredUserController::class, 
            'success'
        ])->name('register.success');
        
        try {
            $url = route('register.success');
            echo "   ✅ Manual route registration successful: {$url}\n";
        } catch (Exception $e2) {
            echo "   ❌ Manual route registration failed: " . $e2->getMessage() . "\n";
        }
    }
    
    echo "\n5. Simulating registration process...\n";
    
    // Create a test user to simulate registration
    $testUserData = [
        'nim' => '9999999999',
        'name' => 'Test Registration User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '081234567890',
        'gender' => 'male',
        'faculty' => 'Fakultas Informatika',
        'major' => 'Sistem Informasi',
        'batch' => '2024',
    ];
    
    // Check if test user already exists
    $existingUser = \App\Models\User::where('email', $testUserData['email'])->first();
    if ($existingUser) {
        $existingUser->delete();
        echo "   Deleted existing test user\n";
    }
    
    // Create user with pending status
    $user = \App\Models\User::create([
        'nim' => $testUserData['nim'],
        'name' => $testUserData['name'],
        'email' => $testUserData['email'],
        'password' => \Illuminate\Support\Facades\Hash::make($testUserData['password']),
        'phone' => $testUserData['phone'],
        'gender' => $testUserData['gender'],
        'faculty' => $testUserData['faculty'],
        'major' => $testUserData['major'],
        'batch' => $testUserData['batch'],
        'role' => 'student',
        'status' => 'pending',
    ]);
    
    echo "   ✅ Test user created with status: {$user->status}\n";
    echo "   User: {$user->name} ({$user->email})\n";
    
    // Clean up test user
    $user->delete();
    echo "   ✅ Test user cleaned up\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== REGISTRATION FLOW TEST COMPLETED ===\n";
echo "\nREGISTRATION WORKFLOW:\n";
echo "1. User goes to /register\n";
echo "2. Fills form and submits\n";
echo "3. POST /register creates user with status 'pending'\n";
echo "4. Redirects to /register/success\n";
echo "5. Shows success page with:\n";
echo "   - Congratulations message\n";
echo "   - Status: Waiting for admin approval\n";
echo "   - WhatsApp: 081382640946\n";
echo "   - Email: <EMAIL>\n";
echo "   - Expected approval time: 1-2 days\n";
echo "\nADMIN APPROVAL:\n";
echo "1. Admin login and go to user management\n";
echo "2. Find user with status 'Menunggu Persetujuan'\n";
echo "3. Edit user and change status to 'Aktif'\n";
echo "4. User can now login\n";
