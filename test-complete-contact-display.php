<?php
echo "=== TESTING COMPLETE CONTACT DISPLAY ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    echo "📊 Complete UKM Information Display Test:\n\n";
    
    // Test query that shows both contact info and leader info
    $result = $pdo->query("
        SELECT 
            u.id,
            u.name as ukm_name,
            u.slug,
            u.contact_info,
            us.name as leader_name,
            us.nim as leader_nim,
            us.email as leader_email
        FROM ukms u
        LEFT JOIN users us ON u.leader_id = us.id
        ORDER BY u.name
        LIMIT 5
    ");
    
    $count = 0;
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $count++;
        echo "🎯 {$count}. {$row['ukm_name']} ({$row['slug']})\n";
        
        // Parse contact info
        $contactInfo = json_decode($row['contact_info'], true) ?? [];
        
        echo "   📋 INFORMASI KONTAK UKM:\n";
        if (!empty($contactInfo['email'])) {
            echo "      📧 Email UKM: {$contactInfo['email']}\n";
        }
        if (!empty($contactInfo['phone'])) {
            echo "      📱 Telepon UKM: {$contactInfo['phone']}\n";
        }
        if (!empty($contactInfo['instagram'])) {
            echo "      📷 Instagram UKM: {$contactInfo['instagram']}\n";
        }
        if (!empty($contactInfo['website'])) {
            echo "      🌐 Website UKM: {$contactInfo['website']}\n";
        }
        
        echo "   👤 INFORMASI KETUA UKM:\n";
        if ($row['leader_name']) {
            echo "      👨‍💼 Ketua: {$row['leader_name']}\n";
            if ($row['leader_nim']) {
                echo "      🆔 NIM: {$row['leader_nim']}\n";
            }
            if ($row['leader_email']) {
                echo "      📧 Email Ketua: {$row['leader_email']}\n";
            }
        } else {
            echo "      ⚠️  Ketua belum ada, mungkin pendaftaran anggota akan tertunda\n";
        }
        
        echo "\n";
    }
    
    echo "📋 Expected Display in Views:\n\n";
    
    echo "🌐 PUBLIC UKM PAGE (ukms/show.blade.php):\n";
    echo "   ✅ Informasi Kontak section will show:\n";
    echo "      • Ketua UKM (name + NIM) OR fallback message\n";
    echo "      • Email UKM (from contact_info)\n";
    echo "      • Telepon UKM (from contact_info)\n";
    echo "      • Instagram UKM (from contact_info)\n";
    echo "      • Website UKM (from contact_info)\n\n";
    
    echo "🔧 ADMIN UKM DETAIL (admin/ukms/show.blade.php):\n";
    echo "   ✅ Kontak & Pertemuan section will show:\n";
    echo "      • Ketua UKM (name + NIM + email) OR fallback message\n";
    echo "      • Email UKM (from contact_info)\n";
    echo "      • Telepon UKM (from contact_info)\n";
    echo "      • Instagram UKM (from contact_info)\n";
    echo "      • Website UKM (from contact_info)\n\n";
    
    echo "📋 ADMIN UKM LIST (admin/ukms/index.blade.php):\n";
    echo "   ✅ Each UKM card will show:\n";
    echo "      • UKM logo (if available)\n";
    echo "      • UKM stats (members/capacity)\n";
    echo "      • Ketua UKM info box (name + NIM) OR 'Belum ada ketua'\n\n";
    
    echo "🌐 Test URLs:\n";
    echo "   Public UKM: http://localhost:8000/ukm/dpm\n";
    echo "   Admin UKM Detail: http://localhost:8000/admin/ukms/dpm\n";
    echo "   Admin UKM List: http://localhost:8000/admin/ukms\n\n";
    
    echo "📋 To Complete Setup:\n";
    echo "1. Run SQL script: add-ukm-contact-info.sql in phpMyAdmin\n";
    echo "2. Run SQL script: assign-ukm-leaders.sql in phpMyAdmin\n";
    echo "3. Test the URLs above\n\n";
    
    echo "✅ Both old contact info and new leader info will be displayed together!\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
