@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Components */
@layer components {
    /* Button Styles */
    .btn-primary {
        @apply inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-sm text-white hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200;
    }

    .btn-secondary {
        @apply inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-lg font-medium text-sm text-gray-800 hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200;
    }

    .btn-telkom {
        @apply inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-lg font-medium text-sm text-white hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200;
    }

    /* Form Styles */
    .form-input {
        @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    /* Card Styles */
    .card {
        @apply bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
    }

    .card-body {
        @apply px-6 py-4;
    }

    /* Badge Styles */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-success {
        @apply bg-green-100 text-green-800;
    }

    .badge-warning {
        @apply bg-yellow-100 text-yellow-800;
    }

    .badge-danger {
        @apply bg-red-100 text-red-800;
    }

    .badge-info {
        @apply bg-blue-100 text-blue-800;
    }

    .badge-secondary {
        @apply bg-gray-100 text-gray-800;
    }

    /* Utility Classes */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Auto-hide alerts */
    .alert-auto-hide {
        animation: fadeOut 0.5s ease-out 5s forwards;
    }

    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
}
