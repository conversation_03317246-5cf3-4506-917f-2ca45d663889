<?php

echo "=== CREATING SAMPLE EVENT FOR UKM SISTEM INFORMASI ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Find UKM Sistem Informasi
    $ukm = \App\Models\Ukm::where('name', 'like', '%sistem informasi%')->first();
    
    if (!$ukm) {
        echo "❌ UKM Sistem Informasi not found\n";
        exit;
    }
    
    echo "✅ Found UKM: {$ukm->name}\n";
    echo "   UKM ID: {$ukm->id}\n";
    echo "   UKM Logo: " . ($ukm->logo ?: 'None') . "\n";
    
    // Check if event already exists
    $existingEvent = \App\Models\Event::where('title', 'Workshop Web Development')->first();
    if ($existingEvent) {
        echo "✅ Event already exists: {$existingEvent->title}\n";
        echo "   Event ID: {$existingEvent->id}\n";
        echo "   Event Status: {$existingEvent->status}\n";
        exit;
    }
    
    // Create sample event
    $event = \App\Models\Event::create([
        'title' => 'Workshop Web Development',
        'slug' => 'workshop-web-development',
        'description' => 'Workshop intensif tentang pengembangan web modern menggunakan Laravel dan Vue.js. Peserta akan belajar dari dasar hingga membuat aplikasi web yang kompleks.',
        'start_datetime' => now()->addDays(7)->setTime(9, 0),
        'end_datetime' => now()->addDays(7)->setTime(17, 0),
        'location' => 'Lab Komputer 1, Gedung Tokong Nanas',
        'max_participants' => 30,
        'current_participants' => 0,
        'registration_start' => now(),
        'registration_end' => now()->addDays(5),
        'type' => 'workshop',
        'status' => 'published',
        'ukm_id' => $ukm->id,
        'created_by' => $ukm->leader_id ?: 1, // Use UKM leader or admin
    ]);
    
    echo "✅ Event created successfully!\n";
    echo "   Event ID: {$event->id}\n";
    echo "   Title: {$event->title}\n";
    echo "   Start Date: {$event->start_datetime}\n";
    echo "   Status: {$event->status}\n";
    echo "   UKM: {$event->ukm->name}\n";
    echo "   UKM Logo: " . ($event->ukm->logo ?: 'None') . "\n";
    
    echo "\n=== EVENT CREATION COMPLETE ===\n";
    echo "Now the UKM logo should appear in the upcoming events section on homepage!\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
