<?php
/**
 * Simple image loading test
 */

echo "=== SIMPLE IMAGE LOADING TEST ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing storage link...\n";
    
    $publicStoragePath = public_path('storage');
    echo "   📁 Public storage path: {$publicStoragePath}\n";
    echo "   📁 Storage link exists: " . (file_exists($publicStoragePath) ? 'YES ✅' : 'NO ❌') . "\n";
    
    echo "\n2. Testing UKM logos...\n";
    
    $ukms = \App\Models\Ukm::whereNotNull('logo')->get();
    
    foreach ($ukms as $ukm) {
        $logoPath = storage_path('app/public/' . $ukm->logo);
        $publicLogoPath = public_path('storage/' . $ukm->logo);
        
        echo "   UKM: {$ukm->name}\n";
        echo "      Logo file: {$ukm->logo}\n";
        echo "      Storage file exists: " . (file_exists($logoPath) ? 'YES ✅' : 'NO ❌') . "\n";
        echo "      Public accessible: " . (file_exists($publicLogoPath) ? 'YES ✅' : 'NO ❌') . "\n";
        echo "      URL: " . asset('storage/' . $ukm->logo) . "\n";
        echo "      ---\n";
    }
    
    echo "\n3. Testing event posters...\n";
    
    $events = \App\Models\Event::whereNotNull('poster')->get();
    
    foreach ($events as $event) {
        $posterPath = storage_path('app/public/' . $event->poster);
        $publicPosterPath = public_path('storage/' . $event->poster);
        
        echo "   Event: {$event->title}\n";
        echo "      Poster file: {$event->poster}\n";
        echo "      Storage file exists: " . (file_exists($posterPath) ? 'YES ✅' : 'NO ❌') . "\n";
        echo "      Public accessible: " . (file_exists($publicPosterPath) ? 'YES ✅' : 'NO ❌') . "\n";
        echo "      URL: " . asset('storage/' . $event->poster) . "\n";
        echo "      ---\n";
    }
    
    echo "\n4. Creating test HTML page...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>🖼️ Image Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%); 
            color: white; 
            text-align: center; 
            padding: 30px; 
            border-radius: 12px; 
            margin-bottom: 30px; 
        }
        .test-section { 
            background: white; 
            padding: 25px; 
            border-radius: 12px; 
            margin: 20px 0; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .image-item { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 15px 0; 
            background: #f8f9fa; 
        }
        .test-image { 
            max-width: 150px; 
            max-height: 100px; 
            border: 2px solid #ddd; 
            border-radius: 4px; 
            margin: 10px 0; 
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .test-link { 
            display: inline-block; 
            background: #007bff; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 6px; 
            margin: 5px; 
        }
        .test-link:hover { background: #0056b3; color: white; text-decoration: none; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🖼️ Image Loading Test</h1>
            <p>After storage link fix</p>
        </div>
        
        <div class='test-section'>
            <h2>🏛️ UKM Logos</h2>";
    
    foreach ($ukms as $ukm) {
        $logoExists = file_exists(public_path('storage/' . $ukm->logo));
        $testHtml .= "
            <div class='image-item'>
                <h4>{$ukm->name}</h4>
                <p><strong>File:</strong> {$ukm->logo}</p>
                <p><strong>Status:</strong> " . ($logoExists ? '<span class="success">✅ Available</span>' : '<span class="error">❌ Missing</span>') . "</p>";
        
        if ($logoExists) {
            $testHtml .= "<br><img src='" . asset('storage/' . $ukm->logo) . "' alt='{$ukm->name}' class='test-image'>";
        }
        
        $testHtml .= "</div>";
    }
    
    $testHtml .= "
        </div>
        
        <div class='test-section'>
            <h2>📅 Event Posters</h2>";
    
    foreach ($events as $event) {
        $posterExists = file_exists(public_path('storage/' . $event->poster));
        $testHtml .= "
            <div class='image-item'>
                <h4>{$event->title}</h4>
                <p><strong>File:</strong> {$event->poster}</p>
                <p><strong>Status:</strong> " . ($posterExists ? '<span class="success">✅ Available</span>' : '<span class="error">❌ Missing</span>') . "</p>";
        
        if ($posterExists) {
            $testHtml .= "<br><img src='" . asset('storage/' . $event->poster) . "' alt='{$event->title}' class='test-image'>";
        }
        
        $testHtml .= "</div>";
    }
    
    $testHtml .= "
        </div>
        
        <div class='test-section'>
            <h2>🔗 Test Links</h2>
            <a href='http://127.0.0.1:8000/admin/events' class='test-link' target='_blank'>Admin Events</a>
            <a href='http://127.0.0.1:8000/kegiatan' class='test-link' target='_blank'>Public Events</a>
            <a href='http://127.0.0.1:8000/ukms' class='test-link' target='_blank'>UKMs List</a>
        </div>
        
        <div class='test-section'>
            <h2>✅ Fix Applied</h2>
            <p><strong>Storage link recreated successfully!</strong></p>
            <p>Images should now load properly without 403 errors.</p>
        </div>
    </div>
</body>
</html>";
    
    file_put_contents(public_path('image-test-fixed.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/image-test-fixed.html\n";
    
    echo "\n=== IMAGE TEST COMPLETED ===\n";
    echo "🔗 Test page: http://127.0.0.1:8000/image-test-fixed.html\n";
    echo "📋 Storage link: " . (file_exists($publicStoragePath) ? 'Working ✅' : 'Missing ❌') . "\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
