@startuml UKM_Telkom_Jakarta_Class_Diagram

!theme plain
skinparam classAttributeIconSize 0
skinparam classFontSize 10
skinparam packageStyle rectangle

title UKM Telkom Jakarta - Class Diagram

package "Models" {
    
    class User {
        -id: int
        -nim: string
        -name: string
        -email: string
        -password: string
        -phone: string
        -gender: enum
        -faculty: string
        -major: string
        -batch: string
        -bio: text
        -avatar: string
        -status: enum
        -role: enum
        -last_login_at: datetime
        -email_verified_at: datetime
        --
        +ukms(): BelongsToMany
        +activeUkms(): BelongsToMany
        +ledUkms(): HasMany
        +notifications(): HasMany
        +eventRegistrations(): HasMany
        +attendances(): HasMany
        +certificates(): HasMany
        +isMemberOf(ukmId): boolean
        +isKetuaUkm(): boolean
        +isAdmin(): boolean
        +syncRoleWithSpatie(): void
        +unreadNotificationsCount(): int
    }

    class Ukm {
        -id: int
        -name: string
        -slug: string
        -description: text
        -vision: text
        -mission: text
        -category: enum
        -logo: string
        -banner: string
        -background_image: string
        -contact_info: json
        -meeting_schedule: string
        -meeting_location: string
        -max_members: int
        -current_members: int
        -status: enum
        -registration_status: enum
        -requirements: text
        -organization_structure: string
        -is_recruiting: boolean
        -established_date: date
        -leader_id: int
        --
        +leader(): BelongsTo
        +members(): BelongsToMany
        +events(): HasMany
        +achievements(): HasMany
        +getSlugOptions(): SlugOptions
        +getRouteKeyName(): string
        +activeMembers(): BelongsToMany
        +pendingMembers(): BelongsToMany
    }

    class Event {
        -id: int
        -ukm_id: int
        -title: string
        -slug: string
        -description: text
        -requirements: text
        -poster: string
        -gallery: json
        -type: enum
        -location: string
        -start_datetime: datetime
        -end_datetime: datetime
        -registration_start: datetime
        -registration_end: datetime
        -max_participants: int
        -current_participants: int
        -registration_fee: decimal
        -status: enum
        -requires_approval: boolean
        -registration_open: boolean
        -certificate_available: boolean
        -certificate_template: string
        -contact_person: string
        -notes: text
        -proposal_file: string
        -rab_file: string
        -lpj_file: string
        -approved_by: int
        -approved_at: datetime
        -approval_notes: text
        -rejected_by: int
        -rejected_at: datetime
        -rejection_reason: text
        -cancelled_by: int
        -cancelled_at: datetime
        -cancellation_reason: text
        --
        +ukm(): BelongsTo
        +registrations(): HasMany
        +attendances(): HasMany
        +certificates(): HasMany
        +approver(): BelongsTo
        +rejecter(): BelongsTo
        +canceller(): BelongsTo
        +updateStatusBasedOnDates(): void
        +isRegistrationOpen(): boolean
        +canRegister(): boolean
        +getStatusTextAttribute(): string
    }

    class EventRegistration {
        -id: int
        -user_id: int
        -event_id: int
        -status: enum
        -motivation: text
        -additional_data: json
        -payment_proof: string
        -payment_status: enum
        -approved_at: datetime
        -approved_by: int
        -rejection_reason: text
        -availability_form: json
        -registration_notes: text
        -cancelled_at: datetime
        -cancellation_reason: text
        --
        +user(): BelongsTo
        +event(): BelongsTo
        +approver(): BelongsTo
        +isApproved(): boolean
        +isPending(): boolean
        +isRejected(): boolean
    }

    class UkmMember {
        -id: int
        -user_id: int
        -ukm_id: int
        -role: enum
        -status: enum
        -joined_date: date
        -left_date: date
        -notes: text
        -previous_experience: text
        -skills_interests: text
        -reason_joining: text
        -preferred_division: string
        -cv_file: string
        -applied_at: datetime
        -approved_at: datetime
        -rejected_at: datetime
        -rejection_reason: text
        -approved_by: int
        -rejected_by: int
        --
        +user(): BelongsTo
        +ukm(): BelongsTo
        +approver(): BelongsTo
        +rejecter(): BelongsTo
    }

    class UkmAchievement {
        -id: int
        -ukm_id: int
        -title: string
        -description: text
        -type: enum
        -level: enum
        -organizer: string
        -achievement_date: date
        -year: int
        -certificate_file: string
        -participants: string
        -position: int
        -is_featured: boolean
        --
        +ukm(): BelongsTo
        +getPositionTextAttribute(): string
        +getTypeTextAttribute(): string
        +getLevelTextAttribute(): string
        +scopeFeatured(query): Builder
        +scopeByYear(query, year): Builder
        +scopeRecent(query, limit): Builder
    }

    class Notification {
        -id: int
        -user_id: int
        -type: string
        -title: string
        -message: text
        -data: json
        -read_at: datetime
        --
        +user(): BelongsTo
        +markAsRead(): void
        +isRead(): boolean
        +scopeUnread(query): Builder
        +scopeRead(query): Builder
    }

    class EventAttendance {
        -id: int
        -user_id: int
        -event_id: int
        -status: enum
        -check_in_time: datetime
        -check_out_time: datetime
        -notes: text
        --
        +user(): BelongsTo
        +event(): BelongsTo
        +isPresent(): boolean
    }

    class Certificate {
        -id: int
        -user_id: int
        -event_id: int
        -certificate_number: string
        -issued_at: datetime
        -file_path: string
        --
        +user(): BelongsTo
        +event(): BelongsTo
    }
}

package "Controllers" {

    class UserController {
        +index(): View
        +show(User): View
        +edit(User): View
        +update(Request, User): RedirectResponse
        +destroy(User): RedirectResponse
    }

    class UkmController {
        +index(): View
        +show(Ukm): View
        +join(Request, Ukm): RedirectResponse
        +leave(Ukm): RedirectResponse
    }

    class EventController {
        +index(): View
        +show(Event): View
        +register(Request, Event): RedirectResponse
        +cancelRegistration(Event): RedirectResponse
    }

    class KetuaUkmController {
        +dashboard(): View
        +manageUkm(id): View
        +editUkm(id): View
        +updateUkm(Request, id): RedirectResponse
        +events(): View
        +createEvent(ukmId): View
        +storeEvent(Request, ukmId): RedirectResponse
        +editEvent(Event): View
        +updateEvent(Request, Event): RedirectResponse
        +manageMembers(id): View
        +approveMember(Request): RedirectResponse
        +rejectMember(Request): RedirectResponse
        +removeMember(Request): RedirectResponse
    }

    abstract class AdminController {
        +dashboard(): View
    }

    class AdminUserManagementController {
        +index(Request): View
        +create(): View
        +store(Request): RedirectResponse
        +show(User): View
        +edit(User): View
        +update(Request, User): RedirectResponse
        +destroy(User): RedirectResponse
        +activate(User): RedirectResponse
        +suspend(User): RedirectResponse
    }

    class AdminUkmManagementController {
        +index(Request): View
        +create(): View
        +store(Request): RedirectResponse
        +show(Ukm): View
        +edit(Ukm): View
        +update(Request, Ukm): RedirectResponse
        +destroy(Ukm): RedirectResponse
        +removeLeader(Ukm): RedirectResponse
    }

    class AdminEventManagementController {
        +index(Request): View
        +show(Event): View
        +edit(Event): View
        +update(Request, Event): RedirectResponse
        +destroy(Event): RedirectResponse
        +publish(Event): RedirectResponse
        +cancel(Event): RedirectResponse
    }

    class AdminKetuaUkmManagementController {
        +index(Request): View
        +create(): View
        +store(Request): RedirectResponse
        +show(User): View
        +edit(User): View
        +update(Request, User): RedirectResponse
        +destroy(User): RedirectResponse
        +assignUkm(Request, User): RedirectResponse
        +removeUkm(Request, User): RedirectResponse
        +suspend(User): RedirectResponse
        +activate(User): RedirectResponse
    }
}

package "Services" {

    class NotificationService {
        +{static} createUkmApplicationApproved(User, ukmName): Notification
        +{static} createUkmApplicationRejected(User, ukmName): Notification
        +{static} createUkmMemberRemoved(User, ukmName, reason): Notification
        +{static} createEventRegistrationApproved(User, eventTitle): Notification
        +{static} createEventRegistrationRejected(User, eventTitle, reason): Notification
    }

    class SeoHelper {
        +{static} cleanText(text, length): string
        +{static} generateKeywords(text, additionalKeywords): string
        +{static} generateDescription(text, length): string
    }
}

package "Middleware" {

    class Authenticate {
        +handle(Request, Closure): Response
    }

    class CheckRole {
        +handle(Request, Closure, role): Response
    }

    class VerifyCsrfToken {
        +handle(Request, Closure): Response
    }
}

' === RELATIONSHIPS ===

' User Relationships
User ||--o{ UkmMember : "has many memberships"
User ||--o{ Ukm : "leads (leader_id)"
User ||--o{ EventRegistration : "registers for events"
User ||--o{ EventAttendance : "attends events"
User ||--o{ Certificate : "earns certificates"
User ||--o{ Notification : "receives notifications"

' UKM Relationships
Ukm ||--o{ UkmMember : "has members"
Ukm ||--o{ Event : "organizes events"
Ukm ||--o{ UkmAchievement : "has achievements"
Ukm }o--|| User : "led by (leader_id)"

' Event Relationships
Event }o--|| Ukm : "belongs to UKM"
Event ||--o{ EventRegistration : "has registrations"
Event ||--o{ EventAttendance : "has attendances"
Event ||--o{ Certificate : "issues certificates"

' Pivot/Junction Relationships
UkmMember }o--|| User : "user_id"
UkmMember }o--|| Ukm : "ukm_id"

' Registration Relationships
EventRegistration }o--|| User : "user_id"
EventRegistration }o--|| Event : "event_id"

' Attendance Relationships
EventAttendance }o--|| User : "user_id"
EventAttendance }o--|| Event : "event_id"

' Certificate Relationships
Certificate }o--|| User : "user_id"
Certificate }o--|| Event : "event_id"

' Achievement Relationships
UkmAchievement }o--|| Ukm : "ukm_id"

' Notification Relationships
Notification }o--|| User : "user_id"

' Controller Dependencies
UserController ..> User : "manages"
UkmController ..> Ukm : "manages"
UkmController ..> UkmMember : "manages memberships"
EventController ..> Event : "manages"
EventController ..> EventRegistration : "manages registrations"

KetuaUkmController ..> Ukm : "manages"
KetuaUkmController ..> Event : "creates/manages"
KetuaUkmController ..> UkmMember : "manages members"
KetuaUkmController ..> EventRegistration : "approves/rejects"

AdminController <|-- AdminUserManagementController
AdminController <|-- AdminUkmManagementController
AdminController <|-- AdminEventManagementController
AdminController <|-- AdminKetuaUkmManagementController

AdminUserManagementController ..> User : "manages"
AdminUkmManagementController ..> Ukm : "manages"
AdminEventManagementController ..> Event : "manages"
AdminKetuaUkmManagementController ..> User : "manages ketua UKM"

' Service Dependencies
NotificationService ..> Notification : "creates"
NotificationService ..> User : "notifies"

@enduml
