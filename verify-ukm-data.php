<?php
echo "=== VERIFYING UKM DATA ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check total UKMs
    $result = $pdo->query("SELECT COUNT(*) as total FROM ukms");
    $total = $result->fetch(PDO::FETCH_ASSOC)['total'];
    echo "📊 Total UKMs: {$total}\n\n";
    
    if ($total > 0) {
        // Show all UKMs
        echo "📋 UKM List:\n";
        $result = $pdo->query("SELECT id, name, slug, category, current_members, max_members, status FROM ukms ORDER BY name");
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            $status = $row['status'] == 'active' ? '✅' : '❌';
            echo "   {$status} {$row['name']} ({$row['category']}) - {$row['current_members']}/{$row['max_members']} members\n";
        }
        
        echo "\n📊 Categories:\n";
        $result = $pdo->query("SELECT category, COUNT(*) as count FROM ukms GROUP BY category ORDER BY count DESC");
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "   • {$row['category']}: {$row['count']} UKMs\n";
        }
        
        echo "\n🎯 Recruitment Status:\n";
        $result = $pdo->query("SELECT recruitment_open, COUNT(*) as count FROM ukms GROUP BY recruitment_open");
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            $status = $row['recruitment_open'] ? 'Open' : 'Closed';
            echo "   • {$status}: {$row['count']} UKMs\n";
        }
        
        echo "\n📅 Establishment Years:\n";
        $result = $pdo->query("SELECT established_year, COUNT(*) as count FROM ukms GROUP BY established_year ORDER BY established_year");
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "   • {$row['established_year']}: {$row['count']} UKMs\n";
        }
        
        echo "\n🌐 Test URLs:\n";
        echo "   UKM Index: http://localhost:8000/ukm\n";
        echo "   Admin UKMs: http://localhost:8000/admin/ukms\n";
        
        echo "\n📱 Sample UKM URLs:\n";
        $result = $pdo->query("SELECT name, slug FROM ukms LIMIT 5");
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "   • {$row['name']}: http://localhost:8000/ukm/{$row['slug']}\n";
        }
        
        echo "\n✅ UKM data verification complete!\n";
        
    } else {
        echo "❌ No UKM data found!\n";
        echo "\n📋 To insert data:\n";
        echo "1. Open phpMyAdmin: http://localhost/phpmyadmin\n";
        echo "2. Select database 'ukmwebv'\n";
        echo "3. Go to SQL tab\n";
        echo "4. Copy content from 'ukm-insert-phpmyadmin.sql'\n";
        echo "5. Paste and execute\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== VERIFICATION COMPLETE ===\n";
?>
