<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Event;
use App\Models\EventRegistration;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

echo "=== TESTING APPROVAL FUNCTIONALITY AFTER FIX ===\n";

echo "1. Finding test data...\n";

$event = Event::where('slug', 'bukber')->first();
$registration = EventRegistration::where('event_id', $event->id)
    ->where('status', 'pending')
    ->first();

if (!$registration) {
    echo "   ❌ No pending registration found\n";
    exit;
}

echo "   ✅ Event: {$event->title}\n";
echo "   ✅ Registration: {$registration->user->name} (ID: {$registration->id})\n";
echo "   Current status: {$registration->status}\n";

echo "2. Testing approval logic without notification...\n";

// Simulate the approval process
$originalStatus = $registration->status;
$originalParticipantCount = $event->current_participants;

echo "   Before approval:\n";
echo "   - Registration status: {$originalStatus}\n";
echo "   - Participant count: {$originalParticipantCount}\n";

try {
    // Simulate approval (without calling the controller)
    $registration->update([
        'status' => 'approved',
        'approved_at' => now(),
        'approved_by' => 1, // Assuming ketua UKM user ID
    ]);
    
    // Update participant count
    $event->updateParticipantCount();
    $event->refresh();
    
    echo "   ✅ Approval simulation successful\n";
    echo "   After approval:\n";
    echo "   - Registration status: {$registration->status}\n";
    echo "   - Participant count: {$event->current_participants}\n";
    
    // Revert for testing
    $registration->update([
        'status' => $originalStatus,
        'approved_at' => null,
        'approved_by' => null,
    ]);
    $event->updateParticipantCount();
    
    echo "   ✅ Reverted to original state for actual testing\n";
    
} catch (\Exception $e) {
    echo "   ❌ Error during approval simulation: " . $e->getMessage() . "\n";
}

echo "3. Testing controller methods exist...\n";

$controller = new \App\Http\Controllers\KetuaUkmController();

$methods = [
    'approveRegistration' => 'Individual approval',
    'rejectRegistration' => 'Individual rejection',
    'bulkApproveRegistrations' => 'Bulk approval'
];

foreach ($methods as $method => $description) {
    if (method_exists($controller, $method)) {
        echo "   ✅ {$description}: Method exists\n";
    } else {
        echo "   ❌ {$description}: Method missing\n";
    }
}

echo "4. Testing routes exist...\n";

$routes = [
    'ketua-ukm.events.registrations.approve' => 'Approve route',
    'ketua-ukm.events.registrations.reject' => 'Reject route',
    'ketua-ukm.events.registrations.bulk-approve' => 'Bulk approve route'
];

foreach ($routes as $routeName => $description) {
    if (\Illuminate\Support\Facades\Route::has($routeName)) {
        echo "   ✅ {$description}: Route exists\n";
        
        try {
            if ($routeName === 'ketua-ukm.events.registrations.bulk-approve') {
                $url = route($routeName, $event);
            } else {
                $url = route($routeName, [$event, $registration]);
            }
            echo "   URL: {$url}\n";
        } catch (\Exception $e) {
            echo "   ⚠️  URL generation error: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ {$description}: Route missing\n";
    }
}

echo "5. Testing notification service removal...\n";

$controllerPath = app_path('Http/Controllers/KetuaUkmController.php');
$controllerContent = file_get_contents($controllerPath);

$notificationChecks = [
    'sendEventRegistrationApproved' => 'Should be commented out',
    'sendEventRegistrationRejected' => 'Should be commented out',
    'TODO: Send notification' => 'Should have TODO comments'
];

echo "   Notification service checks:\n";
foreach ($notificationChecks as $check => $description) {
    $found = strpos($controllerContent, $check) !== false;
    if ($check === 'TODO: Send notification') {
        echo "   " . ($found ? '✅' : '❌') . " {$description}: " . ($found ? 'Found' : 'Missing') . "\n";
    } else {
        // These should NOT be found (they should be commented out)
        $uncommentedFound = preg_match('/^\s*[^\/\*].*' . preg_quote($check, '/') . '/m', $controllerContent);
        echo "   " . ($uncommentedFound ? '❌' : '✅') . " {$description}: " . ($uncommentedFound ? 'Still active (ERROR)' : 'Properly commented out') . "\n";
    }
}

echo "6. Manual testing instructions...\n";

echo "   📋 TO TEST APPROVAL MANUALLY:\n";
echo "   1. Login as ketua UKM\n";
echo "   2. Go to: http://localhost:8000/ketua-ukm/events/{$event->slug}/registrations\n";
echo "   3. Find pending registration (ID: {$registration->id})\n";
echo "   4. Click 'Setujui' button\n";
echo "   5. Should redirect back with success message\n";
echo "   6. Registration status should change to 'approved'\n";
echo "   7. Participant count should increase\n";

echo "7. Expected behavior after fix...\n";

echo "   ✅ WHAT SHOULD HAPPEN:\n";
echo "   - No more 'undefined method' errors\n";
echo "   - Approval process completes successfully\n";
echo "   - Registration status updates to 'approved'\n";
echo "   - Participant count increases\n";
echo "   - Success message appears\n";
echo "   - No notification errors in logs\n";

echo "8. Testing URLs...\n";

echo "   🌐 TEST THESE URLS:\n";
echo "   - Registrations: http://localhost:8000/ketua-ukm/events/{$event->slug}/registrations\n";
echo "   - Registration detail: http://localhost:8000/ketua-ukm/events/{$event->slug}/registrations/{$registration->id}\n";
echo "   - Event detail: http://localhost:8000/ketua-ukm/events/{$event->slug}\n";

echo "\n=== APPROVAL FIX TEST COMPLETED ===\n";
echo "✅ Notification service calls have been disabled!\n";
echo "✅ Approval functionality should now work without errors!\n";

echo "\nSUMMARY OF FIXES:\n";
echo "🔧 Commented out sendEventRegistrationApproved() calls\n";
echo "🔧 Commented out sendEventRegistrationRejected() calls\n";
echo "🔧 Added TODO comments for future implementation\n";
echo "🔧 Preserved all approval logic functionality\n";
echo "🔧 Removed dependency on unimplemented notification methods\n";

echo "\nNOTE:\n";
echo "📝 Notification features can be implemented later by:\n";
echo "   1. Creating the missing methods in NotificationService\n";
echo "   2. Uncommenting the notification code in controller\n";
echo "   3. Testing the notification functionality\n";

?>
