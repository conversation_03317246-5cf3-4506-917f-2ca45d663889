<?php

echo "=== TESTING UKM AMBIGUOUS COLUMN FIX ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing User model isMemberOf method...\n";
    
    $user = \App\Models\User::where('role', 'student')->first();
    $ukm = \App\Models\Ukm::first();
    
    if ($user && $ukm) {
        echo "   Testing with user: {$user->name}\n";
        echo "   Testing with UKM: {$ukm->name}\n";
        
        try {
            $isMember = $user->isMemberOf($ukm->id);
            echo "   isMemberOf result: " . ($isMember ? 'true' : 'false') . "\n";
            echo "   ✅ User isMemberOf method works without ambiguous column error\n";
        } catch (Exception $e) {
            echo "   ❌ User isMemberOf error: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ⚠️  No user or UKM found for testing\n";
    }
    
    echo "2. Testing UKM activeMembers relationship...\n";
    
    if ($ukm) {
        try {
            $activeMembers = $ukm->activeMembers()->count();
            echo "   Active members count: {$activeMembers}\n";
            echo "   ✅ UKM activeMembers relationship works\n";
        } catch (Exception $e) {
            echo "   ❌ UKM activeMembers error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "3. Testing User ukms relationship with wherePivot...\n";
    
    if ($user) {
        try {
            $userUkms = $user->ukms()->wherePivot('status', 'active')->count();
            echo "   User active UKMs count: {$userUkms}\n";
            echo "   ✅ User ukms relationship with wherePivot works\n";
        } catch (Exception $e) {
            echo "   ❌ User ukms wherePivot error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "4. Testing UKM membership check query...\n";
    
    if ($user && $ukm) {
        try {
            // Test the exact query that was causing the error
            $membership = $ukm->members()
                ->where('ukm_members.user_id', $user->id)
                ->wherePivot('status', 'active')
                ->first();
            
            echo "   Membership check: " . ($membership ? 'MEMBER' : 'NOT MEMBER') . "\n";
            echo "   ✅ UKM membership check query works\n";
        } catch (Exception $e) {
            echo "   ❌ UKM membership check error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "5. Testing dashboard stats query...\n";
    
    if ($user) {
        try {
            $ukmCount = $user->ukms()->wherePivot('status', 'active')->count();
            echo "   Dashboard UKM count: {$ukmCount}\n";
            echo "   ✅ Dashboard stats query works\n";
        } catch (Exception $e) {
            echo "   ❌ Dashboard stats error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "6. Testing UKM join/leave functionality...\n";
    
    if ($user && $ukm) {
        try {
            // Test existing membership check (the problematic query)
            $existingMembership = $ukm->members()
                ->where('ukm_members.user_id', $user->id)
                ->wherePivot('status', 'active')
                ->first();
            
            echo "   Existing membership check: " . ($existingMembership ? 'EXISTS' : 'NOT EXISTS') . "\n";
            echo "   ✅ UKM join/leave functionality works\n";
        } catch (Exception $e) {
            echo "   ❌ UKM join/leave error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "7. Testing view-level queries...\n";
    
    if ($user && $ukm) {
        try {
            // Simulate the query used in views
            $isMemberView = $user->ukms()->where('ukm_id', $ukm->id)->wherePivot('status', 'active')->exists();
            echo "   View-level membership check: " . ($isMemberView ? 'MEMBER' : 'NOT MEMBER') . "\n";
            echo "   ✅ View-level queries work\n";
        } catch (Exception $e) {
            echo "   ❌ View-level query error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "8. Testing UKM controller queries...\n";
    
    if ($user && $ukm) {
        try {
            // Test UKM controller membership check
            $membership = $ukm->members()
                ->where('ukm_members.user_id', $user->id)
                ->first();
            
            if ($membership) {
                echo "   UKM controller membership: EXISTS\n";
                echo "   Membership status: {$membership->pivot->status}\n";
            } else {
                echo "   UKM controller membership: NOT EXISTS\n";
            }
            echo "   ✅ UKM controller queries work\n";
        } catch (Exception $e) {
            echo "   ❌ UKM controller error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== TESTING COMPLETED ===\n";
    echo "✅ Ambiguous column 'status' error should be fixed!\n";
    echo "\nChanges made:\n";
    echo "1. ✅ User::isMemberOf() - Changed to use wherePivot('status', 'active')\n";
    echo "2. ✅ Views (ukms/show.blade.php, ukms/index.blade.php) - Fixed wherePivot\n";
    echo "3. ✅ DashboardController - Fixed wherePivot for stats\n";
    echo "4. ✅ UkmController::leave() - Fixed wherePivot for membership check\n";
    echo "5. ✅ All pivot table queries now use wherePivot() instead of where()\n";
    echo "\nMahasiswa sekarang bisa menjelajahi UKM tanpa error!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
