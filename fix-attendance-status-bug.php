<?php

echo "=== FIXING ATTENDANCE STATUS BUG ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking current attendance records...\n";
    
    // Find attendance records with incorrect status
    $incorrectAttendances = \App\Models\EventAttendance::whereNull('status')
                                                      ->orWhere('status', '')
                                                      ->orWhereNull('verification_status')
                                                      ->orWhere('verification_status', '')
                                                      ->get();
    
    echo "   Found {$incorrectAttendances->count()} attendance records with incorrect status\n";
    
    if ($incorrectAttendances->count() > 0) {
        echo "2. Fixing incorrect attendance records...\n";
        
        foreach ($incorrectAttendances as $attendance) {
            $oldStatus = $attendance->status;
            $oldVerificationStatus = $attendance->verification_status;
            
            // Fix status based on submitted_at
            if ($attendance->submitted_at) {
                // Already submitted, should be 'present'
                $attendance->status = 'present';
                if (!$attendance->verification_status) {
                    $attendance->verification_status = 'pending';
                }
            } else {
                // Not submitted yet, should be 'pending'
                $attendance->status = 'pending';
                $attendance->verification_status = 'pending';
            }
            
            $attendance->save();
            
            echo "   ✅ Fixed attendance ID {$attendance->id}: status '{$oldStatus}' → '{$attendance->status}', verification '{$oldVerificationStatus}' → '{$attendance->verification_status}'\n";
        }
    }
    
    echo "3. Testing attendance button visibility...\n";
    
    // Find a completed event
    $event = \App\Models\Event::where('status', 'completed')->first();
    if (!$event) {
        echo "   ❌ No completed events found\n";
        exit;
    }
    
    echo "   Testing with event: {$event->title}\n";
    echo "   Event status: {$event->status}\n";
    echo "   Can submit attendance: " . ($event->canSubmitAttendance() ? 'Yes' : 'No') . "\n";
    
    // Find a student with registration
    $registration = \App\Models\EventRegistration::where('event_id', $event->id)
                                                ->where('status', 'approved')
                                                ->first();
    
    if (!$registration) {
        echo "   ❌ No approved registrations found\n";
        exit;
    }
    
    $student = $registration->user;
    echo "   Testing with student: {$student->name}\n";
    
    // Check attendance record
    $attendance = \App\Models\EventAttendance::where('event_id', $event->id)
                                           ->where('user_id', $student->id)
                                           ->first();
    
    if ($attendance) {
        echo "   Attendance record found:\n";
        echo "     Status: {$attendance->status}\n";
        echo "     Verification Status: {$attendance->verification_status}\n";
        echo "     Submitted At: " . ($attendance->submitted_at ? $attendance->submitted_at : 'Not submitted') . "\n";
        echo "     Proof File: " . ($attendance->proof_file ? 'Yes' : 'No') . "\n";
        
        // Test what button should show
        if (!$attendance || $attendance->status === 'pending') {
            echo "   ✅ Should show: 'Isi Absensi' button\n";
        } elseif ($attendance->status === 'present') {
            if ($attendance->verification_status === 'pending') {
                echo "   ✅ Should show: 'Menunggu Verifikasi Absensi'\n";
            } elseif ($attendance->verification_status === 'verified') {
                echo "   ✅ Should show: 'Absensi Terverifikasi' + Certificate Download\n";
            } elseif ($attendance->verification_status === 'rejected') {
                echo "   ✅ Should show: 'Absensi Ditolak'\n";
            }
        }
    } else {
        echo "   No attendance record found\n";
        echo "   ✅ Should show: 'Isi Absensi' button\n";
    }
    
    echo "4. Testing ketua UKM detail view route...\n";
    
    if ($attendance) {
        try {
            $route = route('ketua-ukm.events.attendances.verify', [$event->slug, $attendance->id]);
            echo "   ✅ Route generated successfully: {$route}\n";
        } catch (Exception $e) {
            echo "   ❌ Route error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "5. Testing attendance creation with correct defaults...\n";
    
    // Create a test student
    $testStudent = \App\Models\User::where('role', 'student')->skip(1)->first();
    if (!$testStudent) {
        echo "   ❌ No test student found\n";
    } else {
        // Create test registration
        $testRegistration = \App\Models\EventRegistration::firstOrCreate([
            'event_id' => $event->id,
            'user_id' => $testStudent->id,
        ], [
            'status' => 'approved',
            'motivation' => 'Test registration for attendance fix',
            'registered_at' => now(),
        ]);
        
        // Create attendance with correct defaults
        $testAttendance = \App\Models\EventAttendance::firstOrCreate([
            'event_id' => $event->id,
            'user_id' => $testStudent->id,
            'event_registration_id' => $testRegistration->id,
        ], [
            'status' => 'pending',
            'verification_status' => 'pending',
        ]);
        
        echo "   ✅ Test attendance created:\n";
        echo "     Status: {$testAttendance->status}\n";
        echo "     Verification Status: {$testAttendance->verification_status}\n";
        echo "     Should show 'Isi Absensi' button: " . ($testAttendance->status === 'pending' ? 'Yes' : 'No') . "\n";
        
        // Clean up test data
        $testAttendance->delete();
        $testRegistration->delete();
        echo "   ✅ Test data cleaned up\n";
    }
    
    echo "6. Verifying all attendance records have correct status...\n";
    
    $allAttendances = \App\Models\EventAttendance::all();
    $pendingCount = $allAttendances->where('status', 'pending')->count();
    $presentCount = $allAttendances->where('status', 'present')->count();
    $verificationPendingCount = $allAttendances->where('verification_status', 'pending')->count();
    $verifiedCount = $allAttendances->where('verification_status', 'verified')->count();
    $rejectedCount = $allAttendances->where('verification_status', 'rejected')->count();
    
    echo "   Total attendance records: {$allAttendances->count()}\n";
    echo "   Status breakdown:\n";
    echo "     - Pending: {$pendingCount}\n";
    echo "     - Present: {$presentCount}\n";
    echo "   Verification breakdown:\n";
    echo "     - Pending: {$verificationPendingCount}\n";
    echo "     - Verified: {$verifiedCount}\n";
    echo "     - Rejected: {$rejectedCount}\n";
    
    // Check for any records with null status
    $nullStatusCount = $allAttendances->whereNull('status')->count();
    $nullVerificationCount = $allAttendances->whereNull('verification_status')->count();
    
    if ($nullStatusCount > 0 || $nullVerificationCount > 0) {
        echo "   ❌ Found {$nullStatusCount} records with null status\n";
        echo "   ❌ Found {$nullVerificationCount} records with null verification status\n";
    } else {
        echo "   ✅ All records have proper status values\n";
    }
    
    echo "\n=== ATTENDANCE STATUS BUG FIX COMPLETED ===\n";
    echo "✅ Attendance status bug fixed!\n";
    echo "\nKey Fixes Applied:\n";
    echo "🔧 ISSUE: firstOrCreate not setting default status values\n";
    echo "🔧 SOLUTION: Added default values for status and verification_status\n";
    echo "🔧 ISSUE: Route parameter error in JavaScript\n";
    echo "🔧 SOLUTION: Fixed route generation in viewDetails function\n";
    echo "\nExpected Behavior:\n";
    echo "✅ New attendance records created with status='pending', verification_status='pending'\n";
    echo "✅ Student sees 'Isi Absensi' button when status='pending'\n";
    echo "✅ After submission: status='present', verification_status='pending'\n";
    echo "✅ Student sees 'Menunggu Verifikasi Absensi'\n";
    echo "✅ After verification: verification_status='verified'\n";
    echo "✅ Student sees 'Absensi Terverifikasi' + certificate download\n";
    echo "✅ Ketua UKM can click detail button without route errors\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
