<?php
/**
 * Test admin UKM members functionality
 */

echo "=== TESTING ADMIN UKM MEMBERS ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing UKM members relationship...\n";
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    if (!$ukm) {
        echo "   ❌ IMMA UKM not found\n";
        exit;
    }
    
    echo "   ✅ Found IMMA UKM (ID: {$ukm->id})\n";
    
    // Test members relationship
    $members = $ukm->members()->get();
    echo "   📊 Total members: " . $members->count() . "\n";
    
    // Test with user data (members are users with pivot data)
    $membersWithPivot = $ukm->members()->withPivot(['status', 'role', 'joined_date', 'applied_at'])->get();
    echo "   📊 Members with pivot data: " . $membersWithPivot->count() . "\n";

    foreach ($membersWithPivot as $member) {
        echo "   👤 Member: {$member->name} ({$member->nim})\n";
        echo "      Status: {$member->pivot->status}\n";
        echo "      Role: " . ($member->pivot->role ?? 'member') . "\n";
        echo "      Joined: " . ($member->pivot->joined_date ?? 'N/A') . "\n";
        echo "      ---\n";
    }
    
    echo "\n2. Testing admin controller method...\n";
    
    $controller = new \App\Http\Controllers\Admin\UkmManagementController();
    $request = \Illuminate\Http\Request::create("/admin/ukms/{$ukm->id}/members", 'GET');
    
    try {
        $response = $controller->members($request, $ukm);
        echo "   ✅ Admin members controller executed successfully\n";
        
        if ($response instanceof \Illuminate\View\View) {
            echo "   ✅ Response is a view\n";
            echo "   View name: " . $response->getName() . "\n";
            
            $viewData = $response->getData();
            echo "   View data keys: " . implode(', ', array_keys($viewData)) . "\n";
            
            if (isset($viewData['members'])) {
                echo "   ✅ Members data passed to view: " . $viewData['members']->count() . " items\n";
                echo "   📊 Total members: " . $viewData['totalMembers'] . "\n";
                echo "   📊 Active members: " . $viewData['activeMembers'] . "\n";
                echo "   📊 Pending members: " . $viewData['pendingMembers'] . "\n";
                echo "   📊 Rejected members: " . $viewData['rejectedMembers'] . "\n";
            } else {
                echo "   ❌ Members data not found in view\n";
            }
            
            // Test rendering
            echo "   Testing view rendering...\n";
            $content = $response->render();
            echo "   ✅ View rendered successfully\n";
            echo "   Content length: " . strlen($content) . " characters\n";
            
        }
        
    } catch (\Exception $e) {
        echo "   ❌ Controller error: " . $e->getMessage() . "\n";
        echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    }
    
    echo "\n3. Testing UKM show page members section...\n";
    
    // Test UKM show page
    $showController = new \App\Http\Controllers\Admin\UkmManagementController();
    $showRequest = \Illuminate\Http\Request::create("/admin/ukms/{$ukm->id}", 'GET');
    
    try {
        $showResponse = $showController->show($ukm);
        echo "   ✅ Admin UKM show controller executed successfully\n";
        
        if ($showResponse instanceof \Illuminate\View\View) {
            $showViewData = $showResponse->getData();
            echo "   Show view data keys: " . implode(', ', array_keys($showViewData)) . "\n";
            
            if (isset($showViewData['ukm'])) {
                $ukmData = $showViewData['ukm'];
                echo "   ✅ UKM data found\n";
                
                // Check if members are loaded
                if ($ukmData->relationLoaded('members')) {
                    echo "   ✅ Members relationship loaded: " . $ukmData->members->count() . " members\n";
                } else {
                    echo "   ❌ Members relationship not loaded\n";
                }
            }
        }
        
    } catch (\Exception $e) {
        echo "   ❌ Show controller error: " . $e->getMessage() . "\n";
        echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    }
    
    echo "\n4. Creating test data if needed...\n";
    
    // Check if we have any members
    if ($members->count() === 0) {
        echo "   No members found, creating test member...\n";
        
        // Find a student user
        $student = \App\Models\User::where('role', 'student')->first();
        if ($student) {
            // Add as member
            $ukm->members()->attach($student->id, [
                'role' => 'member',
                'status' => 'active',
                'joined_date' => now(),
                'applied_at' => now(),
            ]);
            
            echo "   ✅ Added test member: {$student->name}\n";
            
            // Update member count
            $ukm->increment('current_members');
        }
    }
    
    echo "\n5. Creating test HTML page...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Admin Members Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            background: #f9f9f9; 
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .member-card { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 10px 0; 
            background: white; 
        }
        .badge { 
            display: inline-block; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: bold; 
            margin-right: 5px; 
        }
        .badge-active { background: #d4edda; color: #155724; }
        .badge-pending { background: #fff3cd; color: #856404; }
        .badge-rejected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🔧 Admin UKM Members Test</h1>
    
    <div class='test-section'>
        <h2>📊 Members Statistics</h2>
        <p><strong>UKM:</strong> {$ukm->name}</p>
        <p><strong>Total Members:</strong> " . $members->count() . "</p>";
    
    $activeCount = $members->where('pivot.status', 'active')->count();
    $pendingCount = $members->where('pivot.status', 'pending')->count();
    $rejectedCount = $members->where('pivot.status', 'rejected')->count();
    
    $testHtml .= "
        <p><strong>Active:</strong> $activeCount</p>
        <p><strong>Pending:</strong> $pendingCount</p>
        <p><strong>Rejected:</strong> $rejectedCount</p>
    </div>
    
    <div class='test-section'>
        <h2>👥 Members List</h2>";
    
    if ($members->count() > 0) {
        foreach ($members as $member) {
            if ($member->user) {
                $statusClass = $member->pivot->status === 'active' ? 'badge-active' : 
                              ($member->pivot->status === 'pending' ? 'badge-pending' : 'badge-rejected');
                
                $testHtml .= "
        <div class='member-card'>
            <h4>{$member->user->name}</h4>
            <p><strong>NIM:</strong> " . ($member->user->nim ?? 'N/A') . "</p>
            <p><strong>Email:</strong> {$member->user->email}</p>
            <p><strong>Major:</strong> " . ($member->user->major ?? 'N/A') . "</p>
            <p><strong>Status:</strong> <span class='badge $statusClass'>" . ucfirst($member->pivot->status) . "</span></p>
            <p><strong>Role:</strong> " . ucfirst($member->pivot->role ?? 'member') . "</p>
            <p><strong>Joined:</strong> " . ($member->pivot->joined_date ? date('d M Y', strtotime($member->pivot->joined_date)) : 'N/A') . "</p>
        </div>";
            }
        }
    } else {
        $testHtml .= "<p>No members found for this UKM.</p>";
    }
    
    $testHtml .= "
    </div>
    
    <div class='test-section'>
        <h2>🔗 Test Links</h2>
        <ul>
            <li><a href='http://127.0.0.1:8000/admin/ukms/{$ukm->id}/members' target='_blank'><strong>Admin Members Page</strong> - Should show all members</a></li>
            <li><a href='http://127.0.0.1:8000/admin/ukms/{$ukm->id}' target='_blank'><strong>Admin UKM Detail</strong> - Should show members section</a></li>
            <li><a href='http://127.0.0.1:8000/admin/ukms' target='_blank'><strong>Admin UKM Index</strong> - All UKMs</a></li>
        </ul>
    </div>
    
    <div class='test-section'>
        <h2>📋 Debug Info</h2>
        <p>If members are not showing, check:</p>
        <ul>
            <li>UKM members relationship in model</li>
            <li>Pivot table data (ukm_members)</li>
            <li>User relationship loading</li>
            <li>View template rendering</li>
        </ul>
    </div>
</body>
</html>";
    
    file_put_contents(public_path('admin-members-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/admin-members-test.html\n";
    
    echo "\n=== ADMIN MEMBERS TEST COMPLETED ===\n";
    echo "🔗 Test page: http://127.0.0.1:8000/admin-members-test.html\n";
    echo "🔗 Admin members: http://127.0.0.1:8000/admin/ukms/{$ukm->id}/members\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
