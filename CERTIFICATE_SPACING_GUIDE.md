# 🎨 Certificate Spacing & Positioning Guide

## 📍 **File Location**
**File:** `app/Services/CertificateService.php`  
**Method:** `getCertificatePositioning($event)`

## 🎯 **How to Adjust Certificate Text Spacing**

### **1. Template-Specific Positioning**

The system automatically detects your template and applies custom positioning:

```php
// Current template detection (line ~430 in CertificateService.php)
if (strpos($templateName, 'rQUuuutqBwYrgYUbgI0xUVAGDj8hjb05rgZ9WYZc') !== false) {
    // Your custom positioning here
}
```

### **2. Positioning Properties You Can Adjust**

#### **Container Positioning:**
```php
'container' => 'justify-content: flex-start; align-items: center; padding-top: 80px; padding-left: 50px; padding-right: 50px;'
```
- `padding-top: 80px` → Move all content down/up
- `padding-left: 50px` → Move content left/right
- `justify-content: flex-start` → Align to top (center/flex-end for middle/bottom)

#### **Individual Element Spacing:**
```php
'name' => 'margin: 10px 0; font-size: 32px; font-weight: bold; color: #000;'
'event_title' => 'margin: 5px 0 10px 0; font-size: 24px; font-weight: bold;'
'date' => 'margin: 3px 0; font-size: 14px; color: #000;'
```

### **3. Common Adjustments**

#### **Move Name Higher/Lower:**
```php
'name' => 'margin: 5px 0; font-size: 32px;' // Less margin = closer to other elements
'name' => 'margin: 20px 0; font-size: 32px;' // More margin = more space
```

#### **Adjust Font Sizes:**
```php
'name' => 'font-size: 28px;' // Smaller name
'name' => 'font-size: 40px;' // Larger name
'event_title' => 'font-size: 20px;' // Smaller event title
```

#### **Hide/Show Elements:**
```php
'title' => 'display: none;' // Hide "SERTIFIKAT" text
'subtitle' => 'display: block; font-size: 16px;' // Show "Certificate of Participation"
```

#### **Change Text Colors:**
```php
'name' => 'color: #000;' // Black
'name' => 'color: #ffffff;' // White
'name' => 'color: #2c3e50;' // Dark blue
```

## 🔧 **Step-by-Step Adjustment Process**

### **Step 1: Identify Your Template**
1. Check your template filename in the database or storage
2. Look for the template detection condition in `getCertificatePositioning()`

### **Step 2: Modify Positioning**
Edit the positioning array for your template:

```php
if (strpos($templateName, 'YOUR_TEMPLATE_NAME') !== false) {
    return [
        'container' => 'padding-top: 100px;', // Adjust this to move content up/down
        'name' => 'margin: 15px 0; font-size: 36px;', // Adjust name spacing
        'event_title' => 'margin: 8px 0; font-size: 26px;', // Adjust event title
        // ... other elements
    ];
}
```

### **Step 3: Test Changes**
1. Download a certificate to see the changes
2. Adjust values as needed
3. Repeat until positioning is perfect

## 📐 **Positioning Reference**

### **Margin Values:**
- `margin: 10px 0` → 10px top/bottom, 0 left/right
- `margin: 5px 0 15px 0` → 5px top, 0 right, 15px bottom, 0 left
- `margin-top: 20px` → Only top margin

### **Padding Values:**
- `padding-top: 80px` → Space from top of certificate
- `padding-left: 50px` → Space from left edge
- `padding: 20px` → 20px all around

### **Font Sizes:**
- `font-size: 14px` → Small text (details)
- `font-size: 24px` → Medium text (event title)
- `font-size: 32px` → Large text (participant name)
- `font-size: 40px` → Extra large text

## 🎨 **Example Customizations**

### **For Template with Name in Center:**
```php
'container' => 'justify-content: center; align-items: center; padding-top: 0;'
'name' => 'margin: 20px 0; font-size: 40px; text-align: center;'
```

### **For Template with Name at Bottom:**
```php
'container' => 'justify-content: flex-end; align-items: center; padding-bottom: 100px;'
'name' => 'margin: 10px 0; font-size: 36px;'
```

### **For Template with White Text:**
```php
'name' => 'color: #ffffff; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);'
'event_title' => 'color: #ffffff;'
```

## 🔄 **Adding New Templates**

To add positioning for a new template:

```php
// Add this in getCertificatePositioning() method
if (strpos($templateName, 'new-template-filename') !== false) {
    return [
        'container' => 'your-container-styles',
        'name' => 'your-name-styles',
        'event_title' => 'your-event-title-styles',
        // ... other elements
    ];
}
```

## 📝 **Current Template Settings**

**Template:** `rQUuuutqBwYrgYUbgI0xUVAGDj8hjb05rgZ9WYZc.jpg`

**UPDATED Settings (Latest):**
- Container: Center-aligned, 150px from top, 100px from sides
- Name: 42px, black, 30px spacing, line-height 1.6
- Event title: 32px, bold, 25px margins
- Details: 18px, 15px spacing, line-height 1.8
- Hidden: Default title, subtitle, "Diberikan kepada" text

**Template Detection:** Automatically detects templates:
- `rQUuuutqBwYrgYUbgI0xUVAGDj8hjb05rgZ9WYZc`
- `iCefnksDkb9lTJA4smTteZzYZMSQNb9RzaVkwM0p`

**To adjust this template, modify the array starting at line ~451 in CertificateService.php**

## 🚀 **LATEST UPDATES (Enhanced Spacing)**

### **What Changed:**
- ✅ **MORE centered positioning** (150px top padding)
- ✅ **BIGGER font sizes** (Name: 42px, Event: 32px, Details: 18px)
- ✅ **INCREASED spacing** between elements (30px, 25px, 15px margins)
- ✅ **BETTER line heights** (1.6 for name, 1.8 for details)
- ✅ **Auto template detection** for multiple templates

### **Quick Customization Method:**
```php
// Use the new customizeCertificateLayout method
$customSettings = [
    'container_padding_top' => '200px',    // Move text lower
    'name_font_size' => '48px',            // Bigger name
    'detail_margin' => '20px 0',           // More spacing
];
```
