@echo off
echo ========================================
echo   TESTING REGISTER SUCCESS ROUTE
echo ========================================
echo.

echo [1/5] Clearing all caches...
php artisan route:clear
php artisan config:clear
php artisan cache:clear
php artisan view:clear

echo.
echo [2/5] Checking route files...
echo Checking web.php...
findstr /C:"require __DIR__.'/auth.php'" routes\web.php
echo.
echo Checking auth.php...
dir routes\auth.php

echo.
echo [3/5] Listing all routes...
php artisan route:list | findstr register

echo.
echo [4/5] Testing route generation...
php -r "
require_once 'vendor/autoload.php';
\$app = require_once 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    \$url = route('register.success');
    echo 'SUCCESS: Route URL generated: ' . \$url . PHP_EOL;
} catch (Exception \$e) {
    echo 'ERROR: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/5] Testing registration flow...
echo.
echo ========================================
echo   ROUTE TEST COMPLETED
echo ========================================
echo.
echo ROUTE STRUCTURE:
echo - routes/web.php includes routes/auth.php
echo - routes/auth.php contains register.success route
echo - Route: GET /register/success
echo - Name: register.success
echo - Controller: RegisteredUserController@success
echo.
echo TEST REGISTRATION:
echo 1. Go to: http://127.0.0.1:8000/register
echo 2. Fill form and submit
echo 3. Should redirect to: http://127.0.0.1:8000/register/success
echo 4. Should show success page with WhatsApp: 081382640946
echo.
echo If still getting route error:
echo 1. Restart Laravel server completely
echo 2. Check for syntax errors in route files
echo 3. Verify controller method exists
echo.
echo Starting Laravel server...
echo.

php artisan serve --host=127.0.0.1 --port=8000
