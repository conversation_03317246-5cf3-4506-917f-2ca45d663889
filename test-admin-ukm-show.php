<?php
/**
 * Test admin UKM show page functionality
 */

echo "=== TESTING ADMIN UKM SHOW PAGE ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing UKM data loading...\n";
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    if (!$ukm) {
        echo "   ❌ IMMA UKM not found\n";
        exit;
    }
    
    echo "   ✅ Found IMMA UKM (ID: {$ukm->id})\n";
    
    echo "\n2. Testing admin controller show method...\n";
    
    $controller = new \App\Http\Controllers\Admin\UkmManagementController();
    $request = \Illuminate\Http\Request::create("/admin/ukms/{$ukm->slug}", 'GET');
    
    try {
        $response = $controller->show($ukm);
        echo "   ✅ Admin UKM show controller executed successfully\n";
        
        if ($response instanceof \Illuminate\View\View) {
            echo "   ✅ Response is a view\n";
            echo "   View name: " . $response->getName() . "\n";
            
            $viewData = $response->getData();
            echo "   View data keys: " . implode(', ', array_keys($viewData)) . "\n";
            
            if (isset($viewData['ukm'])) {
                $ukmData = $viewData['ukm'];
                echo "   ✅ UKM data found\n";
                echo "   UKM name: {$ukmData->name}\n";
                echo "   Members count: " . ($ukmData->members_count ?? 'N/A') . "\n";
                echo "   Events count: " . ($ukmData->events_count ?? 'N/A') . "\n";
                
                // Check members relationship
                if ($ukmData->relationLoaded('members')) {
                    echo "   ✅ Members relationship loaded: " . $ukmData->members->count() . " members\n";
                    
                    foreach ($ukmData->members as $index => $member) {
                        if ($index >= 3) break; // Only show first 3
                        
                        echo "      Member " . ($index + 1) . ": {$member->name}\n";
                        echo "         Email: {$member->email}\n";
                        echo "         NIM: " . ($member->nim ?? 'N/A') . "\n";
                        echo "         Status: " . ($member->pivot->status ?? 'N/A') . "\n";
                        echo "         Role: " . ($member->pivot->role ?? 'N/A') . "\n";
                        echo "         ---\n";
                    }
                } else {
                    echo "   ❌ Members relationship not loaded\n";
                }
                
                // Check leader relationship
                if ($ukmData->relationLoaded('leader')) {
                    echo "   ✅ Leader relationship loaded\n";
                    if ($ukmData->leader) {
                        echo "      Leader: {$ukmData->leader->name}\n";
                    } else {
                        echo "      No leader assigned\n";
                    }
                } else {
                    echo "   ❌ Leader relationship not loaded\n";
                }
            }
            
            // Test rendering
            echo "\n   Testing view rendering...\n";
            $content = $response->render();
            echo "   ✅ View rendered successfully\n";
            echo "   Content length: " . strlen($content) . " characters\n";
            
            // Check for error messages in content
            $hasError = strpos($content, 'Data Anggota Tidak Valid') !== false;
            $hasValidMembers = strpos($content, 'Belum ada anggota') !== false || 
                              preg_match('/\d+ anggota/', $content);
            
            echo "   📊 Contains error message: " . ($hasError ? 'Yes ❌' : 'No ✅') . "\n";
            echo "   📊 Has valid member display: " . ($hasValidMembers ? 'Yes ✅' : 'No ❌') . "\n";
            
        }
        
    } catch (\Exception $e) {
        echo "   ❌ Controller error: " . $e->getMessage() . "\n";
        echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
        echo "   Stack trace:\n";
        echo $e->getTraceAsString() . "\n";
    }
    
    echo "\n3. Testing direct members loading...\n";
    
    // Test members loading directly
    $members = $ukm->members()->withPivot([
        'role', 'status', 'joined_date', 'left_date', 'notes',
        'previous_experience', 'skills_interests', 'reason_joining',
        'preferred_division', 'cv_file', 'applied_at', 'approved_at',
        'rejected_at', 'rejection_reason', 'approved_by', 'rejected_by'
    ])->get();
    
    echo "   📊 Direct members query result: " . $members->count() . " members\n";
    
    foreach ($members as $index => $member) {
        if ($index >= 3) break; // Only show first 3
        
        echo "      Member " . ($index + 1) . ": {$member->name}\n";
        echo "         ID: {$member->id}\n";
        echo "         Email: {$member->email}\n";
        echo "         Pivot status: " . ($member->pivot->status ?? 'N/A') . "\n";
        echo "         Pivot role: " . ($member->pivot->role ?? 'N/A') . "\n";
        echo "         ---\n";
    }
    
    echo "\n4. Creating test HTML page...\n";
    
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Admin UKM Show Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            background: #f9f9f9; 
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .member-card { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 10px 0; 
            background: white; 
        }
        .badge { 
            display: inline-block; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: bold; 
            margin-right: 5px; 
        }
        .badge-active { background: #d4edda; color: #155724; }
        .badge-pending { background: #fff3cd; color: #856404; }
        .badge-inactive { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🔧 Admin UKM Show Test</h1>
    
    <div class='test-section'>
        <h2>📊 UKM Information</h2>
        <p><strong>UKM:</strong> {$ukm->name}</p>
        <p><strong>Slug:</strong> {$ukm->slug}</p>
        <p><strong>Status:</strong> {$ukm->status}</p>
        <p><strong>Total Members:</strong> " . $members->count() . "</p>
    </div>
    
    <div class='test-section'>
        <h2>👥 Members List</h2>";
    
    if ($members->count() > 0) {
        foreach ($members as $member) {
            $statusClass = $member->pivot->status === 'active' ? 'badge-active' : 
                          ($member->pivot->status === 'pending' ? 'badge-pending' : 'badge-inactive');
            
            $testHtml .= "
        <div class='member-card'>
            <h4>{$member->name}</h4>
            <p><strong>Email:</strong> {$member->email}</p>
            <p><strong>NIM:</strong> " . ($member->nim ?? 'N/A') . "</p>
            <p><strong>Major:</strong> " . ($member->major ?? 'N/A') . "</p>
            <p><strong>Status:</strong> <span class='badge $statusClass'>" . ucfirst($member->pivot->status) . "</span></p>
            <p><strong>Role:</strong> " . ucfirst($member->pivot->role ?? 'member') . "</p>
            <p><strong>Joined:</strong> " . ($member->pivot->joined_date ? date('d M Y', strtotime($member->pivot->joined_date)) : 'N/A') . "</p>
        </div>";
        }
    } else {
        $testHtml .= "<p>No members found for this UKM.</p>";
    }
    
    $testHtml .= "
    </div>
    
    <div class='test-section'>
        <h2>🔗 Test Links</h2>
        <ul>
            <li><a href='http://127.0.0.1:8000/admin/ukms/{$ukm->slug}' target='_blank'><strong>Admin UKM Detail</strong> - Should show members without errors</a></li>
            <li><a href='http://127.0.0.1:8000/admin/ukms/{$ukm->id}/members' target='_blank'><strong>Admin Members Page</strong> - Dedicated members page</a></li>
            <li><a href='http://127.0.0.1:8000/admin/ukms' target='_blank'><strong>Admin UKM Index</strong> - All UKMs</a></li>
        </ul>
    </div>
    
    <div class='test-section'>
        <h2>✅ Expected Results</h2>
        <ul>
            <li>✅ No 'Data Anggota Tidak Valid' errors</li>
            <li>✅ Member names and details display correctly</li>
            <li>✅ Member status badges show properly</li>
            <li>✅ Member count displays accurately</li>
        </ul>
    </div>
</body>
</html>";
    
    file_put_contents(public_path('admin-ukm-show-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/admin-ukm-show-test.html\n";
    
    echo "\n=== ADMIN UKM SHOW TEST COMPLETED ===\n";
    echo "🔗 Test page: http://127.0.0.1:8000/admin-ukm-show-test.html\n";
    echo "🔗 Admin UKM detail: http://127.0.0.1:8000/admin/ukms/{$ukm->slug}\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
