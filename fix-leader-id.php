<?php

echo "=== FIXING LEADER ID MISMATCH ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Check current leader assignments
    echo "📋 CURRENT LEADER ASSIGNMENTS:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id,
            ukm.name,
            ukm.leader_id,
            u.name as leader_name,
            u.email as leader_email
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
    ");
    $assignments = $stmt->fetchAll();
    
    foreach ($assignments as $assignment) {
        echo "- UKM: {$assignment['name']} (ID: {$assignment['id']})\n";
        echo "  Leader ID: {$assignment['leader_id']}\n";
        echo "  Leader: " . ($assignment['leader_name'] ?: 'NOT FOUND') . "\n";
        echo "  Email: " . ($assignment['leader_email'] ?: 'N/A') . "\n";
        echo "  ---\n";
    }
    
    // Fix UKM Cendol leader
    echo "\n🔧 FIXING UKM CENDOL LEADER:\n";
    
    // Get Aras Agita fasya user ID
    $stmt = $pdo->prepare("SELECT id, name, email FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $aras = $stmt->fetch();
    
    if ($aras) {
        echo "Found Aras: ID {$aras['id']}, Name: {$aras['name']}\n";
        
        // Update UKM Cendol leader
        $stmt = $pdo->prepare("UPDATE ukms SET leader_id = ? WHERE name = 'UKM Cendol'");
        $stmt->execute([$aras['id']]);
        
        echo "✅ Updated UKM Cendol leader to Aras (ID: {$aras['id']})\n";
    } else {
        echo "❌ Aras not found!\n";
    }
    
    // Fix Sistem Informasi leader
    echo "\n🔧 FIXING SISTEM INFORMASI LEADER:\n";
    
    // Get Rehan user ID
    $stmt = $pdo->prepare("SELECT id, name, email FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $rehan = $stmt->fetch();
    
    if ($rehan) {
        echo "Found Rehan: ID {$rehan['id']}, Name: {$rehan['name']}\n";
        
        // Update Sistem Informasi leader
        $stmt = $pdo->prepare("UPDATE ukms SET leader_id = ? WHERE name = 'Sistem informasi'");
        $stmt->execute([$rehan['id']]);
        
        echo "✅ Updated Sistem Informasi leader to Rehan (ID: {$rehan['id']})\n";
    } else {
        echo "❌ Rehan not found!\n";
    }
    
    // Verify fixes
    echo "\n✅ VERIFICATION - UPDATED LEADER ASSIGNMENTS:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id,
            ukm.name,
            ukm.leader_id,
            u.name as leader_name,
            u.email as leader_email
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
    ");
    $updatedAssignments = $stmt->fetchAll();
    
    foreach ($updatedAssignments as $assignment) {
        echo "- UKM: {$assignment['name']} (ID: {$assignment['id']})\n";
        echo "  Leader ID: {$assignment['leader_id']}\n";
        echo "  Leader: " . ($assignment['leader_name'] ?: 'NOT FOUND') . "\n";
        echo "  Email: " . ($assignment['leader_email'] ?: 'N/A') . "\n";
        echo "  ---\n";
    }
    
    echo "\n🎯 Now ketua UKM should be able to see their pending members!\n";
    echo "Login as:\n";
    echo "- Aras (<EMAIL>) to manage UKM Cendol\n";
    echo "- Rehan (<EMAIL>) to manage Sistem Informasi\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
