<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Ukm;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

echo "=== DEBUGGING FORM SUBMISSION ISSUE ===\n";

echo "1. Checking routes...\n";

$routes = [
    'ketua-ukm.events' => 'Events list page',
    'ketua-ukm.events.create' => 'Create event form',
    'ketua-ukm.events.store' => 'Store event (POST)',
];

foreach ($routes as $routeName => $description) {
    if (Route::has($routeName)) {
        try {
            $url = route($routeName);
            echo "   ✅ {$description}: {$url}\n";
        } catch (\Exception $e) {
            echo "   ❌ {$description}: Error generating URL - {$e->getMessage()}\n";
        }
    } else {
        echo "   ❌ {$description}: Route not found\n";
    }
}

echo "2. Checking form view file...\n";

$formPath = resource_path('views/ketua-ukm/events/create.blade.php');
if (file_exists($formPath)) {
    echo "   ✅ Form view exists\n";
    
    $formContent = file_get_contents($formPath);
    
    // Check critical form elements
    $checks = [
        'action="{{ route(\'ketua-ukm.events.store\') }}"' => 'Form action URL',
        'method="POST"' => 'POST method',
        '@csrf' => 'CSRF token',
        'enctype="multipart/form-data"' => 'File upload support',
        'name="ukm_id"' => 'UKM ID field',
        'name="title"' => 'Title field',
        'name="description"' => 'Description field',
        'name="start_datetime"' => 'Start datetime field',
        'name="end_datetime"' => 'End datetime field',
        'name="location"' => 'Location field',
        'name="type"' => 'Type field',
        'type="submit"' => 'Submit button'
    ];
    
    echo "   Form element checks:\n";
    foreach ($checks as $check => $description) {
        $found = strpos($formContent, $check) !== false;
        echo "   " . ($found ? '✅' : '❌') . " {$description}: " . ($found ? 'Found' : 'Missing') . "\n";
    }
    
    // Check for potential issues
    $issues = [
        'disabled' => 'Disabled form elements',
        'readonly' => 'Readonly form elements',
        'onclick="return false"' => 'JavaScript preventing submission',
        'preventDefault()' => 'JavaScript preventing default',
        'return false' => 'JavaScript returning false'
    ];
    
    echo "   Potential issues:\n";
    foreach ($issues as $issue => $description) {
        $found = strpos($formContent, $issue) !== false;
        if ($found) {
            echo "   ⚠️  {$description}: Found (might prevent submission)\n";
        } else {
            echo "   ✅ {$description}: Not found (good)\n";
        }
    }
    
} else {
    echo "   ❌ Form view file not found\n";
}

echo "3. Checking controller method...\n";

$controllerPath = app_path('Http/Controllers/KetuaUkmController.php');
if (file_exists($controllerPath)) {
    $controllerContent = file_get_contents($controllerPath);
    
    // Check for storeEvent method
    if (strpos($controllerContent, 'function storeEvent') !== false) {
        echo "   ✅ storeEvent method exists\n";
        
        // Check for common issues in controller
        $controllerChecks = [
            'try {' => 'Error handling',
            'validate(' => 'Validation',
            'Event::create(' => 'Event creation',
            'redirect(' => 'Redirect after success',
            'Log::error' => 'Error logging'
        ];
        
        echo "   Controller checks:\n";
        foreach ($controllerChecks as $check => $description) {
            $found = strpos($controllerContent, $check) !== false;
            echo "   " . ($found ? '✅' : '❌') . " {$description}: " . ($found ? 'Found' : 'Missing') . "\n";
        }
        
    } else {
        echo "   ❌ storeEvent method not found\n";
    }
} else {
    echo "   ❌ Controller file not found\n";
}

echo "4. Checking middleware and authentication...\n";

// Check if user is authenticated
$ketuaUkm = User::where('role', 'ketua_ukm')->first();
if ($ketuaUkm) {
    echo "   ✅ Ketua UKM user exists: {$ketuaUkm->name}\n";
    
    // Check UKMs
    $ukms = Ukm::where('leader_id', $ketuaUkm->id)->get();
    echo "   UKMs led by this user: {$ukms->count()}\n";
    
    foreach ($ukms as $ukm) {
        echo "   - {$ukm->name} (ID: {$ukm->id})\n";
    }
    
    if ($ukms->count() === 0) {
        echo "   ⚠️  User has no UKMs - this might cause issues\n";
    }
    
} else {
    echo "   ❌ No ketua UKM user found\n";
}

echo "5. Common form submission issues to check...\n";

$commonIssues = [
    'CSRF Token Mismatch' => 'Check if @csrf is in form and session is working',
    'Validation Errors' => 'Check if all required fields are filled correctly',
    'JavaScript Errors' => 'Check browser console for JavaScript errors',
    'File Upload Issues' => 'Check if enctype="multipart/form-data" is set',
    'Route Not Found' => 'Check if route exists and is accessible',
    'Middleware Blocking' => 'Check if user has proper role and permissions',
    'Session Issues' => 'Check if session is working properly',
    'Database Connection' => 'Check if database is accessible',
    'Server Errors' => 'Check Laravel logs for 500 errors',
    'Redirect Loop' => 'Check if redirect logic is correct'
];

echo "   Issues to investigate:\n";
foreach ($commonIssues as $issue => $solution) {
    echo "   🔍 {$issue}: {$solution}\n";
}

echo "6. Debugging steps...\n";

echo "   📋 MANUAL DEBUGGING STEPS:\n";
echo "   1. Open browser developer tools (F12)\n";
echo "   2. Go to Network tab\n";
echo "   3. Navigate to: http://localhost:8000/ketua-ukm/events/create\n";
echo "   4. Fill out the form completely\n";
echo "   5. Click submit and watch Network tab\n";
echo "   6. Check what HTTP request is sent and what response is received\n";
echo "   7. Look for any JavaScript errors in Console tab\n";
echo "   8. Check Laravel logs: storage/logs/laravel.log\n";

echo "7. Quick test URLs...\n";

echo "   🌐 TEST URLS:\n";
echo "   - Form: http://localhost:8000/ketua-ukm/events/create\n";
echo "   - Events List: http://localhost:8000/ketua-ukm/events\n";
echo "   - Dashboard: http://localhost:8000/ketua-ukm/dashboard\n";

echo "8. Log file locations...\n";

$logPaths = [
    storage_path('logs/laravel.log') => 'Laravel application log',
    storage_path('logs/laravel-' . date('Y-m-d') . '.log') => 'Today\'s Laravel log',
];

echo "   📄 LOG FILES TO CHECK:\n";
foreach ($logPaths as $path => $description) {
    if (file_exists($path)) {
        $size = filesize($path);
        echo "   ✅ {$description}: {$path} ({$size} bytes)\n";
        
        // Show last few lines if file is not too big
        if ($size > 0 && $size < 1024 * 1024) { // Less than 1MB
            $lines = file($path);
            $lastLines = array_slice($lines, -5);
            echo "   Last 5 lines:\n";
            foreach ($lastLines as $line) {
                echo "   " . trim($line) . "\n";
            }
        }
    } else {
        echo "   ❌ {$description}: {$path} (not found)\n";
    }
}

echo "\n=== DEBUGGING COMPLETED ===\n";
echo "✅ Use the manual debugging steps above to identify the exact issue\n";
echo "🔍 Focus on Network tab to see what happens when form is submitted\n";
echo "📊 Check Laravel logs for any server-side errors\n";

?>
