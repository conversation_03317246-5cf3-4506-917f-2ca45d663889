<?php

echo "=== CREATING SPATIE PERMISSION TABLES ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // Read SQL file
    $sql = file_get_contents('create-permission-tables.sql');
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
            
            // Show progress for major operations
            if (stripos($statement, 'CREATE TABLE') !== false) {
                if (preg_match('/CREATE TABLE.*?(\w+)\s*\(/i', $statement, $matches)) {
                    echo "✅ Created table: {$matches[1]}\n";
                }
            } elseif (stripos($statement, 'INSERT') !== false && stripos($statement, 'roles') !== false) {
                echo "✅ Inserted roles\n";
            } elseif (stripos($statement, 'INSERT') !== false && stripos($statement, 'permissions') !== false) {
                echo "✅ Inserted permissions\n";
            }
        } catch (Exception $e) {
            // Only show error if it's not "already exists" type
            if (strpos($e->getMessage(), 'already exists') === false && 
                strpos($e->getMessage(), 'Duplicate entry') === false) {
                echo "❌ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n📊 VERIFICATION:\n";
    
    // Check tables exist
    $tables = ['permissions', 'roles', 'model_has_permissions', 'model_has_roles', 'role_has_permissions'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            echo "✅ Table {$table}: {$count} records\n";
        } catch (Exception $e) {
            echo "❌ Table {$table}: Not found or error\n";
        }
    }
    
    // Show role assignments
    echo "\n👥 USER ROLE ASSIGNMENTS:\n";
    
    $stmt = $pdo->query("
        SELECT u.name, u.email, u.role as user_role, r.name as assigned_role
        FROM users u
        LEFT JOIN model_has_roles mhr ON mhr.model_id = u.id AND mhr.model_type = 'App\\\\Models\\\\User'
        LEFT JOIN roles r ON r.id = mhr.role_id
        ORDER BY u.id
    ");
    
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        $assignedRole = $user['assigned_role'] ?: 'None';
        echo "- {$user['name']} ({$user['email']}) - User Role: {$user['user_role']}, Assigned Role: {$assignedRole}\n";
    }
    
    echo "\n✅ Spatie Permission tables created and configured!\n";
    echo "🎯 Admin edit user functionality should now work.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
