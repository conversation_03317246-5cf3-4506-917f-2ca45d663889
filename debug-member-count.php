<?php
/**
 * Debug member count logic
 */

echo "=== DEBUGGING MEMBER COUNT LOGIC ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Analyzing UKM member counts...\n";
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    
    if (!$ukm) {
        echo "   ❌ UKM not found\n";
        exit;
    }
    
    echo "   ✅ UKM: {$ukm->name}\n";
    echo "   📊 Current members (database field): {$ukm->current_members}\n";
    echo "   📊 Max members: {$ukm->max_members}\n";
    
    echo "\n2. Analyzing actual member data...\n";
    
    // Get all members with their status
    $allMembers = $ukm->members()->withPivot(['status', 'role', 'joined_date', 'left_date'])->get();
    
    echo "   📋 Total member records: " . $allMembers->count() . "\n";
    
    $statusCounts = [
        'active' => 0,
        'pending' => 0,
        'inactive' => 0,
        'rejected' => 0,
        'alumni' => 0,
    ];
    
    foreach ($allMembers as $member) {
        $status = $member->pivot->status;
        if (isset($statusCounts[$status])) {
            $statusCounts[$status]++;
        }
        
        echo "      Member: {$member->name} - Status: {$status} - Role: {$member->pivot->role}\n";
    }
    
    echo "\n   📊 Status breakdown:\n";
    foreach ($statusCounts as $status => $count) {
        echo "      {$status}: {$count}\n";
    }
    
    echo "\n3. Checking what should be counted as 'active members'...\n";
    
    $actualActiveMembers = $ukm->members()->wherePivot('status', 'active')->count();
    echo "   📊 Actual active members: {$actualActiveMembers}\n";
    
    $pendingMembers = $ukm->members()->wherePivot('status', 'pending')->count();
    echo "   📊 Pending members: {$pendingMembers}\n";
    
    echo "\n4. Identifying the problem...\n";
    
    if ($ukm->current_members != $actualActiveMembers) {
        echo "   🔍 PROBLEM FOUND: Database field doesn't match actual active members\n";
        echo "      Database field: {$ukm->current_members}\n";
        echo "      Actual active: {$actualActiveMembers}\n";
        echo "      Difference: " . ($ukm->current_members - $actualActiveMembers) . "\n";
    } else {
        echo "   ✅ Member count is correct\n";
    }
    
    echo "\n5. Checking UKM model methods...\n";
    
    // Check if UKM has activeMembers relationship
    $activeFromRelation = $ukm->activeMembers()->count();
    echo "   📊 Active members (from relation): {$activeFromRelation}\n";
    
    echo "\n6. Finding where current_members is updated...\n";
    
    // This will help us understand where the issue comes from
    echo "   🔍 Current_members field is likely updated in:\n";
    echo "      - UkmController when approving members\n";
    echo "      - UkmController when removing members\n";
    echo "      - KetuaUkmController when managing members\n";
    
    echo "\n7. Fixing the count...\n";
    
    $correctCount = $actualActiveMembers;
    $ukm->update(['current_members' => $correctCount]);
    
    echo "   ✅ Updated current_members from {$ukm->current_members} to {$correctCount}\n";
    
    echo "\n8. Creating fix script...\n";
    
    $fixScript = "<?php
// Fix all UKM member counts
require_once 'vendor/autoload.php';
\$app = require_once 'bootstrap/app.php';
\$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

\$ukms = \\App\\Models\\Ukm::all();

foreach (\$ukms as \$ukm) {
    \$actualActiveCount = \$ukm->members()->wherePivot('status', 'active')->count();
    
    if (\$ukm->current_members != \$actualActiveCount) {
        echo \"Fixing {\$ukm->name}: {\$ukm->current_members} -> {\$actualActiveCount}\\n\";
        \$ukm->update(['current_members' => \$actualActiveCount]);
    }
}

echo \"All UKM member counts fixed!\\n\";
?>";
    
    file_put_contents('fix-all-member-counts.php', $fixScript);
    echo "   ✅ Created fix script: fix-all-member-counts.php\n";
    
    echo "\n=== MEMBER COUNT DEBUG COMPLETED ===\n";
    echo "📊 Summary:\n";
    echo "   - Database field: {$ukm->current_members} (before fix)\n";
    echo "   - Actual active: {$actualActiveMembers}\n";
    echo "   - Fixed to: {$correctCount}\n";
    echo "   - Available slots: " . ($ukm->max_members - $correctCount) . "\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
