<?php
echo "=== SIMPLE UKM DATA INSERT ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n";
    
    // Clear existing data
    echo "Clearing existing UKM data...\n";
    $pdo->exec("DELETE FROM ukms");
    echo "✅ Existing data cleared\n\n";
    
    // Insert new data
    echo "Inserting new UKM data...\n";
    
    $sql = "INSERT INTO ukms (name, slug, description, category, email, phone, instagram, website, recruitment_open, max_members, current_members, status, established_year, vision, mission, meeting_schedule, meeting_location, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
    
    $stmt = $pdo->prepare($sql);
    
    $ukms = [
        ['UKM Badminton', 'badminton', 'Unit Kegiatan Mahasiswa Badminton Telkom Jakarta adalah wadah bagi mahasiswa yang memiliki passion dalam olahraga badminton. Kami mengembangkan kemampuan teknik, taktik, dan mental bertanding melalui latihan rutin dan kompetisi.', 'Olahraga', '<EMAIL>', '081234567801', '@ukmbadminton_telkomjkt', null, 1, 50, 35, 'active', 2018, 'Menjadi UKM badminton terdepan yang menghasilkan atlet berprestasi dan berkarakter.', 'Mengembangkan potensi mahasiswa dalam bidang badminton melalui latihan berkualitas, kompetisi, dan pembinaan karakter.', 'Selasa & Kamis 16:00-18:00, Sabtu 08:00-10:00', 'GOR Telkom University Jakarta'],
        
        ['UKM DPM (Dewan Perwakilan Mahasiswa)', 'dpm', 'Dewan Perwakilan Mahasiswa (DPM) adalah lembaga legislatif mahasiswa yang berperan sebagai pengawas dan mitra BEM dalam menjalankan program kerja kemahasiswaan.', 'Politik & Pemerintahan', '<EMAIL>', '081234567802', '@dpm_telkomjkt', null, 1, 25, 20, 'active', 2017, 'Menjadi lembaga legislatif mahasiswa yang aspiratif, demokratis, dan berintegritas.', 'Menyuarakan aspirasi mahasiswa, mengawasi kinerja eksekutif mahasiswa, dan memperjuangkan kepentingan mahasiswa.', 'Senin 19:00-21:00', 'Ruang Sidang Kemahasiswaan'],
        
        ['UKM Esport', 'esport', 'UKM Esport Telkom Jakarta adalah komunitas gamers yang fokus pada pengembangan kemampuan bermain game kompetitif. Kami menaungi berbagai divisi game seperti Mobile Legends, PUBG Mobile, Valorant, dan Dota 2.', 'Teknologi & Gaming', '<EMAIL>', '081234567803', '@esport_telkomjkt', 'https://esport.telkomuniversity.ac.id', 1, 80, 65, 'active', 2019, 'Menjadi UKM esport terdepan yang menghasilkan atlet esport berprestasi tingkat nasional.', 'Mengembangkan ekosistem esport di kampus, membina atlet esport berkualitas, dan membangun komunitas gaming yang positif.', 'Senin, Rabu, Jumat 19:00-22:00', 'Lab Gaming & Multimedia'],
        
        ['UKM Futsal', 'futsal', 'UKM Futsal Telkom Jakarta adalah wadah bagi mahasiswa yang memiliki passion dalam olahraga futsal. Kami mengembangkan teknik individu, kerjasama tim, dan strategi permainan melalui latihan intensif.', 'Olahraga', '<EMAIL>', '081234567804', '@futsal_telkomjkt', null, 1, 40, 32, 'active', 2018, 'Menjadi tim futsal universitas yang kompetitif dan berprestasi di tingkat regional.', 'Mengembangkan kemampuan futsal mahasiswa, membangun sportivitas, dan mengharumkan nama Telkom University Jakarta.', 'Selasa & Kamis 16:00-18:00, Minggu 08:00-10:00', 'Lapangan Futsal Kampus'],
        
        ['UKM IMMA (Ikatan Mahasiswa Muslim Akuntansi)', 'imma', 'IMMA adalah organisasi yang menghimpun mahasiswa muslim khususnya dari program studi akuntansi dan ekonomi. Kami fokus pada pengembangan spiritual, akademik, dan sosial.', 'Keagamaan', '<EMAIL>', '081234567805', '@imma_telkomjkt', null, 1, 60, 45, 'active', 2017, 'Menjadi wadah pengembangan mahasiswa muslim yang berakhlak mulia dan kompeten di bidang ekonomi.', 'Menyelenggarakan kajian Islam, mengembangkan pemahaman ekonomi syariah, dan membangun ukhuwah islamiyah.', 'Jumat 13:00-15:00', 'Musholla Kampus'],
        
        ['UKM Mapala (Mahasiswa Pecinta Alam)', 'mapala', 'UKM Mapala Telkom Jakarta adalah komunitas mahasiswa yang memiliki kecintaan terhadap alam dan lingkungan. Kami melakukan kegiatan pendakian gunung, camping, rock climbing, dan konservasi alam.', 'Alam & Lingkungan', '<EMAIL>', '081234567806', '@mapala_telkomjkt', null, 1, 45, 38, 'active', 2018, 'Menjadi komunitas pecinta alam yang berperan aktif dalam pelestarian lingkungan dan pembentukan karakter.', 'Mengembangkan kecintaan terhadap alam, melatih mental dan fisik melalui kegiatan alam, serta berkontribusi dalam konservasi lingkungan.', 'Sabtu 14:00-17:00', 'Basecamp Mapala'],
        
        ['UKM PMK (Persekutuan Mahasiswa Kristen)', 'pmk', 'PMK adalah wadah persekutuan bagi mahasiswa Kristen di Telkom University Jakarta. Kami menyelenggarakan ibadah, fellowship, retreat, dan pelayanan sosial.', 'Keagamaan', '<EMAIL>', '081234567807', '@pmk_telkomjkt', null, 1, 40, 28, 'active', 2017, 'Menjadi persekutuan yang membawa terang Kristus dalam kehidupan kampus dan masyarakat.', 'Menguatkan iman mahasiswa Kristen, membangun persekutuan yang solid, dan melayani sesama dengan kasih.', 'Kamis 18:00-20:00', 'Ruang Persekutuan'],
        
        ['UKM Seni Budaya', 'seni-budaya', 'UKM Seni Budaya Telkom Jakarta adalah wadah kreativitas mahasiswa dalam bidang seni dan budaya Indonesia. Kami mengembangkan berbagai kesenian tradisional seperti tari, musik tradisional, teater, dan seni rupa.', 'Seni & Budaya', '<EMAIL>', '081234567808', '@senibudaya_telkomjkt', null, 1, 55, 42, 'active', 2017, 'Menjadi pusat pengembangan seni dan budaya yang melestarikan kearifan lokal Indonesia.', 'Mengembangkan bakat seni mahasiswa, melestarikan budaya Indonesia, dan memperkenalkan kesenian tradisional kepada generasi muda.', 'Rabu & Sabtu 15:00-18:00', 'Studio Seni & Budaya'],
        
        ['UKM Sistem Informasi', 'sistem-informasi', 'UKM Sistem Informasi adalah komunitas mahasiswa yang fokus pada pengembangan teknologi informasi dan sistem. Kami menyelenggarakan workshop programming, seminar teknologi, hackathon, dan project development.', 'Teknologi & Informasi', '<EMAIL>', '081234567809', '@si_telkomjkt', 'https://si.telkomuniversity.ac.id', 1, 70, 58, 'active', 2018, 'Menjadi komunitas IT terdepan yang menghasilkan lulusan kompeten dan inovatif.', 'Mengembangkan kemampuan programming, membangun solusi teknologi, dan mempersiapkan mahasiswa untuk industri IT.', 'Selasa & Kamis 19:00-21:00', 'Lab Komputer & Programming']
    ];
    
    $created = 0;
    foreach ($ukms as $ukm) {
        if ($stmt->execute($ukm)) {
            echo "✅ {$ukm[0]}\n";
            $created++;
        } else {
            echo "❌ Failed: {$ukm[0]}\n";
        }
    }
    
    echo "\n=== RESULT ===\n";
    echo "✅ Successfully created: {$created} UKMs\n";
    
    // Check total
    $result = $pdo->query("SELECT COUNT(*) as total FROM ukms");
    $total = $result->fetch(PDO::FETCH_ASSOC)['total'];
    echo "📊 Total UKMs in database: {$total}\n";
    
    // Show categories
    echo "\n📋 Categories:\n";
    $result = $pdo->query("SELECT category, COUNT(*) as count FROM ukms GROUP BY category");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "   • {$row['category']}: {$row['count']} UKMs\n";
    }
    
    echo "\n🌐 Test URLs:\n";
    echo "   UKM Index: http://localhost:8000/ukm\n";
    echo "   Sample UKM: http://localhost:8000/ukm/badminton\n";
    echo "   Admin UKMs: http://localhost:8000/admin/ukms\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== COMPLETE ===\n";
?>
