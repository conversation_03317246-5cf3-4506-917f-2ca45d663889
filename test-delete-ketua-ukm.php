<?php

echo "=== TESTING DELETE KETUA UKM FUNCTIONALITY ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing route existence...\n";
    
    // Test if route exists
    try {
        $url = route('admin.ketua-ukm.destroy', 1);
        echo "   ✅ Route admin.ketua-ukm.destroy exists: {$url}\n";
    } catch (Exception $e) {
        echo "   ❌ Route admin.ketua-ukm.destroy not found: " . $e->getMessage() . "\n";
        exit(1);
    }
    
    echo "\n2. Testing controller method...\n";
    
    // Test controller instantiation
    $controller = new \App\Http\Controllers\Admin\KetuaUkmManagementController();
    echo "   ✅ KetuaUkmManagementController instantiated\n";
    
    // Test if destroy method exists
    if (method_exists($controller, 'destroy')) {
        echo "   ✅ destroy method exists\n";
    } else {
        echo "   ❌ destroy method not found\n";
        exit(1);
    }
    
    echo "\n3. Testing database connection and ketua UKM data...\n";
    
    // Get ketua UKM users
    $ketuaUkms = \App\Models\User::where('role', 'ketua_ukm')->get();
    echo "   ✅ Found " . $ketuaUkms->count() . " ketua UKM users\n";
    
    if ($ketuaUkms->count() > 0) {
        $firstKetuaUkm = $ketuaUkms->first();
        echo "   ✅ First ketua UKM: {$firstKetuaUkm->name} (ID: {$firstKetuaUkm->id})\n";
        echo "   ✅ Role: {$firstKetuaUkm->role}\n";
        echo "   ✅ Status: {$firstKetuaUkm->status}\n";
        echo "   ✅ Led UKMs count: " . $firstKetuaUkm->ledUkms()->count() . "\n";
        
        // Check if this user can be deleted (not leading any UKM)
        if ($firstKetuaUkm->ledUkms()->count() === 0) {
            echo "   ✅ This ketua UKM can be deleted (not leading any UKM)\n";
        } else {
            echo "   ⚠️  This ketua UKM cannot be deleted (leading " . $firstKetuaUkm->ledUkms()->count() . " UKM(s))\n";
        }
    }
    
    echo "\n4. Testing CSRF token generation...\n";
    
    // Test CSRF token
    $token = csrf_token();
    if ($token) {
        echo "   ✅ CSRF token generated: " . substr($token, 0, 10) . "...\n";
    } else {
        echo "   ❌ CSRF token not generated\n";
    }
    
    echo "\n5. Testing form HTML generation...\n";
    
    if ($ketuaUkms->count() > 0) {
        $testUser = $ketuaUkms->first();
        $formAction = route('admin.ketua-ukm.destroy', $testUser);
        
        echo "   ✅ Form action URL: {$formAction}\n";
        
        // Generate sample form HTML
        $formHtml = '
<form action="' . $formAction . '" method="POST" class="inline">
    <input type="hidden" name="_token" value="' . $token . '">
    <input type="hidden" name="_method" value="DELETE">
    <button type="submit" class="text-red-600 hover:text-red-900">
        Hapus
    </button>
</form>';
        
        echo "   ✅ Sample form HTML generated successfully\n";
    }
    
    echo "\n=== ALL TESTS PASSED ===\n";
    echo "The delete functionality should work. Check browser console for JavaScript errors.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
