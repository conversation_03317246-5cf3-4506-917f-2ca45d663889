<?php
/**
 * Test achievement certificate access without login
 */

echo "=== TESTING ACHIEVEMENT CERTIFICATE ACCESS ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing achievements with certificates...\n";
    
    $achievements = \App\Models\UkmAchievement::whereNotNull('certificate_file')->get();
    echo "   📊 Achievements with certificates: " . $achievements->count() . "\n";
    
    foreach ($achievements as $achievement) {
        echo "   📋 Achievement: {$achievement->title}\n";
        echo "      Certificate: {$achievement->certificate_file}\n";
        echo "      UKM: {$achievement->ukm->name}\n";
        
        // Test file existence
        $certPath = public_path('storage/' . $achievement->certificate_file);
        $exists = file_exists($certPath);
        echo "      File exists: " . ($exists ? "✅" : "❌") . "\n";
        
        if ($exists) {
            $url = asset('storage/' . $achievement->certificate_file);
            echo "      URL: $url\n";
        }
        echo "      ---\n";
    }
    
    echo "\n2. Testing achievement routes without auth...\n";
    
    // Test achievement index
    $controller = new \App\Http\Controllers\AchievementController();
    $request = \Illuminate\Http\Request::create('/prestasi', 'GET');
    
    try {
        $response = $controller->index($request);
        echo "   ✅ Achievement index accessible without login\n";
        
        if ($response instanceof \Illuminate\View\View) {
            $viewData = $response->getData();
            echo "   📊 Achievements in view: " . $viewData['achievements']->count() . "\n";
        }
        
    } catch (\Exception $e) {
        echo "   ❌ Achievement index error: " . $e->getMessage() . "\n";
    }
    
    // Test achievement show
    if ($achievements->count() > 0) {
        $firstAchievement = $achievements->first();
        $showRequest = \Illuminate\Http\Request::create("/prestasi/{$firstAchievement->id}", 'GET');
        
        try {
            $showResponse = $controller->show($firstAchievement);
            echo "   ✅ Achievement show accessible without login\n";
            
            if ($showResponse instanceof \Illuminate\View\View) {
                echo "   ✅ Achievement detail view rendered\n";
            }
            
        } catch (\Exception $e) {
            echo "   ❌ Achievement show error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n3. Testing certificate file access...\n";
    
    // Create test HTML to check certificate access
    $testHtml = "<!DOCTYPE html>
<html>
<head>
    <title>Certificate Access Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            background: #f9f9f9; 
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .cert-card { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 10px 0; 
            background: white; 
        }
        .cert-link {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 10px;
        }
        .cert-link:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <h1>🏆 Certificate Access Test (No Login Required)</h1>
    
    <div class='test-section'>
        <h2>📋 Available Certificates</h2>
        <p>These certificates should be accessible without login:</p>";
    
    foreach ($achievements as $achievement) {
        $certUrl = asset('storage/' . $achievement->certificate_file);
        $testHtml .= "
        <div class='cert-card'>
            <h4>{$achievement->title}</h4>
            <p><strong>UKM:</strong> {$achievement->ukm->name}</p>
            <p><strong>Level:</strong> " . ucfirst($achievement->level) . "</p>
            <p><strong>Date:</strong> " . $achievement->achievement_date->format('d M Y') . "</p>
            <p><strong>Certificate URL:</strong> $certUrl</p>
            <a href='$certUrl' target='_blank' class='cert-link'>
                📜 View Certificate
            </a>
            <a href='" . route('achievements.show', $achievement) . "' target='_blank' class='cert-link' style='background: #007bff; margin-left: 10px;'>
                📄 View Achievement Detail
            </a>
        </div>";
    }
    
    $testHtml .= "
    </div>
    
    <div class='test-section'>
        <h2>🔗 Test Links</h2>
        <ul>
            <li><a href='http://127.0.0.1:8000/prestasi' target='_blank'><strong>All Achievements</strong> - Should be accessible without login</a></li>";
    
    if ($achievements->count() > 0) {
        $firstAchievement = $achievements->first();
        $testHtml .= "
            <li><a href='" . route('achievements.show', $firstAchievement) . "' target='_blank'><strong>Sample Achievement Detail</strong> - Should show certificate download</a></li>";
    }
    
    $testHtml .= "
            <li><a href='http://127.0.0.1:8000/ukms/imma' target='_blank'><strong>UKM Detail</strong> - Should show achievement certificates</a></li>
        </ul>
    </div>
    
    <div class='test-section'>
        <h2>✅ Expected Behavior</h2>
        <ul>
            <li>✅ Prestasi page accessible without login</li>
            <li>✅ Achievement detail pages accessible without login</li>
            <li>✅ Certificate files downloadable without login</li>
            <li>✅ Certificate links work in UKM detail pages</li>
        </ul>
    </div>
    
    <div class='test-section'>
        <h2>🧪 Test Instructions</h2>
        <ol>
            <li>Open this page in incognito/private browsing mode</li>
            <li>Make sure you're not logged in</li>
            <li>Click on certificate links above</li>
            <li>All certificates should open/download without requiring login</li>
        </ol>
    </div>
</body>
</html>";
    
    file_put_contents(public_path('certificate-access-test.html'), $testHtml);
    echo "   ✅ Created test page: http://127.0.0.1:8000/certificate-access-test.html\n";
    
    echo "\n4. Checking middleware on routes...\n";
    
    // Check if there are any middleware that might block access
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    
    foreach ($routes as $route) {
        if (str_contains($route->uri(), 'prestasi') || str_contains($route->uri(), 'achievements')) {
            $middleware = $route->middleware();
            echo "   Route: {$route->uri()}\n";
            echo "   Middleware: " . (empty($middleware) ? 'None' : implode(', ', $middleware)) . "\n";
            echo "   ---\n";
        }
    }
    
    echo "\n=== CERTIFICATE ACCESS TEST COMPLETED ===\n";
    echo "🔗 Test page: http://127.0.0.1:8000/certificate-access-test.html\n";
    echo "🔗 Prestasi page: http://127.0.0.1:8000/prestasi\n";
    
    if ($achievements->count() > 0) {
        $firstCert = $achievements->first();
        $certUrl = asset('storage/' . $firstCert->certificate_file);
        echo "🔗 Sample certificate: $certUrl\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
