<?php
echo "=== FAVICON TEST ===\n\n";

$publicDir = 'public/';
$faviconFiles = [
    'favicon.ico',
    'favicon-16x16.png',
    'favicon-32x32.png', 
    'favicon-48x48.png',
    'favicon-64x64.png',
    'favicon-128x128.png',
    'favicon-180x180.png',
    'apple-touch-icon.png',
    'telkom-logo.png'
];

echo "📁 Checking favicon files:\n";
foreach ($faviconFiles as $file) {
    $path = $publicDir . $file;
    if (file_exists($path)) {
        $size = filesize($path);
        echo "  ✅ $file (" . number_format($size) . " bytes)\n";
    } else {
        echo "  ❌ $file (missing)\n";
    }
}

echo "\n🌐 Favicon URLs to test:\n";
$baseUrl = 'http://localhost:8000';
foreach ($faviconFiles as $file) {
    if (file_exists($publicDir . $file)) {
        echo "  $baseUrl/$file\n";
    }
}

echo "\n🧪 Test pages:\n";
echo "  Main site: $baseUrl\n";
echo "  Admin panel: $baseUrl/admin\n";
echo "  Login page: $baseUrl/login\n";

echo "\n📋 How to verify favicon is working:\n";
echo "1. Open browser and go to http://localhost:8000\n";
echo "2. Look at browser tab - should show Telkom logo\n";
echo "3. Check browser developer tools > Network tab\n";
echo "4. Look for favicon requests (should return 200 status)\n";
echo "5. Try hard refresh (Ctrl+F5) to clear browser cache\n";

echo "\n🔧 If favicon doesn't appear:\n";
echo "1. Clear browser cache completely\n";
echo "2. Try incognito/private browsing mode\n";
echo "3. Check browser developer console for errors\n";
echo "4. Wait a few minutes (browsers cache favicons aggressively)\n";

echo "\n=== TEST COMPLETE ===\n";
?>
