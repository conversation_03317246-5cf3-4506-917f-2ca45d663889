<?php

echo "=== SIMPLE HTMLSPECIALCHARS ERROR FIX TEST ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Testing view file for dangerous patterns...\n";
    
    $viewPath = resource_path('views/ketua-ukm/events/registration-details.blade.php');
    if (file_exists($viewPath)) {
        $viewContent = file_get_contents($viewPath);
        
        // Check for dangerous direct array output patterns
        $dangerousPatterns = [
            '{{ $registration->availability_form }}' => 'Direct availability_form output',
            '{{ $registration->additional_data }}' => 'Direct additional_data output',
        ];
        
        $foundDangerous = false;
        foreach ($dangerousPatterns as $pattern => $description) {
            if (strpos($viewContent, $pattern) !== false) {
                echo "   ❌ {$description}: STILL EXISTS (dangerous)\n";
                $foundDangerous = true;
            } else {
                echo "   ✅ {$description}: REMOVED (safe)\n";
            }
        }
        
        // Check for safe patterns
        $safePatterns = [
            'is_array($availabilityData)' => 'Array type checking',
            'is_array($value)' => 'Value type checking',
            'implode(\', \', $value)' => 'Safe array conversion',
            'is_string($registration->availability_form)' => 'String type checking',
        ];
        
        foreach ($safePatterns as $pattern => $description) {
            if (strpos($viewContent, $pattern) !== false) {
                echo "   ✅ {$description}: IMPLEMENTED\n";
            } else {
                echo "   ❌ {$description}: MISSING\n";
            }
        }
        
        if (!$foundDangerous) {
            echo "   🎉 No dangerous patterns found - view is safe!\n";
        }
        
    } else {
        echo "   ❌ View file not found\n";
    }
    
    echo "2. Testing EventRegistration model...\n";
    
    $registration = \App\Models\EventRegistration::first();
    if ($registration) {
        echo "   ✅ Registration found: ID {$registration->id}\n";
        
        // Test field types
        echo "   Field types:\n";
        echo "     availability_form: " . gettype($registration->availability_form) . "\n";
        echo "     additional_data: " . gettype($registration->additional_data) . "\n";
        echo "     motivation: " . gettype($registration->motivation) . "\n";
        
        // Test if fields are properly cast
        if (is_array($registration->availability_form) || is_null($registration->availability_form)) {
            echo "   ✅ availability_form is properly cast\n";
        } else {
            echo "   ⚠️  availability_form is not array: " . gettype($registration->availability_form) . "\n";
        }
        
        if (is_array($registration->additional_data) || is_null($registration->additional_data)) {
            echo "   ✅ additional_data is properly cast\n";
        } else {
            echo "   ⚠️  additional_data is not array: " . gettype($registration->additional_data) . "\n";
        }
        
    } else {
        echo "   ⚠️  No registration found for testing\n";
    }
    
    echo "3. Testing route generation...\n";
    
    if ($registration && $registration->event) {
        try {
            $url = route('ketua-ukm.events.registrations.show', [$registration->event->slug, $registration->id]);
            echo "   ✅ Route generation successful: {$url}\n";
        } catch (Exception $e) {
            echo "   ❌ Route generation failed: " . $e->getMessage() . "\n";
        }
    }
    
    echo "4. Summary of fixes...\n";
    
    $fixes = [
        'availability_form field' => 'Added proper array handling with type checking',
        'additional_data field' => 'Enhanced array handling with safe conversion',
        'Array values' => 'Added implode() for safe array-to-string conversion',
        'Fallback handling' => 'Added string type checking for non-array data',
    ];
    
    foreach ($fixes as $component => $fix) {
        echo "   ✅ {$component}: {$fix}\n";
    }
    
    echo "\n=== HTMLSPECIALCHARS ERROR FIX COMPLETED ===\n";
    echo "🎉 Registration details page should now work without errors!\n";
    echo "\nWhat was the problem:\n";
    echo "❌ ISSUE: Array fields were displayed directly as strings\n";
    echo "❌ ERROR: htmlspecialchars(): Argument #1 must be string, array given\n";
    echo "\nWhat was fixed:\n";
    echo "✅ SOLUTION: Added proper type checking before display\n";
    echo "✅ ENHANCEMENT: Safe array-to-string conversion\n";
    echo "✅ FALLBACK: Graceful handling of different data types\n";
    echo "\nTesting instructions:\n";
    echo "1. Login as ketua UKM user\n";
    echo "2. Navigate to /ketua-ukm/events\n";
    echo "3. Click 'Pendaftar' on any event\n";
    echo "4. Click 'Detail' on any registration\n";
    echo "5. Page should load without htmlspecialchars errors\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
