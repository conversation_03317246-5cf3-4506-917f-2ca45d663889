<?php

echo "=== DATABASE RECOVERY SCRIPT ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected\n\n";
    
    // 1. Check current state
    echo "1. 📊 CHECKING CURRENT STATE...\n";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "   ❌ Users table missing - database needs migration\n";
        echo "   🔧 Run: php artisan migrate\n\n";
        exit(1);
    }
    
    // Check user count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    echo "   📊 Current users: $userCount\n";
    
    if ($userCount > 0) {
        echo "   👥 Existing users:\n";
        $stmt = $pdo->query("SELECT email, role, status FROM users");
        while ($user = $stmt->fetch()) {
            echo "      - {$user['email']} ({$user['role']}) - {$user['status']}\n";
        }
        echo "\n   ✅ Users exist - login issue might be password/status related\n\n";
    } else {
        echo "   ❌ No users found - creating admin users...\n\n";
        
        // 2. Create admin users
        echo "2. 👥 CREATING ADMIN USERS...\n";
        
        $users = [
            [
                'nim' => 'ADMIN001',
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukh4Q0k2G', // admin123
                'phone' => '081234567890',
                'gender' => 'male',
                'faculty' => 'Administrasi',
                'major' => 'Sistem Informasi',
                'batch' => '2024',
                'role' => 'admin',
                'status' => 'active'
            ],
            [
                'nim' => '1103210001',
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'password' => '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukh4Q0k2G', // admin123
                'phone' => '081234567892',
                'gender' => 'male',
                'faculty' => 'Informatika',
                'major' => 'Teknik Informatika',
                'batch' => '2021',
                'role' => 'student',
                'status' => 'active'
            ],
            [
                'nim' => '1103210002',
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'password' => '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukh4Q0k2G', // admin123
                'phone' => '081234567893',
                'gender' => 'female',
                'faculty' => 'Informatika',
                'major' => 'Sistem Informasi',
                'batch' => '2021',
                'role' => 'ketua_ukm',
                'status' => 'active'
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO users (nim, name, email, password, phone, gender, faculty, major, batch, role, status, email_verified_at, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())
            ON DUPLICATE KEY UPDATE 
            name = VALUES(name), 
            password = VALUES(password),
            role = VALUES(role),
            status = VALUES(status),
            updated_at = NOW()
        ");
        
        foreach ($users as $user) {
            $stmt->execute([
                $user['nim'],
                $user['name'],
                $user['email'],
                $user['password'],
                $user['phone'],
                $user['gender'],
                $user['faculty'],
                $user['major'],
                $user['batch'],
                $user['role'],
                $user['status']
            ]);
            echo "   ✅ Created: {$user['email']} ({$user['role']})\n";
        }
    }
    
    // 3. Verify final state
    echo "\n3. ✅ VERIFICATION...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $finalCount = $stmt->fetch()['count'];
    echo "   📊 Total users: $finalCount\n";
    
    $stmt = $pdo->query("SELECT email, role, status FROM users WHERE status = 'active'");
    $activeUsers = $stmt->fetchAll();
    echo "   👥 Active users:\n";
    foreach ($activeUsers as $user) {
        echo "      - {$user['email']} ({$user['role']})\n";
    }
    
    // 4. Check other critical tables
    echo "\n4. 📋 CHECKING OTHER TABLES...\n";
    $tables = ['ukms', 'events', 'event_registrations'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "   ✅ $table: $count records\n";
        } catch (Exception $e) {
            echo "   ❌ $table: ERROR - {$e->getMessage()}\n";
        }
    }
    
    echo "\n🎯 RECOVERY COMPLETE!\n";
    echo "\n🔑 LOGIN CREDENTIALS (password: admin123):\n";
    echo "   Admin: <EMAIL>\n";
    echo "   Student: <EMAIL>\n";
    echo "   Ketua UKM: <EMAIL>\n";
    
} catch (PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n\n";
    
    echo "🔧 TROUBLESHOOTING:\n";
    echo "1. Check MySQL service is running\n";
    echo "2. Verify database 'ukmwebv' exists\n";
    echo "3. Check .env configuration\n";
    echo "4. Run: CREATE DATABASE ukmwebv; (if needed)\n";
    echo "5. Run: php artisan migrate\n";
}
