<?php

echo "=== DEBUGGING REGISTRATION DETAILS 404 ERROR ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking if Event with ID 1 exists...\n";
    
    $event = \App\Models\Event::find(1);
    if ($event) {
        echo "   ✅ Event found: {$event->title}\n";
        echo "   ✅ Event UKM: {$event->ukm->name}\n";
        echo "   ✅ Event ID: {$event->id}\n";
    } else {
        echo "   ❌ Event with ID 1 not found\n";
        
        // Check what events exist
        $events = \App\Models\Event::all();
        echo "   Available events:\n";
        foreach ($events as $evt) {
            echo "     - ID: {$evt->id}, Title: {$evt->title}\n";
        }
        
        if ($events->count() > 0) {
            $event = $events->first();
            echo "   Using first available event: {$event->title} (ID: {$event->id})\n";
        } else {
            echo "   ❌ No events found in database\n";
            exit;
        }
    }
    
    echo "2. Checking if EventRegistration with ID 1 exists...\n";
    
    $registration = \App\Models\EventRegistration::find(1);
    if ($registration) {
        echo "   ✅ Registration found: ID {$registration->id}\n";
        echo "   ✅ Registration User: {$registration->user->name}\n";
        echo "   ✅ Registration Event: {$registration->event->title}\n";
        echo "   ✅ Registration Status: {$registration->status}\n";
    } else {
        echo "   ❌ EventRegistration with ID 1 not found\n";
        
        // Check what registrations exist
        $registrations = \App\Models\EventRegistration::all();
        echo "   Available registrations:\n";
        foreach ($registrations as $reg) {
            echo "     - ID: {$reg->id}, User: {$reg->user->name}, Event: {$reg->event->title}\n";
        }
        
        if ($registrations->count() > 0) {
            $registration = $registrations->first();
            echo "   Using first available registration: ID {$registration->id}\n";
        } else {
            echo "   ❌ No registrations found in database\n";
            exit;
        }
    }
    
    echo "3. Checking if registration belongs to the event...\n";
    
    if ($registration->event_id === $event->id) {
        echo "   ✅ Registration belongs to the event\n";
    } else {
        echo "   ❌ Registration does not belong to the event\n";
        echo "   Registration event ID: {$registration->event_id}\n";
        echo "   Current event ID: {$event->id}\n";
        
        // Find a registration for this event
        $eventRegistration = $event->registrations()->first();
        if ($eventRegistration) {
            echo "   Found registration for this event: ID {$eventRegistration->id}\n";
            $registration = $eventRegistration;
        } else {
            echo "   ❌ No registrations found for this event\n";
            exit;
        }
    }
    
    echo "4. Testing route generation...\n";
    
    try {
        $url = route('ketua-ukm.events.registrations.show', [$event->id, $registration->id]);
        echo "   ✅ Route generated: {$url}\n";
    } catch (Exception $e) {
        echo "   ❌ Route generation failed: " . $e->getMessage() . "\n";
    }
    
    echo "5. Testing controller method directly...\n";
    
    try {
        $controller = new \App\Http\Controllers\KetuaUkmController();
        
        // Check if method exists
        if (method_exists($controller, 'showRegistrationDetails')) {
            echo "   ✅ Method showRegistrationDetails exists\n";
        } else {
            echo "   ❌ Method showRegistrationDetails not found\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Controller test failed: " . $e->getMessage() . "\n";
    }
    
    echo "6. Checking view file...\n";
    
    $viewPath = resource_path('views/ketua-ukm/events/registration-details.blade.php');
    if (file_exists($viewPath)) {
        echo "   ✅ View file exists: {$viewPath}\n";
    } else {
        echo "   ❌ View file not found: {$viewPath}\n";
    }
    
    echo "7. Checking user permissions...\n";
    
    // Check if there's a ketua UKM user
    $ketuaUkm = \App\Models\User::where('role', 'ketua_ukm')->first();
    if ($ketuaUkm) {
        echo "   ✅ Ketua UKM user found: {$ketuaUkm->name}\n";
        
        // Check if this ketua UKM leads the event's UKM
        if ($event->ukm->leader_id === $ketuaUkm->id) {
            echo "   ✅ Ketua UKM leads the event's UKM\n";
        } else {
            echo "   ❌ Ketua UKM does not lead the event's UKM\n";
            echo "   Event UKM leader ID: {$event->ukm->leader_id}\n";
            echo "   Ketua UKM ID: {$ketuaUkm->id}\n";
        }
    } else {
        echo "   ❌ No ketua UKM user found\n";
    }
    
    echo "8. Testing URL patterns...\n";
    
    // Test different URL patterns
    $testUrls = [
        "http://localhost:8000/ketua-ukm/events/{$event->id}/registrations/{$registration->id}",
        route('ketua-ukm.events.registrations.show', [$event, $registration]),
        route('ketua-ukm.events.registrations.show', ['event' => $event->id, 'registration' => $registration->id]),
    ];
    
    foreach ($testUrls as $url) {
        echo "   Testing URL: {$url}\n";
    }
    
    echo "\n=== DEBUGGING COMPLETED ===\n";
    echo "Suggested fixes:\n";
    echo "1. Make sure you're logged in as a ketua UKM user\n";
    echo "2. Make sure the ketua UKM user leads the UKM that owns the event\n";
    echo "3. Make sure the registration ID exists and belongs to the event\n";
    echo "4. Check browser console for any JavaScript errors\n";
    echo "5. Check Laravel logs for any server errors\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
