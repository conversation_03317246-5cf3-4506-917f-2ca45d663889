<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Event;

echo "=== TESTING EVENT EDIT FORM ===\n";

$event = Event::first();
if ($event) {
    echo "Event: {$event->title}\n";
    echo "Requires approval: " . ($event->requires_approval ? 'Yes' : 'No') . "\n";
    echo "Edit URL: http://localhost:8000/ketua-ukm/events/{$event->slug}/edit\n";
    
    // Update the event to test approval
    $event->update(['requires_approval' => true]);
    echo "✅ Updated event to require approval\n";
    echo "Now visit the edit URL to see the checkbox\n";
} else {
    echo "No events found\n";
}

?>
