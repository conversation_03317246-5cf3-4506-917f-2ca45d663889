<?php

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== FIXING ROLE ENUM ===\n";

try {
    echo "1. Checking current enum...\n";
    $result = DB::select("SHOW COLUMNS FROM users LIKE 'role'");
    if (!empty($result)) {
        echo "   Current: " . $result[0]->Type . "\n";
    }
    
    echo "2. Updating enum to support ketua_ukm...\n";
    DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('student', 'ketua_ukm', 'admin') DEFAULT 'student'");
    echo "   ✅ Enum updated!\n";
    
    echo "3. Verifying change...\n";
    $result = DB::select("SHOW COLUMNS FROM users LIKE 'role'");
    if (!empty($result)) {
        echo "   New: " . $result[0]->Type . "\n";
    }
    
    echo "4. Testing role assignment...\n";
    $user = \App\Models\User::where('role', 'student')->first();
    if ($user) {
        echo "   Testing with: " . $user->name . "\n";
        $user->update(['role' => 'ketua_ukm']);
        $user->refresh();
        echo "   New role: " . $user->role . "\n";
        
        if ($user->role === 'ketua_ukm') {
            echo "   ✅ SUCCESS!\n";
            $user->update(['role' => 'student']);
            echo "   Reverted to student\n";
        } else {
            echo "   ❌ FAILED!\n";
        }
    }
    
    echo "\n=== COMPLETED ===\n";
    echo "Role enum now supports: student, ketua_ukm, admin\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
