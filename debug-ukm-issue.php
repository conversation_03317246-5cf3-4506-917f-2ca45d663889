<?php

echo "=== DEBUGGING UKM SISTEM INFORMASI ISSUE ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ukmwebv', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected\n\n";
    
    // 1. Check UKM Sistem Informasi
    echo "📋 CHECKING UKM SISTEM INFORMASI:\n";
    $stmt = $pdo->query("SELECT * FROM ukms WHERE name LIKE '%sistem%' OR name LIKE '%informasi%'");
    $ukms = $stmt->fetchAll();
    
    foreach ($ukms as $ukm) {
        echo "- UKM ID: {$ukm['id']}, Name: {$ukm['name']}, Leader ID: {$ukm['leader_id']}\n";
        echo "  Status: {$ukm['status']}, Current Members: {$ukm['current_members']}\n";
        
        // Check if leader exists
        if ($ukm['leader_id']) {
            $stmt = $pdo->prepare("SELECT id, name, email FROM users WHERE id = ?");
            $stmt->execute([$ukm['leader_id']]);
            $leader = $stmt->fetch();
            
            if ($leader) {
                echo "  Leader: {$leader['name']} ({$leader['email']})\n";
            } else {
                echo "  ❌ LEADER NOT FOUND! Leader ID {$ukm['leader_id']} doesn't exist\n";
            }
        } else {
            echo "  ⚠️  No leader assigned\n";
        }
    }
    
    // 2. Check UKM Members for Sistem Informasi
    echo "\n📋 CHECKING UKM MEMBERS:\n";
    $stmt = $pdo->query("
        SELECT um.*, u.name, u.email, u.id as user_id
        FROM ukm_members um
        LEFT JOIN users u ON um.user_id = u.id
        WHERE um.ukm_id IN (SELECT id FROM ukms WHERE name LIKE '%sistem%' OR name LIKE '%informasi%')
        ORDER BY um.status, um.created_at
    ");
    $members = $stmt->fetchAll();
    
    foreach ($members as $member) {
        echo "- Member: ";
        if ($member['name']) {
            echo "{$member['name']} ({$member['email']})";
        } else {
            echo "❌ USER NOT FOUND (ID: {$member['user_id']})";
        }
        echo " - Status: {$member['status']}\n";
        
        if (!$member['name']) {
            echo "  🔧 ORPHANED RECORD: ukm_members.id = {$member['id']}\n";
        }
    }
    
    // 3. Check for orphaned records
    echo "\n🔍 CHECKING FOR ORPHANED RECORDS:\n";
    $stmt = $pdo->query("
        SELECT um.id, um.user_id, um.ukm_id, um.status
        FROM ukm_members um
        LEFT JOIN users u ON um.user_id = u.id
        WHERE u.id IS NULL
    ");
    $orphaned = $stmt->fetchAll();
    
    if (count($orphaned) > 0) {
        echo "❌ Found " . count($orphaned) . " orphaned ukm_members records:\n";
        foreach ($orphaned as $record) {
            echo "  - ukm_members.id: {$record['id']}, user_id: {$record['user_id']}, ukm_id: {$record['ukm_id']}, status: {$record['status']}\n";
        }
    } else {
        echo "✅ No orphaned ukm_members records found\n";
    }
    
    // 4. Check for orphaned leader references
    echo "\n🔍 CHECKING FOR ORPHANED LEADER REFERENCES:\n";
    $stmt = $pdo->query("
        SELECT ukm.id, ukm.name, ukm.leader_id
        FROM ukms ukm
        LEFT JOIN users u ON ukm.leader_id = u.id
        WHERE ukm.leader_id IS NOT NULL AND u.id IS NULL
    ");
    $orphanedLeaders = $stmt->fetchAll();
    
    if (count($orphanedLeaders) > 0) {
        echo "❌ Found " . count($orphanedLeaders) . " UKMs with orphaned leader references:\n";
        foreach ($orphanedLeaders as $record) {
            echo "  - UKM: {$record['name']} (ID: {$record['id']}) - Leader ID: {$record['leader_id']} (doesn't exist)\n";
        }
    } else {
        echo "✅ No orphaned leader references found\n";
    }
    
    // 5. Check current_members count accuracy
    echo "\n📊 CHECKING CURRENT_MEMBERS COUNT ACCURACY:\n";
    $stmt = $pdo->query("
        SELECT 
            ukm.id,
            ukm.name,
            ukm.current_members as stored_count,
            COUNT(um.id) as actual_count
        FROM ukms ukm
        LEFT JOIN ukm_members um ON ukm.id = um.ukm_id AND um.status = 'active'
        WHERE ukm.name LIKE '%sistem%' OR ukm.name LIKE '%informasi%'
        GROUP BY ukm.id, ukm.name, ukm.current_members
    ");
    $counts = $stmt->fetchAll();
    
    foreach ($counts as $count) {
        echo "- UKM: {$count['name']}\n";
        echo "  Stored count: {$count['stored_count']}, Actual count: {$count['actual_count']}\n";
        
        if ($count['stored_count'] != $count['actual_count']) {
            echo "  ❌ MISMATCH! Will fix...\n";
            
            $stmt = $pdo->prepare("UPDATE ukms SET current_members = ? WHERE id = ?");
            $stmt->execute([$count['actual_count'], $count['id']]);
            echo "  ✅ Fixed: Updated current_members to {$count['actual_count']}\n";
        } else {
            echo "  ✅ Count is accurate\n";
        }
    }
    
    // 6. Clean up orphaned records
    if (count($orphaned) > 0) {
        echo "\n🧹 CLEANING UP ORPHANED RECORDS:\n";
        
        foreach ($orphaned as $record) {
            $stmt = $pdo->prepare("DELETE FROM ukm_members WHERE id = ?");
            $stmt->execute([$record['id']]);
            echo "✅ Deleted orphaned record: ukm_members.id = {$record['id']}\n";
        }
    }
    
    // 7. Fix orphaned leader references
    if (count($orphanedLeaders) > 0) {
        echo "\n🧹 FIXING ORPHANED LEADER REFERENCES:\n";
        
        foreach ($orphanedLeaders as $record) {
            $stmt = $pdo->prepare("UPDATE ukms SET leader_id = NULL WHERE id = ?");
            $stmt->execute([$record['id']]);
            echo "✅ Cleared orphaned leader reference for UKM: {$record['name']}\n";
        }
    }
    
    echo "\n✅ Debug and cleanup complete!\n";
    echo "🎯 Try accessing UKM Sistem Informasi again - the error should be fixed.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
