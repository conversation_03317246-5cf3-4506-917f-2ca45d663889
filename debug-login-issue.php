<?php

echo "=== DEBUG LOGIN ISSUE ===\n";

// Test database connection directly
$host = '127.0.0.1';
$dbname = 'ukmwebv';
$username = 'root';
$password = '';

try {
    echo "1. Testing direct database connection...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ Direct DB connection: SUCCESS\n";
    
    // Check users table
    echo "2. Checking users table...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "   Total users: {$result['count']}\n";
    
    if ($result['count'] > 0) {
        echo "3. Listing all users...\n";
        $stmt = $pdo->query("SELECT id, nim, name, email, role, status, password FROM users");
        while ($user = $stmt->fetch()) {
            echo "   ID: {$user['id']}\n";
            echo "   NIM: {$user['nim']}\n";
            echo "   Name: {$user['name']}\n";
            echo "   Email: {$user['email']}\n";
            echo "   Role: {$user['role']}\n";
            echo "   Status: {$user['status']}\n";
            echo "   Password Hash: " . substr($user['password'], 0, 20) . "...\n";
            
            // Test password verification
            if (password_verify('admin123', $user['password'])) {
                echo "   ✅ Password 'admin123': CORRECT\n";
            } elseif (password_verify('student123', $user['password'])) {
                echo "   ✅ Password 'student123': CORRECT\n";
            } else {
                echo "   ❌ Password verification: FAILED\n";
                
                // Try to update with correct hash
                $newHash = password_hash('admin123', PASSWORD_DEFAULT);
                $updateStmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                $updateStmt->execute([$newHash, $user['id']]);
                echo "   🔧 Password updated with new hash\n";
            }
            echo "   ---\n";
        }
    } else {
        echo "   ❌ No users found! Creating admin user...\n";
        
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (
                nim, name, email, password, phone, gender, faculty, major, batch,
                role, status, email_verified_at, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
            )
        ");
        
        $stmt->execute([
            'ADMIN001',
            'Administrator',
            '<EMAIL>',
            $hashedPassword,
            '081234567890',
            'male',
            'Administrasi',
            'Sistem Informasi',
            '2024',
            'admin',
            'active',
            date('Y-m-d H:i:s')
        ]);
        
        echo "   ✅ Admin user created\n";
    }
    
    echo "4. Testing specific login credentials...\n";
    
    // Test admin login
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "   ✅ Admin user found\n";
        echo "   Email: {$admin['email']}\n";
        echo "   Status: {$admin['status']}\n";
        echo "   Role: {$admin['role']}\n";
        
        if (password_verify('admin123', $admin['password'])) {
            echo "   ✅ Admin password verification: SUCCESS\n";
        } else {
            echo "   ❌ Admin password verification: FAILED\n";
            echo "   Current hash: {$admin['password']}\n";
            
            // Update with correct hash
            $correctHash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
            $stmt->execute([$correctHash, '<EMAIL>']);
            echo "   🔧 Admin password updated\n";
        }
    } else {
        echo "   ❌ Admin user not found\n";
    }
    
    // Test student login
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $student = $stmt->fetch();
    
    if ($student) {
        echo "   ✅ Student user found\n";
        if (password_verify('student123', $student['password'])) {
            echo "   ✅ Student password verification: SUCCESS\n";
        } else {
            echo "   ❌ Student password verification: FAILED\n";
        }
    } else {
        echo "   ❌ Student user not found\n";
    }
    
    echo "5. Creating fresh users with correct passwords...\n";
    
    // Delete existing users and create fresh ones
    $pdo->exec("DELETE FROM users");
    echo "   🗑️  All users deleted\n";
    
    // Create admin with fresh hash
    $adminHash = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (
            nim, name, email, password, phone, gender, faculty, major, batch,
            role, status, email_verified_at, created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
        )
    ");
    
    $stmt->execute([
        'ADMIN001',
        'Administrator',
        '<EMAIL>',
        $adminHash,
        '081234567890',
        'male',
        'Administrasi',
        'Sistem Informasi',
        '2024',
        'admin',
        'active',
        date('Y-m-d H:i:s')
    ]);
    
    echo "   ✅ Fresh admin created\n";
    
    // Create student with fresh hash
    $studentHash = password_hash('student123', PASSWORD_DEFAULT);
    $stmt->execute([
        '1301210001',
        'Test Student',
        '<EMAIL>',
        $studentHash,
        '081234567891',
        'male',
        'Informatika',
        'Sistem Informasi',
        '2021',
        'student',
        'active',
        date('Y-m-d H:i:s')
    ]);
    
    echo "   ✅ Fresh student created\n";
    
    echo "6. Final verification...\n";
    
    // Verify admin
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        echo "   ✅ Admin login ready: <EMAIL> / admin123\n";
    } else {
        echo "   ❌ Admin login still not working\n";
    }
    
    // Verify student
    $stmt->execute(['<EMAIL>']);
    $student = $stmt->fetch();
    
    if ($student && password_verify('student123', $student['password'])) {
        echo "   ✅ Student login ready: <EMAIL> / student123\n";
    } else {
        echo "   ❌ Student login still not working\n";
    }
    
    echo "\n7. Database summary...\n";
    $stmt = $pdo->query("SELECT id, nim, name, email, role, status FROM users ORDER BY id");
    while ($user = $stmt->fetch()) {
        echo "   {$user['id']}. {$user['name']} ({$user['email']}) - {$user['role']} - {$user['status']}\n";
    }
    
    echo "\n=== TESTING COMPLETED ===\n";
    echo "🎯 Try logging in now with:\n";
    echo "📧 Admin: <EMAIL> | Password: admin123\n";
    echo "📧 Student: <EMAIL> | Password: student123\n";
    echo "🌐 URL: http://localhost:8000/login\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    echo "\n🔧 TROUBLESHOOTING:\n";
    echo "1. Make sure XAMPP MySQL is running\n";
    echo "2. Check database 'ukmwebv' exists\n";
    echo "3. Check .env file settings\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
