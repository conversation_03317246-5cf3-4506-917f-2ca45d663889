<?php

echo "=== DEBUGGING ROLE PERSISTENCE ISSUE ===\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "1. Checking users with ketua_ukm role...\n";
    
    $ketuaUkmUsers = \App\Models\User::where('role', 'ketua_ukm')->get();
    
    if ($ketuaUkmUsers->count() > 0) {
        echo "   Found {$ketuaUkmUsers->count()} users with ketua_ukm role:\n";
        foreach ($ketuaUkmUsers as $user) {
            echo "      - {$user->name} ({$user->email})\n";
            echo "        Role: {$user->role}\n";
            echo "        Status: {$user->status}\n";
            echo "        Created: {$user->created_at}\n";
            echo "        Updated: {$user->updated_at}\n";
            echo "\n";
        }
    } else {
        echo "   No users found with ketua_ukm role\n";
    }
    
    echo "2. Checking for any role inconsistencies...\n";
    
    $allUsers = \App\Models\User::all();
    $validRoles = ['student', 'ketua_ukm', 'admin'];
    
    foreach ($allUsers as $user) {
        if (!in_array($user->role, $validRoles)) {
            echo "   ❌ Invalid role found: {$user->name} has role '{$user->role}'\n";
        }
    }
    
    echo "3. Testing role assignment...\n";
    
    // Find a test user
    $testUser = \App\Models\User::where('role', 'student')->first();
    
    if ($testUser) {
        echo "   Testing with user: {$testUser->name} ({$testUser->email})\n";
        echo "   Current role: {$testUser->role}\n";
        
        // Change to ketua_ukm
        $testUser->update(['role' => 'ketua_ukm']);
        $testUser->refresh();
        
        echo "   After update to ketua_ukm: {$testUser->role}\n";
        
        // Change back to student
        $testUser->update(['role' => 'student']);
        $testUser->refresh();
        
        echo "   After update back to student: {$testUser->role}\n";
        
        echo "   ✅ Role assignment test passed\n";
    } else {
        echo "   No student users found for testing\n";
    }
    
    echo "4. Checking database schema...\n";
    
    $columns = \Illuminate\Support\Facades\Schema::getColumnListing('users');
    
    if (in_array('role', $columns)) {
        echo "   ✅ 'role' column exists in users table\n";
        
        // Check column type
        $connection = \Illuminate\Support\Facades\DB::connection();
        $columnType = $connection->getDoctrineColumn('users', 'role')->getType()->getName();
        echo "   Column type: {$columnType}\n";
        
    } else {
        echo "   ❌ 'role' column missing from users table\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== DEBUG COMPLETED ===\n";
echo "\nPossible causes of role reset:\n";
echo "1. Database transaction rollback\n";
echo "2. Middleware interference\n";
echo "3. Session data corruption\n";
echo "4. Cache issues\n";
echo "5. Multiple database connections\n";
echo "\nSolutions:\n";
echo "1. Clear all caches: php artisan cache:clear\n";
echo "2. Check database connection\n";
echo "3. Verify role assignment in admin panel\n";
echo "4. Test with fresh browser session\n";
