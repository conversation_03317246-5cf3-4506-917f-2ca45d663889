<?php
/**
 * Test achievements relationship specifically
 */

echo "=== TESTING ACHIEVEMENTS RELATIONSHIP ===\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    $ukm = \App\Models\Ukm::where('slug', 'imma')->first();
    
    if (!$ukm) {
        echo "❌ UKM not found\n";
        exit;
    }
    
    echo "✅ Found UKM: {$ukm->name} (ID: {$ukm->id})\n\n";
    
    echo "1. Testing achievements() method (relationship query):\n";
    try {
        $achievementsQuery = $ukm->achievements();
        echo "   ✅ Achievements query object created\n";
        echo "   📋 Query class: " . get_class($achievementsQuery) . "\n";
        
        $achievementsCollection = $achievementsQuery->get();
        echo "   ✅ Achievements collection retrieved\n";
        echo "   📊 Count: " . $achievementsCollection->count() . "\n";
        
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n2. Testing achievements property (dynamic property):\n";
    try {
        $achievements = $ukm->achievements;
        if ($achievements === null) {
            echo "   ⚠️ Achievements property is null\n";
        } else {
            echo "   ✅ Achievements property type: " . get_class($achievements) . "\n";
            echo "   📊 Count: " . $achievements->count() . "\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n3. Testing with load() method:\n";
    try {
        $ukm->load('achievements');
        echo "   ✅ Achievements loaded via load() method\n";
        
        $achievements = $ukm->achievements;
        if ($achievements === null) {
            echo "   ⚠️ Still null after load()\n";
        } else {
            echo "   ✅ Type after load: " . get_class($achievements) . "\n";
            echo "   📊 Count after load: " . $achievements->count() . "\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n4. Testing direct database query:\n";
    try {
        $directAchievements = \App\Models\UkmAchievement::where('ukm_id', $ukm->id)->get();
        echo "   ✅ Direct query successful\n";
        echo "   📊 Direct count: " . $directAchievements->count() . "\n";
        
        if ($directAchievements->count() > 0) {
            echo "   📋 Sample achievement: " . $directAchievements->first()->title . "\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Direct query error: " . $e->getMessage() . "\n";
    }
    
    echo "\n5. Testing relationship definition:\n";
    try {
        $reflection = new ReflectionClass($ukm);
        $method = $reflection->getMethod('achievements');
        echo "   ✅ achievements() method exists\n";
        
        // Call the method to see what it returns
        $relationshipObject = $method->invoke($ukm);
        echo "   ✅ Relationship object type: " . get_class($relationshipObject) . "\n";
        
    } catch (Exception $e) {
        echo "   ❌ Reflection error: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== TEST COMPLETED ===\n";
    
} catch (\Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    echo "📋 Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
