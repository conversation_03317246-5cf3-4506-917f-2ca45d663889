<?php

echo "=== FIXING ROLE ENUM DIRECTLY ===\n";

// Create SQL file
$sql = "ALTER TABLE users MODIFY COLUMN role ENUM('student', 'ketua_ukm', 'admin') DEFAULT 'student';";
file_put_contents('fix_role.sql', $sql);

echo "1. Created SQL file\n";

// Execute MySQL command
$command = 'mysql -u root ukm_web_db < fix_role.sql 2>&1';
$output = shell_exec($command);

if ($output === null || trim($output) === '') {
    echo "2. ✅ SQL executed successfully!\n";
} else {
    echo "2. SQL output: " . $output . "\n";
}

// Clean up
unlink('fix_role.sql');

echo "3. Cleaned up temporary files\n";

// Test with Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "4. Testing role assignment...\n";
    
    $user = \App\Models\User::where('role', 'student')->first();
    if ($user) {
        echo "   Testing with: " . $user->name . "\n";
        $user->update(['role' => 'ketua_ukm']);
        $user->refresh();
        echo "   New role: " . $user->role . "\n";
        
        if ($user->role === 'ketua_ukm') {
            echo "   ✅ SUCCESS!\n";
            $user->update(['role' => 'student']);
            echo "   Reverted to student\n";
        } else {
            echo "   ❌ FAILED!\n";
        }
    }
    
} catch (Exception $e) {
    echo "Laravel test error: " . $e->getMessage() . "\n";
}

echo "\n=== COMPLETED ===\n";
echo "Role enum should now support: student, ketua_ukm, admin\n";
